{"__meta": {"id": "Xad163bf6ee4909f4eea62d1be9ee49dc", "datetime": "2025-07-31 04:54:29", "utime": **********.228693, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[04:54:29] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 79,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.224733, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753937668.138059, "end": **********.228734, "duration": 1.0906751155853271, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1753937668.138059, "relative_start": 0, "end": **********.05933, "relative_end": **********.05933, "duration": 0.9212710857391357, "duration_str": "921ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.059343, "relative_start": 0.9212841987609863, "end": **********.228739, "relative_end": 5.0067901611328125e-06, "duration": 0.16939592361450195, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48365088, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.012199999999999999, "accumulated_duration_str": "12.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.112494, "duration": 0.00457, "duration_str": "4.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 37.459}, {"sql": "select * from `settings` where `created_by` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.146709, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 37.459, "width_percent": 8.934}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.152421, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 46.393, "width_percent": 4.59}, {"sql": "select * from `ch_favorites` where `user_id` = 79", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.155787, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 50.984, "width_percent": 4.016}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.170965, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "radhe_same", "start_percent": 55, "width_percent": 4.672}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (79) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.179334, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 59.672, "width_percent": 9.344}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (79) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.184526, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 69.016, "width_percent": 6.311}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.189555, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "radhe_same", "start_percent": 75.328, "width_percent": 24.672}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 547, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/settings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-694956925 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-694956925\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2000912073 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2000912073\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-218990599 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-218990599\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1762412761 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im5XYXUycWdNa2xZVm5SRTgyQzZJRHc9PSIsInZhbHVlIjoiVzRmUG1vQlJwMjZEcXM5VUhzU1NyTzUwWDZJVHV3TXRLVTBkdHJ4ZlIvejFXZnVLTEtFbGdZSDF2RllUNkVobFo3eFRHT25xS0s0TGNTakUxOUMvdnYwRVltN05xaEtDMk9DbWZSWUVrd2Z0SWNZTmVnV1pTb0p6UHpZNTNTNDdEM3R1MENwSjBHcllVa0pJOEtRQUdNZDFZR0djdW9rdmp3cDlMT0dTcE9HTnMxYk0rY2xPSEVkMzhLUENGbkNqSzBqNi9EVmhwWExHSFlMZWVaU2V5d3VQY0F6dXptL3lUMUtQNVNGVDFqTWhxSU5PcS9yZ3dZMnhXeC96OWdTVGlhejI2a1RmdTBUYnNkcEJoOE0xL3VaRnRON3V0eUxWNDNBemp6dUUzcnNKc1hoOVZOMFUvaEY2QUcvSmxua1hTd0NyWlo3eFprd3R1dkRDaENWSTl1bHhrSDNQeWlGbHlFVGFNS0t1UmZqSnVnSzZlK1laMnF0WmxiaVViUGxLVFI2ME5nMW55RTdHTnVhRjRzK1hBK1czVXZIb1pONlFjZW4vY3h2b2ZQWnRJMnpjM3M5RWxsTEtGdG0yRU5uSVQyejZ2T0UreCt6SHh3aWpEQnpPQzA5YkViaUxpRlZENDBXVTN1SkdXd3IvMXdPVU9hRngzV0RiMGNXNFhZcEYiLCJtYWMiOiJkZjUxMjBlYjNjZDJlZjY3YWM2OGNlNGU1NjQ2MDEwZTE2MDI0YTI0NGJhYjZlZGZjMTdlZmU3NmZjMDFiMjdmIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InFpV212M3g3UWNBaGt2a2Y0YUlKMEE9PSIsInZhbHVlIjoiNEtDbDdJK3l2THFubU15QWhsdW1KbXBUS0w0bjJ6U2gzSWJxRjdFZmdVRzgzMDliRkdWQmVkS2ZvaXExLzR5amp0dTh5MlZUckszRXFkb29SbjhVU1pYRkEvZ0RIYXBhNDFLMGNqdkRUT2JFTUswN0JEZ1pyVmRuakJQTE8zK1I2QUo1Nm9ZclpqKzhUVkw4emV6dVF5VmMyNmcwTC9rc2ZRcXNEK3NaV21ZNHVyRmdTM3J3V1MwYVdpRVlITzBDcVFEdTlXT2ZTNzJ2WEJvUE0xWXNMVUpEOExrMzdVUm1mVUhIV3pkQWpNK3RzNjN3b1NlbjhmVzEyakp0VmNVRG12Zk5WODgwZnk4M0RIdDJ4UkowT25wQlVnbFQybEl2MVBPMG83Rktuc1QxSHRvSWNwZVpZaElnV3BhenAzM0NnMmpnZTVoYXpDUDVGTTAzQ0dZaHQvend4Z2N4MUR6NEwvSDdFWnhiOG5NNzhiaFJaV1lPOTFGcmRtQVhnaUN3ZU1qb244UE1JMDBGVDBRZzJBYnJIRWh6MlRnYTY2Qmpoc2Mva3E3QitZUzNyYlFlUGxvcEpoNUlhZnlWR1BQV1pLd2JaWUNHUkZoeXdBdWxZMGRqbjlYVmp5dDZ0dU00bkVhcTFHdHZIVUU5cU5zdlJMVk5VeGhDREhrbjZkaEkiLCJtYWMiOiJiN2Y5ZDQzYjU2OGJlMDM1YTVjNjg2ZWZlZmRhMDczMWE5MzRlNjE5Y2VhMTA2MmUyZjUzZDc3ZGM3NjIxZTc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1762412761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1957939182 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5CMjY5xZzCpzyhz20hjbq5xMLnYwYBAUF3viINCI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957939182\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-743953918 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:54:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZ1Z3ZkSi9ydXEweDloWlVzS2k5SFE9PSIsInZhbHVlIjoiNGlEd0RSL1Baa1lBZkdkd1pmWEtuNDlORTlGUXJ3SFBWZDV1R0FBWVVkaHJlSGE2Z0ZSR09ybEh6VmRuNHFqckhPQ1p6Y0E5KzRRcHR5VTdoV1FhQTZKaXREREhiQ3pxUldRRDh2NHVoeVgrQXI2dnNnVUMwbGRYYmlVUFNiVXpWQmVKTkJUQjRFWU5uakIwLzc1TWxkM0dQaEVBUnBpWkRxSFBFYld2WWV0SCtqMVB3VW03WWlCMFJ1WDhDcEpYeHNEdEpqV3BUNzVtU2VDT05VV3JxNFRVY0FXQ29jaTloYVpXQTFMdSsxMHZrSFZWeEdWSmFSUEZPSU0rOGlBTjJleFVUbmxoUkJBWFB2Si8xZFFvVzNEVWRya0lsQ0I5dU5DY0lhblJJaklMNVp3dzdUL0Q4d3p5ZlJRVHo0WGFzZmNrZG0xenVTVTVnUk5PNGExd1U3Ym9TbTN1M1pic2ZwQzJyUHFqNlpoeGFHdjJUTHNCNkZldnkwMFRSeTFna0RFUlU2V2pYeWFuVXlkdU5MUDBraTM5TEtMNzFpeWRPb05GZko3N2pJV00rUmd0cElFSXhwVnhQeXg0WjlRNmhiQk1UTzVWZlFUam1udVl4ZVhiS0Fpb25YekdTY01qRE4zRW93Zjd5Q0RNMFJUZ0VncWV5Y1FWV1ZDeVdENG4iLCJtYWMiOiI3YzgzMmNlN2YzNzg5Y2QzMTdiMGQ3OTBlNjMxMzFkYWFmYzdkOTE5NDJkMmE5Y2E0YWM0MmZlYTk3NWI1MWVmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IktmZU9OMXdia3EzYTM3V0tvenJJMmc9PSIsInZhbHVlIjoiekVBTnp5eVp0Sk9CT3VLVDdBMzVZSWVEdU9aaWFLRExrVlJseUlINWlUbk1JNmJKVDVUYjFsSmJnTWwxZUc3dHRVeCsyNWhYUXdQakl6NVhCVnIwZCtHc0ZYTkRJM0pNYVBSU2k4aTFvNThNK1lzTDR1MFNqVkNkVjE0NlZCUWdmbUVpcnV4d1BkTDRWWCtsVlhwTGtiS1I3STRaYnhhQkZiM05pQkVienZoNTB6M2p3RkRDOVl5QThzU0VSS0JELzZDWnhRT0YwQ1BINVd4MFZPNEdocTZ1QmY5VWN3UC9QL1FQQ01kcWhZSHRRVWVhWTZZanlqZzhUU2dhemh5aHZFMFRuRERpNm9VZXpIRHhmc1VQN3AvaFIrZE1WL1hlQm9SdkxBdkthb3pSSFE1ZGIvT2U2aWZka0pOSFM0b2xtYjBFWkVpczVWT0ZQTU5xZ2dVLytMWjd1WDZoemIxNzM4dC9kWnpDK2NSa2JXM2RJY05WNXkzNE5LN2MyVHRYbkpkSHphNDY3RHNaeFFXK1kwTE9jWE9YTjM5N0pwQmVrMXRqNFNsaVBCTk96WlR2TExVaGlONXhHTmFFcDh1eldYZWxDRm1kdldaSFd6bktldkJEOXpoZE82eXdKUjVqQno4cFBqTStaUHR5eCtGbWpXQ0NZdFlKVlRyenE0T1UiLCJtYWMiOiI4MjY0NTJjZmE3Y2M3NjdkZTU4MDg5YzM1NDYzZDNjNmMzYWRiMGQxZTAyYWU4OGU0MzVlMmEzZTdiNjc5MDE1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZ1Z3ZkSi9ydXEweDloWlVzS2k5SFE9PSIsInZhbHVlIjoiNGlEd0RSL1Baa1lBZkdkd1pmWEtuNDlORTlGUXJ3SFBWZDV1R0FBWVVkaHJlSGE2Z0ZSR09ybEh6VmRuNHFqckhPQ1p6Y0E5KzRRcHR5VTdoV1FhQTZKaXREREhiQ3pxUldRRDh2NHVoeVgrQXI2dnNnVUMwbGRYYmlVUFNiVXpWQmVKTkJUQjRFWU5uakIwLzc1TWxkM0dQaEVBUnBpWkRxSFBFYld2WWV0SCtqMVB3VW03WWlCMFJ1WDhDcEpYeHNEdEpqV3BUNzVtU2VDT05VV3JxNFRVY0FXQ29jaTloYVpXQTFMdSsxMHZrSFZWeEdWSmFSUEZPSU0rOGlBTjJleFVUbmxoUkJBWFB2Si8xZFFvVzNEVWRya0lsQ0I5dU5DY0lhblJJaklMNVp3dzdUL0Q4d3p5ZlJRVHo0WGFzZmNrZG0xenVTVTVnUk5PNGExd1U3Ym9TbTN1M1pic2ZwQzJyUHFqNlpoeGFHdjJUTHNCNkZldnkwMFRSeTFna0RFUlU2V2pYeWFuVXlkdU5MUDBraTM5TEtMNzFpeWRPb05GZko3N2pJV00rUmd0cElFSXhwVnhQeXg0WjlRNmhiQk1UTzVWZlFUam1udVl4ZVhiS0Fpb25YekdTY01qRE4zRW93Zjd5Q0RNMFJUZ0VncWV5Y1FWV1ZDeVdENG4iLCJtYWMiOiI3YzgzMmNlN2YzNzg5Y2QzMTdiMGQ3OTBlNjMxMzFkYWFmYzdkOTE5NDJkMmE5Y2E0YWM0MmZlYTk3NWI1MWVmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IktmZU9OMXdia3EzYTM3V0tvenJJMmc9PSIsInZhbHVlIjoiekVBTnp5eVp0Sk9CT3VLVDdBMzVZSWVEdU9aaWFLRExrVlJseUlINWlUbk1JNmJKVDVUYjFsSmJnTWwxZUc3dHRVeCsyNWhYUXdQakl6NVhCVnIwZCtHc0ZYTkRJM0pNYVBSU2k4aTFvNThNK1lzTDR1MFNqVkNkVjE0NlZCUWdmbUVpcnV4d1BkTDRWWCtsVlhwTGtiS1I3STRaYnhhQkZiM05pQkVienZoNTB6M2p3RkRDOVl5QThzU0VSS0JELzZDWnhRT0YwQ1BINVd4MFZPNEdocTZ1QmY5VWN3UC9QL1FQQ01kcWhZSHRRVWVhWTZZanlqZzhUU2dhemh5aHZFMFRuRERpNm9VZXpIRHhmc1VQN3AvaFIrZE1WL1hlQm9SdkxBdkthb3pSSFE1ZGIvT2U2aWZka0pOSFM0b2xtYjBFWkVpczVWT0ZQTU5xZ2dVLytMWjd1WDZoemIxNzM4dC9kWnpDK2NSa2JXM2RJY05WNXkzNE5LN2MyVHRYbkpkSHphNDY3RHNaeFFXK1kwTE9jWE9YTjM5N0pwQmVrMXRqNFNsaVBCTk96WlR2TExVaGlONXhHTmFFcDh1eldYZWxDRm1kdldaSFd6bktldkJEOXpoZE82eXdKUjVqQno4cFBqTStaUHR5eCtGbWpXQ0NZdFlKVlRyenE0T1UiLCJtYWMiOiI4MjY0NTJjZmE3Y2M3NjdkZTU4MDg5YzM1NDYzZDNjNmMzYWRiMGQxZTAyYWU4OGU0MzVlMmEzZTdiNjc5MDE1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743953918\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-872828733 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872828733\", {\"maxDepth\":0})</script>\n"}}