{"__meta": {"id": "X1e320456b4962cd7ad62cf0227ca285a", "datetime": "2025-07-31 04:43:41", "utime": **********.055028, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[04:43:41] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.05029, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.12644, "end": **********.05506, "duration": 0.9286198616027832, "duration_str": "929ms", "measures": [{"label": "Booting", "start": **********.12644, "relative_start": 0, "end": **********.920702, "relative_end": **********.920702, "duration": 0.7942619323730469, "duration_str": "794ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.920725, "relative_start": 0.7942850589752197, "end": **********.055063, "relative_end": 3.0994415283203125e-06, "duration": 0.1343379020690918, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45913344, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00559, "accumulated_duration_str": "5.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9844291, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 65.116}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.003824, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 65.116, "width_percent": 13.417}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.027678, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 78.533, "width_percent": 11.27}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0320718, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 89.803, "width_percent": 10.197}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-499292780 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-499292780\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1357619171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1357619171\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1066841016 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066841016\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-841526645 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IisyVXFMbU9SQVdWVjltMzlHMVpLTXc9PSIsInZhbHVlIjoiM3NjMmZHdjA0MjZ0b2VUM1U4WWZyZllrUDBPMEQ1a0k5SVJVUy9YajljU0FPUTZFdXdsNU9CTTlwN1ZZYitnTG9qY0JwSjZhdlBka2g3cFpERzlEeCtjc2FBVFJ6Slovb0JyMmRKNVd0TkdEY0tiQjNXelVKblhtWWd0bURycktmZnJwL0FURlVubnZlZUpIWG1obHM3djFyVTJCbG12RWdiQm0xRlo1SElSV0tFVERMb3VWcytJNnNqZXZLcU9EUzhrTHFVRVUvNnBySUZJQUN5MHhSR3Vna2dOZXRlUDNYZG5ENTMwTkwzenZMRlJJVzBPZW1adi93ZTQ5RDN2Y08rQjcvcVpmeXRrNHJMRjZFVURnaDN1U3RGT0k4MkZ1cjR2Nk1XQW1TN0VpSExaTmJhclRsNzJlMVJFQm50SDBQbGJ5TjRtc3g5dmFKRFo4VUtRTEpONWc0anFXam5DZUVvOHFNNnAxc1hiaW1sSC9LcEZ6UUZUdG1SMGtIdjR5VDQyU25hM3Z1cm9QMHovWDZEKzRtUXNISWVSZTU2NVRxMlQ1WS9nNnphRGlLRzd1SldGd3EzaXAveGJBR0FrT1poajEraldOZjJ0MGJOVGRiU2JsbEgvUFpQN2k0d0trRUtONWljekorcE9USUFQTHNscnJzSmZiUlgyaFNYd0oiLCJtYWMiOiI3YTU2Mzc3ZjgxMTJjODAxZWZiODNlNDc3ZGQwZmRiZWNlMTkzMDBmZGU4YmJkN2I1YWM3MjVmMTYyYzFhOWMwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjBNMWtIMzV0UVlyT3N6VS9QdzZVdnc9PSIsInZhbHVlIjoiMnJETUFJZE41eGpwL25iRUxZbnRyRjBsVVBpZmdOTlI5V1MzblNyRkZYK052QURzdGRBbzdRbFVPLzJndHNzdERLZDkxcHQxTjNyUnVnVU9TOWozMjlzQmNkdjlaSURUNWNHRUZ0c1hlZXhaUHdRbE1VVzN6U2o2aHFOOXg3VVdLMmtheVZMOXJmZTZKcVRFWUtCZVZsZXRtVzVJcTdaMVNoTmQrTTJnU1BHNWZEZGhOdmYrdGZUUjI5VTN3NmRaZ0hCUzhCRDRVQjVPQktsV2ttWGxhNU9hS1cwN2xUdzZPZXN1L1dXajl3TUthSFlDekZDb0Z5M2lJRkNscnBXWlE2TWZOMTdSYnBjd0hveDZiWkpsSlJZNEFybjR0L2xxaXQ3OGUrN1lLY2F5Lzk3LzBBanV1M2ZKL1I2amtReGlpWnF2SlVVY09rc0tSUzJidDNJWVZZNmhRUHZsOTFCOWpqSGdLZEdLZlozRW9veGFmK0tvUjc2UE5hU21OS3BHR1RGQU1iUGxUVXBSRTF0dGIxY1VKUCtnS1ZoTWNvZzYwc2RJdVpDbUVzM2RTU3dsNVRSbWYzZ1pJNlprSVljUjJFbnYrWVJpcE1mMXNOZmFKazJ5aksxMHFIeG9mTW50b1VqbGZ1dTg4dnpMcU5pb09lWkR1NDVJZkVmd3gwL3UiLCJtYWMiOiJhNTE5NjgzMTgwMzE2OTI3NTVkNjIwMzAzMzllNGQwMjM4YmYyMjI3YWZlMDU1ZDEzNTkzOTdiNDFjNzQ4ZDBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841526645\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-633420904 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UVeYwvySKA5PPFAkzc6iMcw1VaHAuVegcIgnjUUq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633420904\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1375002511 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:43:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNESDFZZEY4MWMzbXZWaGRZb2Zsc1E9PSIsInZhbHVlIjoiaVU5ODJob3BRWGJoZUFHNmM5d2VsbGNYNEU0NjFaUWo3MXBFUFB0OFpVUDkrblFuVGhUcnJmMmgvdS9SenB0SU9DMndoL0JweWhMcnlsTnBlZVRIazBMU09TT1lsTzJPZ1JtbFJlVTBUUkQxMWNPYUlPWVJkMTV0K2J0cVpEYi9rNEYvQ2djTHBLTlg1YW9STnd1Y01WMURGSHlFNDVEU3cwcGVsUCtuOWY0blJBSEx4b25QMnI2dzlDeVg4VUEvbUtoTENRRFlnK1dVM0hraDg4SUxGdkVxcVJXbHVjeHlFVUpzdmNLUnI5UWxqMDlWYjZpSzJsVlN3YTBuTHhpdFVNcFBVTlhCTUFXQjB5UGJvSkVDbHFJcVMyOEdIYlBySkN0bE1WSmpDWmwzUFJFRHI4bTVScm05UXROQWFZS05WZStndVNJRDlDRmhDWk5VMkx1VEE2SDhqQjViZDZLT2ZsRElpVURWMkFtSHlJU1podmVTUnkzWHRHanhaZXpQWWg5bkhxc1VJN290eEgzWVVoNUFwSmtSeXRXaERYUmk5OEUraEhGNmt6WlYxNjljNnYrVWhRS3p0OUlJVTJhRWtHdG5PY1MzdHZVTXZTOURDR0cxV3lLVjhzUHdPRExSUHRkVGFzL2xMSkhOeklKam15a1U0UWMvNFJRNlFjMEwiLCJtYWMiOiI2YjAwMjM1NDIxMTNjYTc2ODY1Y2JiMjZlY2U2YmFhM2VlYjE3M2QyZTc5MzdhMDIwNWE1YmYxZWU3ZjM4YmNiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkduQzlXZVE1V0FPcjBmcEUvZGZYMHc9PSIsInZhbHVlIjoiejl1SmxzcjlKWkN1Nkl6Vy9ld0VIR3F0U2wyTEV1cTBHWUI5Y1V3QTQ2RHk0SDBUR2lMQ0M0RzhoRmNZUDZFWFRDa3dWWDhOK1RTZ0pFVXhCUWZnWnlNazlrMExnNWhMdWxqbXFaem5JSlBNbnFBWUpwY0ZIcmltdi9JTUI0NGlWa2N3YkJpeHNwMVIreEluVW13eXN2YlFUK09RRFZEZmEzVHR2aVgydzlkOFJ3QUlGRFVDeVg3dTMvbWp4SW94aXV1RVBFV2hGay9meDc3bm8zeDJrR3FwVlpvZDRPK0VJemVIdFB2SVRzN0Q4VEF3OUZ5WnJJWllHZGdsRk5wMjR4MGZNWUdaSjJadUlyYW1xQXZTREwvcDc4eGVEdWMyUjJUTlB2MVZWblJFUy8vNWg5VDZqSHZpRHZxcUhJT3d6S0w0ZmlRdjVpWWRiVG5oKzZHUzFjdHRob2cvQWJVQTBMZkdaN3VwRWVTVUhhT3RkZ0pSZUN5dFViakd4QWlBL3Nuc2JUUjltdFdyQVZNeXVwVXljS28xRzFHMWt2T2t5RTlwV0xXQXN4dUUwSGFCMlNhYVN3VGh3YTlRaGFIUEtXQVJDTFIzc3liUm05WVhWSDZOWEsyY1RiTGhYYUlkK2RXekRnVFNya3V0NnFLQWdvalFpRU9hTXFVY0xoamkiLCJtYWMiOiI3NzM5OGMxZTJkMzcwMjBlZDQwMWZiZTM2MDViYzE5ZWE5Zjk3ZmM0Mzc2MmY3Mzc0ZjE2OTA3ZWU3MmNmZjE5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNESDFZZEY4MWMzbXZWaGRZb2Zsc1E9PSIsInZhbHVlIjoiaVU5ODJob3BRWGJoZUFHNmM5d2VsbGNYNEU0NjFaUWo3MXBFUFB0OFpVUDkrblFuVGhUcnJmMmgvdS9SenB0SU9DMndoL0JweWhMcnlsTnBlZVRIazBMU09TT1lsTzJPZ1JtbFJlVTBUUkQxMWNPYUlPWVJkMTV0K2J0cVpEYi9rNEYvQ2djTHBLTlg1YW9STnd1Y01WMURGSHlFNDVEU3cwcGVsUCtuOWY0blJBSEx4b25QMnI2dzlDeVg4VUEvbUtoTENRRFlnK1dVM0hraDg4SUxGdkVxcVJXbHVjeHlFVUpzdmNLUnI5UWxqMDlWYjZpSzJsVlN3YTBuTHhpdFVNcFBVTlhCTUFXQjB5UGJvSkVDbHFJcVMyOEdIYlBySkN0bE1WSmpDWmwzUFJFRHI4bTVScm05UXROQWFZS05WZStndVNJRDlDRmhDWk5VMkx1VEE2SDhqQjViZDZLT2ZsRElpVURWMkFtSHlJU1podmVTUnkzWHRHanhaZXpQWWg5bkhxc1VJN290eEgzWVVoNUFwSmtSeXRXaERYUmk5OEUraEhGNmt6WlYxNjljNnYrVWhRS3p0OUlJVTJhRWtHdG5PY1MzdHZVTXZTOURDR0cxV3lLVjhzUHdPRExSUHRkVGFzL2xMSkhOeklKam15a1U0UWMvNFJRNlFjMEwiLCJtYWMiOiI2YjAwMjM1NDIxMTNjYTc2ODY1Y2JiMjZlY2U2YmFhM2VlYjE3M2QyZTc5MzdhMDIwNWE1YmYxZWU3ZjM4YmNiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkduQzlXZVE1V0FPcjBmcEUvZGZYMHc9PSIsInZhbHVlIjoiejl1SmxzcjlKWkN1Nkl6Vy9ld0VIR3F0U2wyTEV1cTBHWUI5Y1V3QTQ2RHk0SDBUR2lMQ0M0RzhoRmNZUDZFWFRDa3dWWDhOK1RTZ0pFVXhCUWZnWnlNazlrMExnNWhMdWxqbXFaem5JSlBNbnFBWUpwY0ZIcmltdi9JTUI0NGlWa2N3YkJpeHNwMVIreEluVW13eXN2YlFUK09RRFZEZmEzVHR2aVgydzlkOFJ3QUlGRFVDeVg3dTMvbWp4SW94aXV1RVBFV2hGay9meDc3bm8zeDJrR3FwVlpvZDRPK0VJemVIdFB2SVRzN0Q4VEF3OUZ5WnJJWllHZGdsRk5wMjR4MGZNWUdaSjJadUlyYW1xQXZTREwvcDc4eGVEdWMyUjJUTlB2MVZWblJFUy8vNWg5VDZqSHZpRHZxcUhJT3d6S0w0ZmlRdjVpWWRiVG5oKzZHUzFjdHRob2cvQWJVQTBMZkdaN3VwRWVTVUhhT3RkZ0pSZUN5dFViakd4QWlBL3Nuc2JUUjltdFdyQVZNeXVwVXljS28xRzFHMWt2T2t5RTlwV0xXQXN4dUUwSGFCMlNhYVN3VGh3YTlRaGFIUEtXQVJDTFIzc3liUm05WVhWSDZOWEsyY1RiTGhYYUlkK2RXekRnVFNya3V0NnFLQWdvalFpRU9hTXFVY0xoamkiLCJtYWMiOiI3NzM5OGMxZTJkMzcwMjBlZDQwMWZiZTM2MDViYzE5ZWE5Zjk3ZmM0Mzc2MmY3Mzc0ZjE2OTA3ZWU3MmNmZjE5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375002511\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1175430181 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175430181\", {\"maxDepth\":0})</script>\n"}}