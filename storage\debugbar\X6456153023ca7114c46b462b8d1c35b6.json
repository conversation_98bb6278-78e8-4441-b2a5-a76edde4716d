{"__meta": {"id": "X6456153023ca7114c46b462b8d1c35b6", "datetime": "2025-07-31 04:48:28", "utime": **********.556294, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937307.243133, "end": **********.556353, "duration": 1.3132200241088867, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1753937307.243133, "relative_start": 0, "end": **********.323052, "relative_end": **********.323052, "duration": 1.0799188613891602, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.323067, "relative_start": 1.****************, "end": **********.556356, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "233ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jffrXGu88eClcJV70UB6z4G7pqaRH54lXYtybJd2", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-178959343 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-178959343\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1114163181 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1114163181\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1941866794 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1941866794\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-288249905 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-288249905\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-647566469 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-647566469\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InREbDA4RnUzTEl3cmpBS2JnbFVWdWc9PSIsInZhbHVlIjoiWExYeWFRSFJuZ1pncWFnTUY3MXNBRzc3c1ZFTlBjU1FhUmFwdXN4aHN4N25qR1ZjNDFmZDVISHg1UUd0UC9qeVNVUFhLRTFjeERNYlZWdGpwR3llVDcyemJHckhTOG1LVHZIT0x5NUllTFJIZE41c2ZOZnBhUkNIZWZkTWJhYjA3R21rbVNZUWlyckhiM3p4WUtxL1B6Wm5NOXpnUzZTRFhGZm04WGF6RnMwbm1VUy94djV4YmNBNDFQUzkydEt6NGhNRVZEay9aSzg2L3B6ZU12WURhZnBoY3o5Q04vTXN3TGU4Q05INUdVakZBQ3plR084QjRFV2ZzR3YvZkhHMDNVc0ZOb245K3ZUM1RnSEcvZWdNNHVpMEdNOG5ReXdsb21VYzJQUVpyWWduRWR5VW1PblhseHM0NDVlZ2labFB6YmJhNW01WllzTHprZHRvT0JzdEdtb2wydlN6L0cwSXgxam5aVHlUYndBT2dxR08wL1BYbkhuVVpVeDdYRUFpNnJ0VStoRGRwU0Nad08rWnVXb3NkUDNudXkrMzF5WlBiVkh4ckdKTUU1SzNjYUhqMTA1OXUrR1JjYXZZbExTMlJtdkhTWW44cC9kK0ZyUTVnTURKcmJDMlZ5dXJ1TjYvbkQrN2RRT3AzWkl5QkhxcGc4TmdxcDdCMjFyTFFudDkiLCJtYWMiOiJkNTlmNjdhMDAwODBjMzhkY2E1ZWI4MmRlNjYyYzkxYzQ3OTE4NzM5M2FjODE3YWNkMGJmM2JiYzBiZjliMjcwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjZpaFNHTGR2VDhpNTFNbWFkdEk1UWc9PSIsInZhbHVlIjoic3d1em13VE9VdTVETUl4alJCVmRod0dIc25YcXhGeXlZVXRWcWtwcTJHVmpIc2Vld2x3VHQ5dEtPRzVYZWxBeUFPWGROdWVsZlBmUy81V1NpT0FiZ2ZnN2llS2FwaGVqKyt2dW9jdERSWm9oZXNhUXRmNWhTU0xYZzc5dWJLdmt0Z2l6eTIwVlhwTCs4NjJkZFVpTnN6aE9vK3ZWK3N6b0lrQ2MrRHUwb0YxN3NTTzFzOVRWNzM4WXRJZ243Qkg5SlZqcTljUkorL2dOVUdPYURMV014UGJ5UHFueTF3dlRIaXBMZERGU0NVRWRFbjU5b0QrdjRIeENpbU1IaENjc09jdXIrWGdkK3FteGVxVkpub3BJN2lmNE0zclFkR21MQW5wNWxzQ0cvWmF6NlNmTUpjUGlTYnBpVmxPb21sSFZEMFRYaWxvSi8yYUIzTWFZWEczM3l3T0ZUT2YyMzJYWUdqUmtaN2JmSlphTlQxcWJaejM5UDRnT2NzdkNXSHlzM0NKOW1FdUZNN0lkOGVPNTNra05zMzM4VXQ0SjZ3bml6OUlUOTJiTS9GYUR0SStyQ1NxV28vWnZnUDluc1RlenovTUFKUkYrQzlUWktqRHZYNmpHT0hzRlVLcFVXTTEwWTZuOFZHKytnS0VQYlU1V0N0M0kxK0VidHU5aGM5ZlAiLCJtYWMiOiIxZDQ3YjNjYTMwYTg4MjU1NzExMzQxODE3MjdkMTY2OGNlMWRjMDgxM2NhNjc2MGViZjI5YzNlNTA5ZmU0NjlhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InREbDA4RnUzTEl3cmpBS2JnbFVWdWc9PSIsInZhbHVlIjoiWExYeWFRSFJuZ1pncWFnTUY3MXNBRzc3c1ZFTlBjU1FhUmFwdXN4aHN4N25qR1ZjNDFmZDVISHg1UUd0UC9qeVNVUFhLRTFjeERNYlZWdGpwR3llVDcyemJHckhTOG1LVHZIT0x5NUllTFJIZE41c2ZOZnBhUkNIZWZkTWJhYjA3R21rbVNZUWlyckhiM3p4WUtxL1B6Wm5NOXpnUzZTRFhGZm04WGF6RnMwbm1VUy94djV4YmNBNDFQUzkydEt6NGhNRVZEay9aSzg2L3B6ZU12WURhZnBoY3o5Q04vTXN3TGU4Q05INUdVakZBQ3plR084QjRFV2ZzR3YvZkhHMDNVc0ZOb245K3ZUM1RnSEcvZWdNNHVpMEdNOG5ReXdsb21VYzJQUVpyWWduRWR5VW1PblhseHM0NDVlZ2labFB6YmJhNW01WllzTHprZHRvT0JzdEdtb2wydlN6L0cwSXgxam5aVHlUYndBT2dxR08wL1BYbkhuVVpVeDdYRUFpNnJ0VStoRGRwU0Nad08rWnVXb3NkUDNudXkrMzF5WlBiVkh4ckdKTUU1SzNjYUhqMTA1OXUrR1JjYXZZbExTMlJtdkhTWW44cC9kK0ZyUTVnTURKcmJDMlZ5dXJ1TjYvbkQrN2RRT3AzWkl5QkhxcGc4TmdxcDdCMjFyTFFudDkiLCJtYWMiOiJkNTlmNjdhMDAwODBjMzhkY2E1ZWI4MmRlNjYyYzkxYzQ3OTE4NzM5M2FjODE3YWNkMGJmM2JiYzBiZjliMjcwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjZpaFNHTGR2VDhpNTFNbWFkdEk1UWc9PSIsInZhbHVlIjoic3d1em13VE9VdTVETUl4alJCVmRod0dIc25YcXhGeXlZVXRWcWtwcTJHVmpIc2Vld2x3VHQ5dEtPRzVYZWxBeUFPWGROdWVsZlBmUy81V1NpT0FiZ2ZnN2llS2FwaGVqKyt2dW9jdERSWm9oZXNhUXRmNWhTU0xYZzc5dWJLdmt0Z2l6eTIwVlhwTCs4NjJkZFVpTnN6aE9vK3ZWK3N6b0lrQ2MrRHUwb0YxN3NTTzFzOVRWNzM4WXRJZ243Qkg5SlZqcTljUkorL2dOVUdPYURMV014UGJ5UHFueTF3dlRIaXBMZERGU0NVRWRFbjU5b0QrdjRIeENpbU1IaENjc09jdXIrWGdkK3FteGVxVkpub3BJN2lmNE0zclFkR21MQW5wNWxzQ0cvWmF6NlNmTUpjUGlTYnBpVmxPb21sSFZEMFRYaWxvSi8yYUIzTWFZWEczM3l3T0ZUT2YyMzJYWUdqUmtaN2JmSlphTlQxcWJaejM5UDRnT2NzdkNXSHlzM0NKOW1FdUZNN0lkOGVPNTNra05zMzM4VXQ0SjZ3bml6OUlUOTJiTS9GYUR0SStyQ1NxV28vWnZnUDluc1RlenovTUFKUkYrQzlUWktqRHZYNmpHT0hzRlVLcFVXTTEwWTZuOFZHKytnS0VQYlU1V0N0M0kxK0VidHU5aGM5ZlAiLCJtYWMiOiIxZDQ3YjNjYTMwYTg4MjU1NzExMzQxODE3MjdkMTY2OGNlMWRjMDgxM2NhNjc2MGViZjI5YzNlNTA5ZmU0NjlhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-744041474 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jffrXGu88eClcJV70UB6z4G7pqaRH54lXYtybJd2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744041474\", {\"maxDepth\":0})</script>\n"}}