{"__meta": {"id": "Xc9f9afd173fa50db8871baf1ebdb5016", "datetime": "2025-07-31 04:43:40", "utime": **********.10807, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[04:43:40] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.102541, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753937018.911062, "end": **********.108102, "duration": 1.19704008102417, "duration_str": "1.2s", "measures": [{"label": "Booting", "start": 1753937018.911062, "relative_start": 0, "end": 1753937019.96771, "relative_end": 1753937019.96771, "duration": 1.0566480159759521, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753937019.967732, "relative_start": 1.0566699504852295, "end": **********.108105, "relative_end": 2.86102294921875e-06, "duration": 0.14037299156188965, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614160, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.086672, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02238, "accumulated_duration_str": "22.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0198088, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 15.282}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.038914, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 15.282, "width_percent": 3.485}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.045307, "duration": 0.01709, "duration_str": "17.09ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 18.767, "width_percent": 76.363}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.066685, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 95.13, "width_percent": 4.87}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-622066557 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-622066557\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-409996642 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-409996642\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1579436035 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579436035\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-169367238 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IisyVXFMbU9SQVdWVjltMzlHMVpLTXc9PSIsInZhbHVlIjoiM3NjMmZHdjA0MjZ0b2VUM1U4WWZyZllrUDBPMEQ1a0k5SVJVUy9YajljU0FPUTZFdXdsNU9CTTlwN1ZZYitnTG9qY0JwSjZhdlBka2g3cFpERzlEeCtjc2FBVFJ6Slovb0JyMmRKNVd0TkdEY0tiQjNXelVKblhtWWd0bURycktmZnJwL0FURlVubnZlZUpIWG1obHM3djFyVTJCbG12RWdiQm0xRlo1SElSV0tFVERMb3VWcytJNnNqZXZLcU9EUzhrTHFVRVUvNnBySUZJQUN5MHhSR3Vna2dOZXRlUDNYZG5ENTMwTkwzenZMRlJJVzBPZW1adi93ZTQ5RDN2Y08rQjcvcVpmeXRrNHJMRjZFVURnaDN1U3RGT0k4MkZ1cjR2Nk1XQW1TN0VpSExaTmJhclRsNzJlMVJFQm50SDBQbGJ5TjRtc3g5dmFKRFo4VUtRTEpONWc0anFXam5DZUVvOHFNNnAxc1hiaW1sSC9LcEZ6UUZUdG1SMGtIdjR5VDQyU25hM3Z1cm9QMHovWDZEKzRtUXNISWVSZTU2NVRxMlQ1WS9nNnphRGlLRzd1SldGd3EzaXAveGJBR0FrT1poajEraldOZjJ0MGJOVGRiU2JsbEgvUFpQN2k0d0trRUtONWljekorcE9USUFQTHNscnJzSmZiUlgyaFNYd0oiLCJtYWMiOiI3YTU2Mzc3ZjgxMTJjODAxZWZiODNlNDc3ZGQwZmRiZWNlMTkzMDBmZGU4YmJkN2I1YWM3MjVmMTYyYzFhOWMwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjBNMWtIMzV0UVlyT3N6VS9QdzZVdnc9PSIsInZhbHVlIjoiMnJETUFJZE41eGpwL25iRUxZbnRyRjBsVVBpZmdOTlI5V1MzblNyRkZYK052QURzdGRBbzdRbFVPLzJndHNzdERLZDkxcHQxTjNyUnVnVU9TOWozMjlzQmNkdjlaSURUNWNHRUZ0c1hlZXhaUHdRbE1VVzN6U2o2aHFOOXg3VVdLMmtheVZMOXJmZTZKcVRFWUtCZVZsZXRtVzVJcTdaMVNoTmQrTTJnU1BHNWZEZGhOdmYrdGZUUjI5VTN3NmRaZ0hCUzhCRDRVQjVPQktsV2ttWGxhNU9hS1cwN2xUdzZPZXN1L1dXajl3TUthSFlDekZDb0Z5M2lJRkNscnBXWlE2TWZOMTdSYnBjd0hveDZiWkpsSlJZNEFybjR0L2xxaXQ3OGUrN1lLY2F5Lzk3LzBBanV1M2ZKL1I2amtReGlpWnF2SlVVY09rc0tSUzJidDNJWVZZNmhRUHZsOTFCOWpqSGdLZEdLZlozRW9veGFmK0tvUjc2UE5hU21OS3BHR1RGQU1iUGxUVXBSRTF0dGIxY1VKUCtnS1ZoTWNvZzYwc2RJdVpDbUVzM2RTU3dsNVRSbWYzZ1pJNlprSVljUjJFbnYrWVJpcE1mMXNOZmFKazJ5aksxMHFIeG9mTW50b1VqbGZ1dTg4dnpMcU5pb09lWkR1NDVJZkVmd3gwL3UiLCJtYWMiOiJhNTE5NjgzMTgwMzE2OTI3NTVkNjIwMzAzMzllNGQwMjM4YmYyMjI3YWZlMDU1ZDEzNTkzOTdiNDFjNzQ4ZDBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169367238\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1827976013 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UVeYwvySKA5PPFAkzc6iMcw1VaHAuVegcIgnjUUq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827976013\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-508608545 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:43:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNzTG8xdE1pRVhzT1plOUpwN0VKV0E9PSIsInZhbHVlIjoiYzF1aU40QThWT09OWndOR3hxbVR0ZlZKMU1oa0xUNHk3NHFYcSt0L1dsUndua0RMZUcxODk5Y0RaUXhNR2VKaHBycXBWL2FPNWhjSXBpK3RaVGxCbC9BbTdPTTR4K2xuWG92REp2TFFjMEQ4ZDM3WEYzNHN0elF4ZGJmWEdIUmNIV1ZmSEV6bE13UEN3a0V6Mk5yN202UGd5YWRKWFJXcEZLTC91VXJCdERET21kNlBHRFh6ZFEybE1LaEdCejk5a0dHZEN5R1FGbzFKejkxUzVqdWx4bE82TEJPRmNYY1NiQTg0WjloQkZjM1RUV1NiQm4zRUF4R3pwRndZYjJGUmdLTFh0ZWxBclpUWTFtZ1ZGUW9odHp3cEVSdFp4dzAzK1Joa01yTWptQ2VBQ0FZSy9ZSDl6eVF5LzJobi9HdUR6Sy81REhBTWttR3JjZUtXZU11ZWRHVURiOEJoYUUxRnp4dkZLRzZTSWJKS24rWmtIeEhDdit0Y211WTdUOHJUYllXUkJyRUkrMm9yMG1jclNCdDcwT3RiL3hzcVE3Mm5JOFpyTFNWemRYOXlXZFc1UFJ1QStTUFA1Tm1TZVFEMDdOeHFyNjRzdXc2LzRNQnpHZGRramVCUFBmU09vd21KaFliaGY0RTFrRERwR2VRZDkzUnZYWUMzcEFzUXVnaUgiLCJtYWMiOiJjODdhY2E0YmIzODE0NWM1YzVmNzgyODZiNzRmMTYyZjUyOWNiNTg4MmNiNjY1YWNkZWExMzM0YTc2OTFlMTA1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJvL2U3TzdscXY1V3dZM29jb3VDWEE9PSIsInZhbHVlIjoiM1R2TjFqdmVucDVKdHRvamRFQmltem1ma0dsNVFxdWYva0ROMm9tN1hqemRzYVJIUTRHb1lEcWt0Tk84VnQvaG11ZmNxRjNMOWtCdXIrY2ZhWStzTFhmMllHeHBOdUtqQU9RVHF5c2txWG5Lc1FFUyt1WGZKR1VYQVRYSzBIV0hrN1N6ZDZ5NHY2TER5MTJIeis4VXp4ekJMYWU1bEg5cTluQ1g4Z1JBY0Z3UmtJUFZiT2F6SzMwVlVCWGhpenZQYzJQVUR5aGZhR0p2akJSclp3bWVIQUJDa0szRWpJVnc2SE5TRzYrczl0V0c3aEMxSXVLRFFreElDSTFIV3NadUdPWnZrSi9ENGcxZmxraUFtaVBTUjJjNGdFRjFDNFlsSUtMUi9ISDJJWk1sSVNPd0xDR2ZXc0xId3FYMjBvbFA4Vy9FaWhmU29hODVSMCtZcEVqeGxhdS9kV05rODNJTUhwdjRnQ2tqaEkvL1Q2STNuY0xGLytlOTQ4Y3pGYlZuNmFDRUdyL2lxRGUySXZ6NzhUMWl4YWJ4REtXZkk0eVpSNW5EcU5GWFN5QUFQNUxrQmRxc1BQU3JHd1RUU0ZMNmVyS1JhZ05zZTY1UUpJSDhUc3JXNm5RSmxhMnIxMzgwN1VQd2s5UkFaa0Erb0ZTRkJxdVRsbVEzSW8xNGhkNkoiLCJtYWMiOiI1NmU4M2VhMjZlNmVlODNiN2UyMWU4YzYyNGE1YTVhMTM5ZjRjNDM3Y2Y1NzAzZGI4MzFiZTdhMTk5Y2EzZTNlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNzTG8xdE1pRVhzT1plOUpwN0VKV0E9PSIsInZhbHVlIjoiYzF1aU40QThWT09OWndOR3hxbVR0ZlZKMU1oa0xUNHk3NHFYcSt0L1dsUndua0RMZUcxODk5Y0RaUXhNR2VKaHBycXBWL2FPNWhjSXBpK3RaVGxCbC9BbTdPTTR4K2xuWG92REp2TFFjMEQ4ZDM3WEYzNHN0elF4ZGJmWEdIUmNIV1ZmSEV6bE13UEN3a0V6Mk5yN202UGd5YWRKWFJXcEZLTC91VXJCdERET21kNlBHRFh6ZFEybE1LaEdCejk5a0dHZEN5R1FGbzFKejkxUzVqdWx4bE82TEJPRmNYY1NiQTg0WjloQkZjM1RUV1NiQm4zRUF4R3pwRndZYjJGUmdLTFh0ZWxBclpUWTFtZ1ZGUW9odHp3cEVSdFp4dzAzK1Joa01yTWptQ2VBQ0FZSy9ZSDl6eVF5LzJobi9HdUR6Sy81REhBTWttR3JjZUtXZU11ZWRHVURiOEJoYUUxRnp4dkZLRzZTSWJKS24rWmtIeEhDdit0Y211WTdUOHJUYllXUkJyRUkrMm9yMG1jclNCdDcwT3RiL3hzcVE3Mm5JOFpyTFNWemRYOXlXZFc1UFJ1QStTUFA1Tm1TZVFEMDdOeHFyNjRzdXc2LzRNQnpHZGRramVCUFBmU09vd21KaFliaGY0RTFrRERwR2VRZDkzUnZYWUMzcEFzUXVnaUgiLCJtYWMiOiJjODdhY2E0YmIzODE0NWM1YzVmNzgyODZiNzRmMTYyZjUyOWNiNTg4MmNiNjY1YWNkZWExMzM0YTc2OTFlMTA1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJvL2U3TzdscXY1V3dZM29jb3VDWEE9PSIsInZhbHVlIjoiM1R2TjFqdmVucDVKdHRvamRFQmltem1ma0dsNVFxdWYva0ROMm9tN1hqemRzYVJIUTRHb1lEcWt0Tk84VnQvaG11ZmNxRjNMOWtCdXIrY2ZhWStzTFhmMllHeHBOdUtqQU9RVHF5c2txWG5Lc1FFUyt1WGZKR1VYQVRYSzBIV0hrN1N6ZDZ5NHY2TER5MTJIeis4VXp4ekJMYWU1bEg5cTluQ1g4Z1JBY0Z3UmtJUFZiT2F6SzMwVlVCWGhpenZQYzJQVUR5aGZhR0p2akJSclp3bWVIQUJDa0szRWpJVnc2SE5TRzYrczl0V0c3aEMxSXVLRFFreElDSTFIV3NadUdPWnZrSi9ENGcxZmxraUFtaVBTUjJjNGdFRjFDNFlsSUtMUi9ISDJJWk1sSVNPd0xDR2ZXc0xId3FYMjBvbFA4Vy9FaWhmU29hODVSMCtZcEVqeGxhdS9kV05rODNJTUhwdjRnQ2tqaEkvL1Q2STNuY0xGLytlOTQ4Y3pGYlZuNmFDRUdyL2lxRGUySXZ6NzhUMWl4YWJ4REtXZkk0eVpSNW5EcU5GWFN5QUFQNUxrQmRxc1BQU3JHd1RUU0ZMNmVyS1JhZ05zZTY1UUpJSDhUc3JXNm5RSmxhMnIxMzgwN1VQd2s5UkFaa0Erb0ZTRkJxdVRsbVEzSW8xNGhkNkoiLCJtYWMiOiI1NmU4M2VhMjZlNmVlODNiN2UyMWU4YzYyNGE1YTVhMTM5ZjRjNDM3Y2Y1NzAzZGI4MzFiZTdhMTk5Y2EzZTNlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508608545\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-523646102 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-523646102\", {\"maxDepth\":0})</script>\n"}}