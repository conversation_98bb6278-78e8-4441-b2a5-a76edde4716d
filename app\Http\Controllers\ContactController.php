<?php

namespace App\Http\Controllers;

use App\Models\Lead;
use App\Models\Deal;
use App\Models\Pipeline;
use App\Models\LeadStage;
use App\Exports\ContactExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class ContactController extends Controller
{
    /**
     * Display a listing of contacts from leads and deals.
     */
    public function index(Request $request)
    {
        $contacts = [];

        // Build leads query
        $leadsQuery = Lead::where('created_by', Auth::user()->creatorId());

        // Apply filters for leads
        if ($request->filled('filter_name')) {
            $leadsQuery->where('name', 'like', '%' . $request->filter_name . '%');
        }
        if ($request->filled('filter_email')) {
            $leadsQuery->where('email', 'like', '%' . $request->filter_email . '%');
        }
        if ($request->filled('filter_phone')) {
            $leadsQuery->where('phone', 'like', '%' . $request->filter_phone . '%');
        }

        // Fetch leads data with contact group information
        $leads = $leadsQuery->with('contactGroup')->get();
        foreach ($leads as $lead) {
            $contacts[] = [
                'id' => $lead->id,
                'name' => $lead->name,
                'email' => $lead->email ?? '',
                'phone' => $lead->phone ?? '',
                'type' => 'Lead',
                'contact_group' => $lead->contactGroup ? $lead->contactGroup->name : null
            ];
        }

        // Build deals query
        $dealsQuery = Deal::where('created_by', Auth::user()->creatorId());

        // Apply filters for deals
        if ($request->filled('filter_name')) {
            $dealsQuery->where('name', 'like', '%' . $request->filter_name . '%');
        }
        if ($request->filled('filter_phone')) {
            $dealsQuery->where('phone', 'like', '%' . $request->filter_phone . '%');
        }

        // Fetch deals data
        $deals = $dealsQuery->get();
        foreach ($deals as $deal) {
            $contacts[] = [
                'id' => $deal->id,
                'name' => $deal->name ?? 'No Name',
                'email' => '', // Deals don't have email field
                'phone' => $deal->phone ?? '',
                'type' => 'Deal'
            ];
        }

        // External contacts are now loaded via JavaScript on the frontend
        // This provides better performance and handles API connectivity issues gracefully

        // Apply type filter (only for internal contacts - leads and deals)
        if ($request->filled('filter_type') && $request->filter_type !== 'WhatsApp') {
            $contacts = array_filter($contacts, function($contact) use ($request) {
                return $contact['type'] === $request->filter_type;
            });
        }

        // Get enabled module integration for OMX Flow (same as leads)
        $omxFlowModule = \App\Models\ModuleIntegration::enabled()
            ->whereNotNull('base_url')
            ->where('base_url', '!=', '')
            ->first();

        // Get pipelines for convert to lead modal
        $pipelines = Pipeline::where('created_by', Auth::user()->creatorId())->get();

        // Create default pipeline if none exists
        if ($pipelines->isEmpty()) {
            $pipeline = Pipeline::create([
                'name' => 'Default Pipeline',
                'created_by' => Auth::user()->creatorId()
            ]);

            // Create default stages for the pipeline
            $defaultStages = ['New', 'Qualified', 'Discussion', 'Negotiation', 'Won/Lost'];
            foreach($defaultStages as $order => $stageName) {
                LeadStage::create([
                    'name' => $stageName,
                    'pipeline_id' => $pipeline->id,
                    'created_by' => Auth::user()->creatorId(),
                    'order' => $order
                ]);
            }

            // Re-fetch pipelines after creating defaults
            $pipelines = Pipeline::where('created_by', Auth::user()->creatorId())->get();
        }

        // Get global tags for tags field
        $tags = \App\Models\Tag::where('created_by', Auth::user()->creatorId())->where('is_active', 1)->pluck('name', 'id');
        
        // Create sample tags if none exist
        if ($tags->isEmpty()) {
            $sampleTags = [
                ['name' => 'Hot Lead', 'color' => 'danger'],
                ['name' => 'Cold Lead', 'color' => 'secondary'],
                ['name' => 'Warm Lead', 'color' => 'warning'],
                ['name' => 'VIP', 'color' => 'primary'],
                ['name' => 'Follow Up', 'color' => 'info'],
                ['name' => 'New Customer', 'color' => 'success']
            ];
            
            foreach ($sampleTags as $tagData) {
                \App\Models\Tag::create([
                    'name' => $tagData['name'],
                    'created_by' => Auth::user()->creatorId(),
                    'is_active' => 1
                ]);
            }
            
            // Refresh tags after creating samples
            $tags = \App\Models\Tag::where('created_by', Auth::user()->creatorId())->where('is_active', 1)->pluck('name', 'id');
        }
        
        // Keep labels for backward compatibility with the view
        $labels = $tags;

        return view('contacts.index', compact('contacts', 'omxFlowModule', 'pipelines', 'labels'));
    }

    /**
     * Check which external contact emails have already been converted to leads
     */
    public function checkConvertedContacts(Request $request)
    {
        $emails = $request->input('emails', []);

        if (empty($emails)) {
            return response()->json(['converted_emails' => []]);
        }

        // Check if any of these emails already exist as leads
        $convertedEmails = Lead::where('created_by', Auth::user()->creatorId())
            ->whereIn('email', $emails)
            ->pluck('email')
            ->map(function($email) {
                return strtolower($email);
            })
            ->toArray();

        return response()->json(['converted_emails' => $convertedEmails]);
    }

    /**
     * Get stages for a specific pipeline
     */
    public function getPipelineStages(Request $request)
    {
        $pipelineId = $request->input('pipeline_id');

        \Log::info('ContactController: Getting stages for pipeline', [
            'pipeline_id' => $pipelineId,
            'user_id' => Auth::id(),
            'creator_id' => Auth::user()->creatorId(),
            'request_data' => $request->all()
        ]);

        if (!$pipelineId) {
            \Log::warning('No pipeline ID provided');
            return response()->json([
                'success' => false,
                'message' => 'Pipeline ID is required',
                'stages' => []
            ]);
        }

        try {
            $stages = \App\Models\LeadStage::where('pipeline_id', $pipelineId)
                ->where('created_by', Auth::user()->creatorId())
                ->orderBy('order', 'asc')
                ->orderBy('id', 'asc')
                ->get(['id', 'name', 'order']);

            \Log::info('ContactController: Found stages', [
                'pipeline_id' => $pipelineId,
                'count' => $stages->count(),
                'stages' => $stages->toArray()
            ]);

            return response()->json([
                'success' => true,
                'stages' => $stages,
                'debug' => [
                    'pipeline_id' => $pipelineId,
                    'creator_id' => Auth::user()->creatorId(),
                    'stages_count' => $stages->count()
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('ContactController: Error fetching stages', [
                'error' => $e->getMessage(),
                'pipeline_id' => $pipelineId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error fetching stages: ' . $e->getMessage(),
                'stages' => []
            ]);
        }
    }

    /**
     * Get contacts for appointment booking form
     */
    public function getAppointmentContacts(Request $request)
    {
        try {
            $contacts = [];

            // Fetch leads
            $leads = Lead::where('created_by', Auth::user()->creatorId())
                ->where('is_active', 1)
                ->select('id', 'name', 'email', 'phone')
                ->get();

            foreach ($leads as $lead) {
                $contacts[] = [
                    'id' => $lead->id,
                    'name' => $lead->name ?? 'No Name',
                    'email' => $lead->email ?? '',
                    'phone' => $lead->phone ?? '',
                    'type' => 'Lead'
                ];
            }

            // Fetch deals
            $deals = \App\Models\Deal::where('created_by', Auth::user()->creatorId())
                ->where('is_active', 1)
                ->select('id', 'name', 'phone')
                ->get();

            foreach ($deals as $deal) {
                $contacts[] = [
                    'id' => $deal->id,
                    'name' => $deal->name ?? 'No Name',
                    'email' => '', // Deals don't have email field
                    'phone' => $deal->phone ?? '',
                    'type' => 'Deal'
                ];
            }

            return response()->json([
                'success' => true,
                'contacts' => $contacts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch contacts: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update contact pipeline assignment (for both Leads and Deals)
     */
    public function updateContactPipeline(Request $request, $id)
    {
        \Log::info('ContactController: Updating contact pipeline', [
            'contact_id' => $id,
            'request_data' => $request->all(),
            'user_id' => Auth::id()
        ]);

        $validator = \Validator::make($request->all(), [
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:lead_stages,id',
            'notes' => 'nullable|string',
            'contact_type' => 'nullable|string|in:Lead,Deal'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
                'errors' => $validator->errors()
            ], 422);
        }

        // Try to find as Lead first, then as Deal
        $contact = Lead::where('id', $id)
            ->where('created_by', Auth::user()->creatorId())
            ->first();

        $contactType = 'Lead';

        if (!$contact) {
            $contact = \App\Models\Deal::where('id', $id)
                ->where('created_by', Auth::user()->creatorId())
                ->first();
            $contactType = 'Deal';
        }

        if (!$contact) {
            return response()->json([
                'success' => false,
                'message' => 'Contact not found'
            ], 404);
        }

        // Store original values for change tracking
        $originalStageId = $contact->stage_id ?? null;
        $originalPipelineId = $contact->pipeline_id ?? null;

        // Update pipeline and stage
        $contact->pipeline_id = $request->pipeline_id;
        $contact->stage_id = $request->stage_id;

        // Update notes if provided
        if ($request->filled('notes')) {
            $contact->notes = $request->notes;
        }

        $contact->save();

        // Get pipeline and stage names for response
        $pipeline = Pipeline::find($request->pipeline_id);
        $stage = \App\Models\LeadStage::find($request->stage_id);

        \Log::info('Contact pipeline updated successfully', [
            'contact_id' => $id,
            'contact_name' => $contact->name,
            'contact_type' => $contactType,
            'pipeline_name' => $pipeline->name,
            'stage_name' => $stage->name
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Contact moved to pipeline successfully!',
            'contact' => [
                'id' => $contact->id,
                'name' => $contact->name,
                'type' => $contactType,
                'pipeline_id' => $contact->pipeline_id,
                'stage_id' => $contact->stage_id,
                'pipeline_name' => $pipeline->name,
                'stage_name' => $stage->name
            ]
        ]);
    }

    /**
     * Fetch external contacts from API
     */
    private function fetchExternalContacts(Request $request)
    {
        $externalContacts = [];

        try {
            // Generate SSO token (similar to leads list view)
            $ssoToken = $this->generateSsoToken();

            if ($ssoToken) {
                $baseUrl = config('app.url');
                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $ssoToken,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'X-User-Email' => Auth::user()->email
                ])->get($baseUrl . '/api/sso/contacts/simple-list');

                if ($response->successful()) {
                    $data = $response->json();

                    if (isset($data['data']['contacts']) && is_array($data['data']['contacts'])) {
                        foreach ($data['data']['contacts'] as $contact) {
                            $contactName = $contact['full_name'] ?? $contact['name'] ?? 'N/A';
                            $contactEmail = $contact['email'] ?? '';

                            // Apply filters for external contacts
                            $includeContact = true;

                            if ($request->filled('filter_name') && stripos($contactName, $request->filter_name) === false) {
                                $includeContact = false;
                            }

                            if ($request->filled('filter_email') && stripos($contactEmail, $request->filter_email) === false) {
                                $includeContact = false;
                            }

                            if ($includeContact) {
                                $externalContacts[] = [
                                    'id' => 'ext_' . $contact['id'],
                                    'name' => $contactName,
                                    'email' => $contactEmail,
                                    'phone' => '', // External contacts might not have phone
                                    'type' => 'WhatsApp',
                                    'source' => 'external',
                                    'source_id' => $contact['id'],
                                    'external_id' => $contact['id']
                                ];
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // Log error but don't break the page
            Log::error('Failed to fetch external contacts: ' . $e->getMessage());
        }

        return $externalContacts;
    }

    /**
     * Generate SSO token for external API
     */
    private function generateSsoToken()
    {
        try {
            $baseUrl = config('app.url');
            $response = Http::post($baseUrl . '/api/sso/generate-token', [
                'email' => Auth::user()->email,
                'name' => Auth::user()->name
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['token'] ?? null;
            }
        } catch (\Exception $e) {
            Log::error('Failed to generate SSO token: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Update lead via AJAX
     */
    public function updateLead(Request $request, $id)
    {
        $lead = Lead::where('id', $id)
            ->where('created_by', Auth::user()->creatorId())
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $validator = \Validator::make($request->all(), [
            'name' => 'required|max:120',
            'email' => 'nullable|email',
            'phone' => 'nullable|max:20',
            'date_of_birth' => 'nullable|date',
            'contact_type' => 'nullable|in:Lead,Customer,Prospect',
            'tags' => 'nullable|string',
            'postal_code' => 'nullable|max:20',
            'city' => 'nullable|max:100',
            'state' => 'nullable|max:100',
            'country' => 'nullable|max:100',
            'business_name' => 'nullable|max:255',
            'business_gst' => 'nullable|max:50',
            'business_state' => 'nullable|max:100',
            'business_postal_code' => 'nullable|max:20',
            'business_address' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Update basic fields
        $lead->name = $request->name;
        $lead->email = $request->email;
        $lead->phone = $request->phone;

        // Update personal information fields
        if ($request->has('date_of_birth')) {
            $lead->date_of_birth = $request->date_of_birth;
        }

        if ($request->has('contact_type')) {
            $lead->contact_type = $request->contact_type;
        }

        if ($request->has('tags')) {
            $lead->tags = $request->tags;
        }

        // Update address fields
        if ($request->has('postal_code')) {
            $lead->postal_code = $request->postal_code;
        }

        if ($request->has('city')) {
            $lead->city = $request->city;
        }

        if ($request->has('state')) {
            $lead->state = $request->state;
        }

        if ($request->has('country')) {
            $lead->country = $request->country;
        }

        // Update business fields
        if ($request->has('business_name')) {
            $lead->business_name = $request->business_name;
        }

        if ($request->has('business_gst')) {
            $lead->business_gst = $request->business_gst;
        }

        if ($request->has('business_state')) {
            $lead->business_state = $request->business_state;
        }

        if ($request->has('business_postal_code')) {
            $lead->business_postal_code = $request->business_postal_code;
        }

        if ($request->has('business_address')) {
            $lead->business_address = $request->business_address;
        }

        // Update notes
        if ($request->has('notes')) {
            $lead->notes = $request->notes;
        }

        // Handle DND settings
        if ($request->has(['dnd_all', 'dnd_emails', 'dnd_whatsapp', 'dnd_sms', 'dnd_calls'])) {
            $dndSettings = [
                'all' => $request->has('dnd_all'),
                'emails' => $request->has('dnd_emails'),
                'whatsapp' => $request->has('dnd_whatsapp'),
                'sms' => $request->has('dnd_sms'),
                'calls' => $request->has('dnd_calls')
            ];
            $lead->dnd_settings = json_encode($dndSettings);
        }

        // Handle tags (support both 'labels' and 'tags' parameters)
        $tagsInput = $request->has('tags') ? $request->tags : $request->labels;
        if ($tagsInput && is_array($tagsInput)) {
            $tagIds = [];

            foreach ($tagsInput as $tag) {
                if (strpos($tag, 'new_') === 0) {
                    // This is a new tag, create it
                    $newTagName = substr($tag, 4); // Remove 'new_' prefix

                    // Check if tag already exists
                    $existingTag = \App\Models\Tag::where('name', $newTagName)
                        ->where('created_by', Auth::user()->creatorId())
                        ->first();

                    if ($existingTag) {
                        $tagIds[] = $existingTag->id;
                    } else {
                        // Create new tag
                        $newTag = \App\Models\Tag::create([
                            'name' => $newTagName,
                            'color' => 'primary', // Default color
                            'created_by' => Auth::user()->creatorId(),
                            'is_active' => 1
                        ]);
                        $tagIds[] = $newTag->id;
                    }
                } else {
                    // This is an existing tag ID
                    $tagIds[] = $tag;
                }
            }

            // Update lead tags
            if (!empty($tagIds)) {
                $lead->tags = implode(",", $tagIds);
            } else {
                $lead->tags = null;
            }
        } elseif ($request->has('tags') || $request->has('labels')) {
            // If tags/labels parameter exists but is empty, clear tags
            $lead->tags = null;
        }

        $lead->save();

        return response()->json(['success' => 'Lead updated successfully']);
    }

    /**
     * Update deal via AJAX
     */
    public function updateDeal(Request $request, $id)
    {
        $deal = Deal::where('id', $id)
            ->where('created_by', Auth::user()->creatorId())
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        $validator = \Validator::make($request->all(), [
            'name' => 'required|max:120',
            'phone' => 'nullable|max:20',
            'price' => 'nullable|numeric',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Update basic fields
        $deal->name = $request->name;
        $deal->phone = $request->phone;
        $deal->price = $request->price ?? 0;

        // Update additional fields if provided
        if ($request->has('notes')) {
            $deal->notes = $request->notes;
        }

        $deal->save();

        return response()->json(['success' => 'Deal updated successfully']);
    }

    /**
     * Get lead data for preview modal
     */
    public function getLeadForPreview($id)
    {
        try {
            $lead = Lead::where('id', $id)
                ->where('created_by', Auth::user()->creatorId())
                ->with(['stage', 'pipeline'])
                ->first();

            if (!$lead) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lead not found'
                ], 404);
            }

            // Parse DND settings if they exist
            $dndSettings = [];
            if ($lead->dnd_settings) {
                $dndSettings = json_decode($lead->dnd_settings, true) ?? [];
            }

            // Add parsed DND settings to the lead data
            $leadData = $lead->toArray();
            $leadData['dnd_parsed'] = $dndSettings;
            
            // Add processed tags (array of tag objects) instead of raw tags string
            $leadData['tags'] = $lead->tags();

            return response()->json([
                'success' => true,
                'data' => $leadData,
                'message' => 'Lead retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving lead: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get deal data for preview modal
     */
    public function getDealForPreview($id)
    {
        try {
            $deal = Deal::where('id', $id)
                ->where('created_by', Auth::user()->creatorId())
                ->with(['stage', 'pipeline'])
                ->first();

            if (!$deal) {
                return response()->json([
                    'success' => false,
                    'message' => 'Deal not found'
                ], 404);
            }

            // Parse DND settings if they exist
            $dndSettings = [];
            if ($deal->dnd_settings) {
                $dndSettings = json_decode($deal->dnd_settings, true) ?? [];
            }

            // Add parsed DND settings to the deal data
            $dealData = $deal->toArray();
            $dealData['dnd_parsed'] = $dndSettings;
            
            // Add processed tags (array of tag objects) instead of raw tags string
            $dealData['tags'] = $deal->tags();

            return response()->json([
                'success' => true,
                'data' => $dealData,
                'message' => 'Deal retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving deal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add tags to multiple contacts (leads and deals)
     */
    public function addTagsToContacts(Request $request)
    {
        try {
            $validator = \Validator::make($request->all(), [
                'contact_ids' => 'required|array',
                'contact_ids.*' => 'required|string',
                'tag_ids' => 'required|array',
                'tag_ids.*' => 'required|string', // Allow both existing IDs and new_ prefixed tags
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $contactIds = $request->contact_ids;
            $tagsInput = $request->tag_ids;

            // Process tags - handle both existing IDs and new tag creation
            $tagIds = [];
            foreach ($tagsInput as $tag) {
                if (strpos($tag, 'new_') === 0) {
                    // This is a new tag, create it
                    $newTagName = substr($tag, 4); // Remove 'new_' prefix

                    // Check if tag already exists
                    $existingTag = \App\Models\Tag::where('name', $newTagName)
                        ->where('created_by', Auth::user()->creatorId())
                        ->first();

                    if ($existingTag) {
                        $tagIds[] = $existingTag->id;
                    } else {
                        // Create new tag
                        $newTag = \App\Models\Tag::create([
                            'name' => $newTagName,
                            'color' => 'primary', // Default color
                            'created_by' => Auth::user()->creatorId(),
                            'is_active' => 1
                        ]);
                        $tagIds[] = $newTag->id;
                    }
                } else {
                    // This is an existing tag ID - validate it exists
                    $existingTag = \App\Models\Tag::where('id', $tag)
                        ->where('created_by', Auth::user()->creatorId())
                        ->first();

                    if ($existingTag) {
                        $tagIds[] = $tag;
                    }
                }
            }

            // Check if we have any valid tags after processing
            if (empty($tagIds)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No valid tags found to add'
                ], 422);
            }

            $updatedCount = 0;
            $errors = [];

            foreach ($contactIds as $contactId) {
                // Parse contact ID to determine type and actual ID
                $parts = explode('_', $contactId);
                if (count($parts) !== 2) {
                    $errors[] = "Invalid contact ID format: $contactId";
                    continue;
                }

                $type = $parts[0];
                $id = $parts[1];

                try {
                    if ($type === 'lead') {
                        $contact = Lead::where('id', $id)
                            ->where('created_by', Auth::user()->creatorId())
                            ->first();
                    } elseif ($type === 'deal') {
                        $contact = Deal::where('id', $id)
                            ->where('created_by', Auth::user()->creatorId())
                            ->first();
                    } else {
                        $errors[] = "Unknown contact type: $type";
                        continue;
                    }

                    if (!$contact) {
                        $errors[] = "Contact not found: $contactId";
                        continue;
                    }

                    // Get existing tags/labels
                    if ($type === 'lead') {
                        $existingTags = $contact->tags ? explode(',', $contact->tags) : [];
                        $contact->tags = implode(',', array_unique(array_merge($existingTags, $tagIds)));
                    } else {
                        // For deals, use the labels field
                        $existingTags = $contact->labels ? explode(',', $contact->labels) : [];
                        $contact->labels = implode(',', array_unique(array_merge($existingTags, $tagIds)));
                    }
                    $contact->save();

                    $updatedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Error updating contact $contactId: " . $e->getMessage();
                }
            }

            $message = "Successfully added tags to $updatedCount contact(s)";
            if (!empty($errors)) {
                $message .= ". Errors: " . implode(', ', $errors);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'updated_count' => $updatedCount,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error adding tags: ' . $e->getMessage()
            ], 500);
        }
    }
    public function export()
    {
        $name = 'contacts_' . date('Y-m-d_H-i-s');
        $data = Excel::download(new ContactExport(), $name . '.csv', \Maatwebsite\Excel\Excel::CSV);
        ob_end_clean();

        return $data;
    }

    public function importFile()
    {
        return view('contacts.import');
    }

    public function fileImport(Request $request)
    {
        $error = '';
        $html = '';

        if ($request->hasFile('file') && $request->file->getClientOriginalName() != '') {
            $file_array = explode(".", $request->file->getClientOriginalName());

            $extension = end($file_array);
            if ($extension == 'csv') {
                $file_data = fopen($request->file->getRealPath(), 'r');
                $file_header = fgetcsv($file_data);

                $html .= '<table class="table table-bordered"><tr>';
                for ($count = 0; $count < count($file_header); $count++) {
                    $html .= '
                                <th>
                                    <select name="set_column_data" class="form-control set_column_data" data-column_number="' . $count . '">
                                        <option value="">Set Column Data</option>
                                        <option value="name">Name</option>
                                        <option value="email">Email</option>
                                        <option value="phone">Phone</option>
                                        <option value="subject">Subject</option>
                                        <option value="notes">Notes</option>
                                    </select>
                                </th>
                                ';
                }
                $html .= '</tr>';
                $html .= '<tr>';
                for ($count = 0; $count < count($file_header); $count++) {
                    $html .= '<td>' . $file_header[$count] . '</td>';
                }
                $html .= '</tr>';

                $limit = 0;
                $temp_data = [];
                while (($row = fgetcsv($file_data)) !== false) {
                    $limit++;
                    $html .= '<tr>';
                    for ($count = 0; $count < count($row); $count++) {
                        $html .= '<td>' . ($row[$count] ?? '') . '</td>';
                    }
                    $html .= '</tr>';

                    // Ensure we store as array, not object
                    $temp_data[] = array_values((array)$row);

                    if ($limit > 100) {
                        break;
                    }
                }
                $html .= '</table>';
                fclose($file_data);

                // Store as array in Laravel session
                session(['contact_import_data' => $temp_data]);
            } else {
                $error = 'Only <b>.csv</b> file allowed';
            }
        } else {
            $error = 'Please Select CSV File';
        }

        $output = array(
            'error' => $error,
            'output' => $html,
        );

        return json_encode($output);
    }

    public function fileImportModal()
    {
        $user = \Auth::user();
        $pipelines = \App\Models\Pipeline::where('created_by', $user->creatorId())->get();

        return view('contacts.import_modal', compact('pipelines'));
    }

    public function contactImportdata(Request $request)
    {
        try {
            $user = \Auth::user();

            // Get CSV data from session
            $csvData = session('contact_import_data');
            if (!$csvData) {
                return response()->json(['success' => false, 'message' => 'No CSV data found']);
            }

            // Force convert to simple array
            $csvData = json_decode(json_encode($csvData), true);

            $imported = 0;

            foreach ($csvData as $row) {
                $row = array_values($row); // Force to indexed array

                $name = $row[0] ?? '';
                $email = $row[1] ?? '';
                $phone = $row[2] ?? '';
                $subject = $row[3] ?? 'Import';
                $notes = $row[4] ?? '';

                if (empty($name)) continue;

                \App\Models\Lead::create([
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone,
                    'subject' => $subject,
                    'notes' => $notes,
                    'user_id' => $user->id,
                    'pipeline_id' => 1,
                    'stage_id' => 1,
                    'sources' => 'Import',
                    'created_by' => $user->creatorId(),
                    'date' => now()->format('Y-m-d'),
                    'is_active' => 1,
                    'order' => 0,
                    'products' => '',
                    'labels' => ''
                ]);

                $imported++;
            }

            session()->forget('contact_import_data');

            return response()->json([
                'success' => true,
                'message' => "Imported {$imported} contacts successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage()
            ]);
        }
    }
}
