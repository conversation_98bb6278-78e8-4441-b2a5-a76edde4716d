{"__meta": {"id": "Xd6359597b3eaff8931e2fda85a241ffa", "datetime": "2025-07-31 04:43:13", "utime": 1753936993.038794, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[04:43:13] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753936993.031839, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753936991.492599, "end": 1753936993.038822, "duration": 1.5462229251861572, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1753936991.492599, "relative_start": 0, "end": **********.866474, "relative_end": **********.866474, "duration": 1.3738749027252197, "duration_str": "1.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.866533, "relative_start": 1.373934030532837, "end": 1753936993.038825, "relative_end": 3.0994415283203125e-06, "duration": 0.17229199409484863, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614208, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": 1753936993.00691, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.04148, "accumulated_duration_str": "41.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.921129, "duration": 0.010199999999999999, "duration_str": "10.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 24.59}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.9476612, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 24.59, "width_percent": 1.953}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.95437, "duration": 0.02918, "duration_str": "29.18ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 26.543, "width_percent": 70.347}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.989662, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 96.89, "width_percent": 3.11}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-952582046 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-952582046\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1956805178 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1956805178\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-47903714 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-47903714\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2013248048 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImNMVkdLOXBTVTI0T3h6NHA0VFQzcWc9PSIsInZhbHVlIjoicjM0TkQ1VHZxeHIrK3NVeDQrcTZHKzZMTWpDNUova0c3NjVRUVNSeFl4T3d5N0lDQnhZVS85azdkR2R4YnNQNWZ2ZkhDNE4zUlFmdnV3VTZkMzJlSGtoNDFKdXZUVlBMTm1Ld01VdDZKQk5QZVVyWFZHN0VXY0lxU2hWWFUrYit4bDZONWtNaDBpK1B6TDZnaXVlalhHdmJKVktqT3N5czVIZTV1OEZrUXovMzJtaWoxU1ZNOG5CZWtjVmZ2U1pGamJ6dTBkK0NnM3FwR0FBbHhldGJSMW83Vk1VRlExVW1Fb2tBQXZqYVVpKzNJNTNHUXR0NmhwcXR2Zm9Za3Q3cFdyWVFKQU41b2Erc1dMUmlyWTlkenY4U2NScklBWVZiRFE3NnJ3Nyt5TnZOMGx1azZCclM4b0ovelBaMzEvWTBCZHZJbCs1RG5vQnphRnFhclRWclowUUhRYm96NXozejVjQjdtcWhyeFJSSytFQTZFLzhkUXZzZkZPT1I1RXJaeU1FWlkzb3VuN2EvdHlWUWRNdERyTml3T2V3RHZ1ZUpTTjR4YlhabDRWS3VuQ0FVSlN5amVROWR5VkF1MmgvRGZEY1lZT1VEN1FZVjBMUEMvZFVydDJTZGJtb1ByMGp1bWZxbFoxUkdlaWF5dS9BbDVSQ0MyMTFmVTF0bFk0bkMiLCJtYWMiOiJmNzljMGRiYzI0MzAxMDJjZWI0YmFjZDE3ZGYyZGRlODg0MDdiM2FkMzNiOThjMmIxZDA0MzM5MzhkMzFjODlhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkFXa2J1Z1lMTGhnMVNHWDNudGtkeFE9PSIsInZhbHVlIjoiUzRGUDJkQXBHTHREbjUweG9VVmU5NDVKY1NkSGdKQkRrZGhkMEx5NVo5R3d5QUdGRTVOZ2M0c2VMNmI2bU5LUEpYTmVkakdlbUVHYlpaOXlwZmFWNFRHOXFaQlg3QndxNnJUQ1oydEo5UEdIRkYxaWZwS1J5UVJnMEdrd2tPZm5EWHduT2NjaytVV2ZzM1Y0TEFPZ0hLZ09ZRnpSYXoybXI0RDZGUnkzblZMTWxiNFZQTmJEcnduU3ZJeWwwY1ZXWS9aL3lHYnowZ1d3dzVVZDd1aDJhSi9OSkYxTEhDd3JXMUxXeldVYWo2SXptSkcva2N2L1hHOENjYnlmMXNpdjJNVjB6bUI1emE0elp5d2RNLzcvclJLcXdpSGxEVUorL0l5bDh2Y1Z0eFhnRGl3WjRLMU1ra2VFTXZVd2llRU9XMW9JR1I2cXRvWllLUTgvWUI5V1h5aHBFN3VpUWJOMlUvV01kYkVEdjVJN21OQ09xYnpyVzFoQmhvMUNsQis2Z2I4ZEhaTmZ3bHo4ZFFJcE1qd2dmZkx2L3paMnZVZGxoZzdaczNMdzZJRDFuS3krdmhFQUc2QlpZVHRib25BSnlIK2tuQ2NhNFY2b3p6Z0thVGxKYkdVU0RSOG5EclVWVEVQT0xrVm1Ieit6c29udDNObzNVa0xkTjZpenUxM0giLCJtYWMiOiI0ZDYxZDAzZTIyODA0ZTkzYmQ0M2M1ZWJlNTNkOTUzN2ViMjhhNmZkN2JlYTM3OTM5OTQ3Y2RlODlhMjY4OWJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013248048\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-902234886 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UVeYwvySKA5PPFAkzc6iMcw1VaHAuVegcIgnjUUq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-902234886\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2126807804 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:43:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVudEpaNythTUZHV054T0xyajM2TXc9PSIsInZhbHVlIjoicGZHRThibG1TeEpkd1VFaVlNTjZKcDIrSSt6UGJQelFNRDdxUjdLTGpDQVFuSGxhVXkwY3dkNmhjL3FTb1JmNEVyQ0N6K2xaNDZPRE82bkFEMDFPRFQ0bkkxNDhJUXdaa3dsQmxQcmxGOXRBQjRONVlUalFUT21RWDdNdi8xUDR6Q0J2UDF6dUdtbFY5M0t2SE9uZ0F6NGJ0VVhrL25KdTVDSW5yZjlMdlFQVkliTzdCS3RNNW11NklGcHNVaUN0QUxDRjBZTGxhcTVJenJQb1Q1bTB4dlN1TmxwTGllWkNrM1lFVWw5Zm9qd0lZaFZ3ODJtRmJIYWUvaUVxRTBCSU9hNEh1RCs1ZHJBbVA3aEhNbHdPdFdRMG9rRkE5UVJQYXArbnZlekVuZVErcUZ5UHFGcklWVU4xYURKWHBabDBhTjE1cnpkczVnZk1DbnRqODdLbzBidlcrbHBKeGREM285LzlNMGUwaUVlczJBZnI3SjhQRVdQeVUvYlpxdWRJWUJPNWVxelJwV2orMXJ3YUVaOEdXL0p1OWkxNVNpWU1GRjVUNHBuSUhOTmorYWRMc1IvQU5qMHJId3N2SU92cnhOUmRBdm5JdkZvZThtc0RpUGFQRmRkVFNMZzIxaWl6WWZ4emIwcjFVMExjc2ZETm9UQlV6emswcStQWVpYdFciLCJtYWMiOiJhZjFkM2Y1YWY3ODE5ODVlNDhhZDFkMzg5MTEzYmY3ZTE1YjA4MzdlMzhjNTdlN2E4MDFkNjgzNjA2MDI0MjViIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InV0djk2RDhPakc1WENjUUZBUGU3a2c9PSIsInZhbHVlIjoiVW4zc25RaGpLTkRtVWdSRzFXbVJNMFVKMC9YK1B0eFJqL3FTeHhWTE01M1cydVQydmVXYk90a2xMZk42b2hMdWRmTVJuRStYWFZmTHBjam13Q0tUVm5HcWdaUUNlUjJFeDl1alhIa281NzBaVmhUY0IyRDdqVHQrd2p2REpEeVhxS2RlZC9jQ1BqQ1ZidVdUK000RUJhTnZ2OXhJWGVxczEwN3QyR1lGVUM5Mmo4K3pIRkRUNVJ2Q3pVQjYvL0N2MXJ5NU1WamIyd3hNSDFSU1BaOUJFZENVdUR3ZFp3Z0lkK0JJREd3Zlp5QnFGV0dweVZZd0gvK28zeHJzckk4RDV0WFE1K3VzdjFYaWNTSmZKRVBDeGkxdzRYOUp6a3U2RkhtV3haQUIzRjE1YWp1OHBlWHhLL2pFQ2tFNmxmdngxczFqZVEwNys1SlVqUFhrYUd5WjN1d20wUndvNmZGcFlIZllQN0d1RjM2bzE3SlVrcUU1Y2dndWh4ZWRWTmY3WGN3VW5YL2FuTUd5czdZdWhnanV5Rm9uRDUvckNyTFJOZFJ3YVhPa0RpV2RlY2JJQjBOczZIdTBRZXdDR1dQYzNmR3c4K29IVGR6VEZXdE83NkR2ekxFT29kbXlUSC9ZcVYxc2tBMWxaa1A1ZlRWTWlEalFHY3lHOE5GZVRLWWYiLCJtYWMiOiI1N2RlYjk2ZTFmNWJlZTJiNjIxYjViOTllNWVkMWU0NjVjMTM2NjliNDViMWRmYzA3YTM3ZjgzM2JhNjdlMzU5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVudEpaNythTUZHV054T0xyajM2TXc9PSIsInZhbHVlIjoicGZHRThibG1TeEpkd1VFaVlNTjZKcDIrSSt6UGJQelFNRDdxUjdLTGpDQVFuSGxhVXkwY3dkNmhjL3FTb1JmNEVyQ0N6K2xaNDZPRE82bkFEMDFPRFQ0bkkxNDhJUXdaa3dsQmxQcmxGOXRBQjRONVlUalFUT21RWDdNdi8xUDR6Q0J2UDF6dUdtbFY5M0t2SE9uZ0F6NGJ0VVhrL25KdTVDSW5yZjlMdlFQVkliTzdCS3RNNW11NklGcHNVaUN0QUxDRjBZTGxhcTVJenJQb1Q1bTB4dlN1TmxwTGllWkNrM1lFVWw5Zm9qd0lZaFZ3ODJtRmJIYWUvaUVxRTBCSU9hNEh1RCs1ZHJBbVA3aEhNbHdPdFdRMG9rRkE5UVJQYXArbnZlekVuZVErcUZ5UHFGcklWVU4xYURKWHBabDBhTjE1cnpkczVnZk1DbnRqODdLbzBidlcrbHBKeGREM285LzlNMGUwaUVlczJBZnI3SjhQRVdQeVUvYlpxdWRJWUJPNWVxelJwV2orMXJ3YUVaOEdXL0p1OWkxNVNpWU1GRjVUNHBuSUhOTmorYWRMc1IvQU5qMHJId3N2SU92cnhOUmRBdm5JdkZvZThtc0RpUGFQRmRkVFNMZzIxaWl6WWZ4emIwcjFVMExjc2ZETm9UQlV6emswcStQWVpYdFciLCJtYWMiOiJhZjFkM2Y1YWY3ODE5ODVlNDhhZDFkMzg5MTEzYmY3ZTE1YjA4MzdlMzhjNTdlN2E4MDFkNjgzNjA2MDI0MjViIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InV0djk2RDhPakc1WENjUUZBUGU3a2c9PSIsInZhbHVlIjoiVW4zc25RaGpLTkRtVWdSRzFXbVJNMFVKMC9YK1B0eFJqL3FTeHhWTE01M1cydVQydmVXYk90a2xMZk42b2hMdWRmTVJuRStYWFZmTHBjam13Q0tUVm5HcWdaUUNlUjJFeDl1alhIa281NzBaVmhUY0IyRDdqVHQrd2p2REpEeVhxS2RlZC9jQ1BqQ1ZidVdUK000RUJhTnZ2OXhJWGVxczEwN3QyR1lGVUM5Mmo4K3pIRkRUNVJ2Q3pVQjYvL0N2MXJ5NU1WamIyd3hNSDFSU1BaOUJFZENVdUR3ZFp3Z0lkK0JJREd3Zlp5QnFGV0dweVZZd0gvK28zeHJzckk4RDV0WFE1K3VzdjFYaWNTSmZKRVBDeGkxdzRYOUp6a3U2RkhtV3haQUIzRjE1YWp1OHBlWHhLL2pFQ2tFNmxmdngxczFqZVEwNys1SlVqUFhrYUd5WjN1d20wUndvNmZGcFlIZllQN0d1RjM2bzE3SlVrcUU1Y2dndWh4ZWRWTmY3WGN3VW5YL2FuTUd5czdZdWhnanV5Rm9uRDUvckNyTFJOZFJ3YVhPa0RpV2RlY2JJQjBOczZIdTBRZXdDR1dQYzNmR3c4K29IVGR6VEZXdE83NkR2ekxFT29kbXlUSC9ZcVYxc2tBMWxaa1A1ZlRWTWlEalFHY3lHOE5GZVRLWWYiLCJtYWMiOiI1N2RlYjk2ZTFmNWJlZTJiNjIxYjViOTllNWVkMWU0NjVjMTM2NjliNDViMWRmYzA3YTM3ZjgzM2JhNjdlMzU5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126807804\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1854788570 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1854788570\", {\"maxDepth\":0})</script>\n"}}