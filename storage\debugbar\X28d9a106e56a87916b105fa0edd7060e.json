{"__meta": {"id": "X28d9a106e56a87916b105fa0edd7060e", "datetime": "2025-07-31 04:48:29", "utime": **********.482329, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937308.580262, "end": **********.482367, "duration": 0.9021050930023193, "duration_str": "902ms", "measures": [{"label": "Booting", "start": 1753937308.580262, "relative_start": 0, "end": **********.409323, "relative_end": **********.409323, "duration": 0.8290610313415527, "duration_str": "829ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409339, "relative_start": 0.****************, "end": **********.482371, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "73.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9IPJDbLK14yKrf4r9c8Q6Oud5cTEq8QMb6A157QE", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1881429050 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1881429050\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1151028745 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1151028745\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1116659049 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1116659049\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1882818416 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882818416\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1534225841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1534225841\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-598083876 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVHaWNuNzRqazQ3ZjVxSTJDMlIvaVE9PSIsInZhbHVlIjoic004UlJyY1cyd2FWeWxWeGkwRTdFY25MSSsvQnZXa1k3TnREMTJsaFp1V2JZNXNORkx5dW8zNmkwRHFnVlp1bDhwcEZ4V2FHU0k2ekJ3eisrV1NRUEdnREM4REN6QXVERlVEc2w0dkp3SXNyREZTQTlBdTF2dmVCVlA1UXRJa2xoYWtTb2pHUE8vYlVZVU1SZG1rRDlhamU0bCtsdW9tVDlHODdpWnU0b0lPRHBzU2RGZnFpL2RlRml3US9UWklYL0Nicm82RUYrRVZBNXB1Q1VKRy9zdDRqRXV4T1ZzS3p6TkRrSTRuUStrZ3RHdEMrcEpiSW1yb1dwc3E3SlUzNk1IakVKWWdnMlhBaTJMczdXRE1WbDN3WHVwNFpQUjlRL3liVyt4NzZ4OEpkOEVsVWkyU3B6aEU2NjRoMDVmanVSMGRvQTAxS3ZKbXdva1Mra3BPd3VJbzJwaFlFUVZVT2ovT2p5SHVtM3RuMGxVUzc0eHNOSTFFeGgyK0pKUEk2d2JVeWx2WUVHY2NPRnpjZW9ramJZY3V3ZEdwK0V2cVZSMDh0MzhnbllKZkdGUVkyZkg1YkN0aGlGVGJ4bEk5MUU4QU1OMkY5bHllQW4rY1F3cEZFVG95SC9sVnp2WW13ZjFiZ0R6ektubXNhZ3llbmViOXNBS0llWFB1elAxMFQiLCJtYWMiOiJkYTJjYWE2MTI5OTc5ZDczNjQxZTZmYTI2NjFjNzc4NTU2MjZhYWM1ZmY4NjUxZDcyMGFkOTUwMzVmMzQyZGI1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InN6VVhuRjhKbisyajRLQVUyRDdjeUE9PSIsInZhbHVlIjoiQWVoZ3gxZXBjNUdmL3dMNitNN2hJemdMRVYyV0x5OU5lNkVVUFFxTFRuOFlJR01ySEhLVFJGOWlCK29EYThISG8yRDZtVzlVYXphQ3BxNUovQkNoVjI4NitJOU0vemdkYlZYSUJTYnpGdnVkMkh3UlhNai9RR1BvSDhxb3Z6SlM5MUdpRy9XZVFNTVRpclp4Mzc5N1FINDZXZmRkc0oxcmN1TStBbGUrNEp5aXBnUWQzdldWTjVOK2IyRXhTdy9SczF5N3lPaVg2VnRuaGhNemQ3Sy9nNGdqMXJoWlNTY1VxNTc2NFhzaUl2bVE2c2RrZEdlOFNic2YzM3FWUG5zbzFRaWNOdmttVHBCNTJHU3VsdlJRdkc2SlBYT1hpNmM4bnRpdWVybXpiTjJnc0kySXpJR0FaL0dTVFRwb01OWnl5SzBUOUF5OHdmdzlncXJoTGwrM2NNNlZ1SUoxK3NMc2t1MXg3bHQrbEpaRk9KeWJnWEgyQitFdzB4NnNyQzM1bmk3dlVzN3BCcjZFeVl5S0c2V2FidTFOSVlHVFVuS2l5UmZtZmZ3M0djQXRscW5iY3NZMDl1K3V6b3hpS2VOS0ZObmpNd3lUdXp4OWV2d2w4cFc1OHJiWk5TVWl5cnFLcDhqNE9XQ1VXRGFRZGVCUTQ0SWZHSDNnUUp2Yk5EOXgiLCJtYWMiOiI2MDdhMDM3NWUwYjRjZjIxMmM3OThmYzZlYTI0Yjg2YmY4MjI2ZGMwMzY5Yzc5NmFmNzdkZGRiZmIzYjc3Mjk1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVHaWNuNzRqazQ3ZjVxSTJDMlIvaVE9PSIsInZhbHVlIjoic004UlJyY1cyd2FWeWxWeGkwRTdFY25MSSsvQnZXa1k3TnREMTJsaFp1V2JZNXNORkx5dW8zNmkwRHFnVlp1bDhwcEZ4V2FHU0k2ekJ3eisrV1NRUEdnREM4REN6QXVERlVEc2w0dkp3SXNyREZTQTlBdTF2dmVCVlA1UXRJa2xoYWtTb2pHUE8vYlVZVU1SZG1rRDlhamU0bCtsdW9tVDlHODdpWnU0b0lPRHBzU2RGZnFpL2RlRml3US9UWklYL0Nicm82RUYrRVZBNXB1Q1VKRy9zdDRqRXV4T1ZzS3p6TkRrSTRuUStrZ3RHdEMrcEpiSW1yb1dwc3E3SlUzNk1IakVKWWdnMlhBaTJMczdXRE1WbDN3WHVwNFpQUjlRL3liVyt4NzZ4OEpkOEVsVWkyU3B6aEU2NjRoMDVmanVSMGRvQTAxS3ZKbXdva1Mra3BPd3VJbzJwaFlFUVZVT2ovT2p5SHVtM3RuMGxVUzc0eHNOSTFFeGgyK0pKUEk2d2JVeWx2WUVHY2NPRnpjZW9ramJZY3V3ZEdwK0V2cVZSMDh0MzhnbllKZkdGUVkyZkg1YkN0aGlGVGJ4bEk5MUU4QU1OMkY5bHllQW4rY1F3cEZFVG95SC9sVnp2WW13ZjFiZ0R6ektubXNhZ3llbmViOXNBS0llWFB1elAxMFQiLCJtYWMiOiJkYTJjYWE2MTI5OTc5ZDczNjQxZTZmYTI2NjFjNzc4NTU2MjZhYWM1ZmY4NjUxZDcyMGFkOTUwMzVmMzQyZGI1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InN6VVhuRjhKbisyajRLQVUyRDdjeUE9PSIsInZhbHVlIjoiQWVoZ3gxZXBjNUdmL3dMNitNN2hJemdMRVYyV0x5OU5lNkVVUFFxTFRuOFlJR01ySEhLVFJGOWlCK29EYThISG8yRDZtVzlVYXphQ3BxNUovQkNoVjI4NitJOU0vemdkYlZYSUJTYnpGdnVkMkh3UlhNai9RR1BvSDhxb3Z6SlM5MUdpRy9XZVFNTVRpclp4Mzc5N1FINDZXZmRkc0oxcmN1TStBbGUrNEp5aXBnUWQzdldWTjVOK2IyRXhTdy9SczF5N3lPaVg2VnRuaGhNemQ3Sy9nNGdqMXJoWlNTY1VxNTc2NFhzaUl2bVE2c2RrZEdlOFNic2YzM3FWUG5zbzFRaWNOdmttVHBCNTJHU3VsdlJRdkc2SlBYT1hpNmM4bnRpdWVybXpiTjJnc0kySXpJR0FaL0dTVFRwb01OWnl5SzBUOUF5OHdmdzlncXJoTGwrM2NNNlZ1SUoxK3NMc2t1MXg3bHQrbEpaRk9KeWJnWEgyQitFdzB4NnNyQzM1bmk3dlVzN3BCcjZFeVl5S0c2V2FidTFOSVlHVFVuS2l5UmZtZmZ3M0djQXRscW5iY3NZMDl1K3V6b3hpS2VOS0ZObmpNd3lUdXp4OWV2d2w4cFc1OHJiWk5TVWl5cnFLcDhqNE9XQ1VXRGFRZGVCUTQ0SWZHSDNnUUp2Yk5EOXgiLCJtYWMiOiI2MDdhMDM3NWUwYjRjZjIxMmM3OThmYzZlYTI0Yjg2YmY4MjI2ZGMwMzY5Yzc5NmFmNzdkZGRiZmIzYjc3Mjk1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-598083876\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-634482157 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9IPJDbLK14yKrf4r9c8Q6Oud5cTEq8QMb6A157QE</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634482157\", {\"maxDepth\":0})</script>\n"}}