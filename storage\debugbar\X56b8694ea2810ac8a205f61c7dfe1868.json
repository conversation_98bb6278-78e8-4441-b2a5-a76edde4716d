{"__meta": {"id": "X56b8694ea2810ac8a205f61c7dfe1868", "datetime": "2025-07-31 04:54:16", "utime": **********.042683, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937655.072262, "end": **********.04271, "duration": 0.9704480171203613, "duration_str": "970ms", "measures": [{"label": "Booting", "start": 1753937655.072262, "relative_start": 0, "end": 1753937655.923874, "relative_end": 1753937655.923874, "duration": 0.851611852645874, "duration_str": "852ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753937655.923895, "relative_start": 0.***************, "end": **********.042713, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "RJdnC6cSkrpLGuX1xeiuuaM6PXf8DMkdJSliSxsu", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1700499565 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1700499565\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1174298244 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1174298244\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-194323860 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-194323860\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1400190619 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400190619\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-493259497 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-493259497\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:54:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii94SnhoNmluR2xVT1Y4NnZnVkp4TUE9PSIsInZhbHVlIjoiTy81TlVRYzlyeHVhaUFWc1FuV3h5UkpzYkFhd1o3WUk2aTlpa0dhT1BDbG5jeXBCSW81cEdvTWx0eUdEMTBaM0dzK1JobFVWZ2hNdkRNK3pBR3d1S0lvMW9VSGdiM2ZzR0VINU5VOVYwM2l4TGk1MVI0OU9jNmd1YmVnV0FwTmxLQzlIMGlhOGhuQk9aeDN4T0hXY1lUS2V4U2swUHVkM2dQemNjMTZrRWhGMkRTR3VaSWdUSmdrVUNhUTNSdGgxeUd6ck8zemxSQ2FPY0laK3RESUUrenBxY3FiQUcwY2dGYlZaYlRDekpydUxIam5keklTL3M2VDdJUERoYVZhK3FSVlRRN3kvUVVzZTFBMXZkTjNFdFVtKzA1WEtmd3cxYkwxTWNHTDBDQXZJaVprOUxKYTJ2VHB6OERmZ0Z1MjNDNmVDclhuL1c2OG1mYlo2aSt4WDNKcU83WlV6RWhqOXZYOFhpamdoTkJQd0RDUnlrRGhvOXFZVFNpOHdzNGxFNVlkRXpyc1dMQWc0aDY2K05ZVmhic2RoRVAvUlV2dHdmNlh4S1ZRWGQyMmZEeE1seHJ6aW5TS0dGdW54MjlrYVBpTERNMUQ0bnFxWjFzc3RLTEF3NSsyNENWaXdrcnBob0dMNk5tNEhqRjFLQWNKbEJLOFQ4TVB3V3NpVlo5eDIiLCJtYWMiOiJkYTY5MzcyOGU5NWUxNzc3MjllZDRkZjE4MDQzNTZkYWQ3ZWQ5NWZmNTdjNzdiNzZjNjcwYTRiNGI3MmU2YWYzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:15 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImZNeWl1ZmRva3MyNUl5cGVWUnh3ZEE9PSIsInZhbHVlIjoiUzBjQmg2U0QrQW1UZ2hNdjhLUlhZUVdJYlJCNEpUb0tWN1dEVGl2Vm9uMGw0dWtMNmplNmQ1OUpJa01FZVNGS1NnejRmRUcvS1U0VXlRZmQrSnJmcjVBam1IVzVsTG9vMUdEWnhSdVdxWHlDcXNOc2VEdlN4Z3dTdkw2VkdDNUJBRHl6Mk9tUnNWNnhRWEVOZmUwT20vMEdMSERvalluY2MweXlVVTFPQ0R0dVhzNE41Vm4ydEErM29DbnZidzVLemY5d2cyMXdHbTgxenRGVWE4dk1GNkhlQVNyNU80Zmlaa2hRWXYxZUFaRE1kTG03UzFqejhLdTJiUWJ6Ynd3Tk5XeDA3YUlpTnZTVUZvZEhtc253M2JONXJWdlpSQnRpMEMzNG5MeVNGT0F3U1FQWHJiVERaVDdtWXlmY3R3Nno5c1hpVmpRVTBoWkJteGwvZkd5ZGc0RUlzaWd0aGwyR0N6N3JUVXVxdXhpd0lLb3lZZHQvWXpyZGd5UEg5d2hPNW01N0haZHlWUzRFdWtkSzV3aSsralR6TGcxcGhIU2x5dEJYV0dRV2JuQlZCcEM5Szl0Ui94eElkL2M3VDlyTEQ4SjJzZlVkU0tYSjBEU1lpN1h0VzJ0ajBYcFYyY3ltQnpON3ZicVFpUlFaaG1xc1RsWTJicU9pNTVYK2QyWTgiLCJtYWMiOiI4ZTExMTVkODA0NDY2Y2NhNTNhMmE4Y2QzZjdlYmUyZTM4Mzk4ZDE2MmY2MWVhZDEyNzhkNjRjM2RjZWYwYWNiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:15 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii94SnhoNmluR2xVT1Y4NnZnVkp4TUE9PSIsInZhbHVlIjoiTy81TlVRYzlyeHVhaUFWc1FuV3h5UkpzYkFhd1o3WUk2aTlpa0dhT1BDbG5jeXBCSW81cEdvTWx0eUdEMTBaM0dzK1JobFVWZ2hNdkRNK3pBR3d1S0lvMW9VSGdiM2ZzR0VINU5VOVYwM2l4TGk1MVI0OU9jNmd1YmVnV0FwTmxLQzlIMGlhOGhuQk9aeDN4T0hXY1lUS2V4U2swUHVkM2dQemNjMTZrRWhGMkRTR3VaSWdUSmdrVUNhUTNSdGgxeUd6ck8zemxSQ2FPY0laK3RESUUrenBxY3FiQUcwY2dGYlZaYlRDekpydUxIam5keklTL3M2VDdJUERoYVZhK3FSVlRRN3kvUVVzZTFBMXZkTjNFdFVtKzA1WEtmd3cxYkwxTWNHTDBDQXZJaVprOUxKYTJ2VHB6OERmZ0Z1MjNDNmVDclhuL1c2OG1mYlo2aSt4WDNKcU83WlV6RWhqOXZYOFhpamdoTkJQd0RDUnlrRGhvOXFZVFNpOHdzNGxFNVlkRXpyc1dMQWc0aDY2K05ZVmhic2RoRVAvUlV2dHdmNlh4S1ZRWGQyMmZEeE1seHJ6aW5TS0dGdW54MjlrYVBpTERNMUQ0bnFxWjFzc3RLTEF3NSsyNENWaXdrcnBob0dMNk5tNEhqRjFLQWNKbEJLOFQ4TVB3V3NpVlo5eDIiLCJtYWMiOiJkYTY5MzcyOGU5NWUxNzc3MjllZDRkZjE4MDQzNTZkYWQ3ZWQ5NWZmNTdjNzdiNzZjNjcwYTRiNGI3MmU2YWYzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImZNeWl1ZmRva3MyNUl5cGVWUnh3ZEE9PSIsInZhbHVlIjoiUzBjQmg2U0QrQW1UZ2hNdjhLUlhZUVdJYlJCNEpUb0tWN1dEVGl2Vm9uMGw0dWtMNmplNmQ1OUpJa01FZVNGS1NnejRmRUcvS1U0VXlRZmQrSnJmcjVBam1IVzVsTG9vMUdEWnhSdVdxWHlDcXNOc2VEdlN4Z3dTdkw2VkdDNUJBRHl6Mk9tUnNWNnhRWEVOZmUwT20vMEdMSERvalluY2MweXlVVTFPQ0R0dVhzNE41Vm4ydEErM29DbnZidzVLemY5d2cyMXdHbTgxenRGVWE4dk1GNkhlQVNyNU80Zmlaa2hRWXYxZUFaRE1kTG03UzFqejhLdTJiUWJ6Ynd3Tk5XeDA3YUlpTnZTVUZvZEhtc253M2JONXJWdlpSQnRpMEMzNG5MeVNGT0F3U1FQWHJiVERaVDdtWXlmY3R3Nno5c1hpVmpRVTBoWkJteGwvZkd5ZGc0RUlzaWd0aGwyR0N6N3JUVXVxdXhpd0lLb3lZZHQvWXpyZGd5UEg5d2hPNW01N0haZHlWUzRFdWtkSzV3aSsralR6TGcxcGhIU2x5dEJYV0dRV2JuQlZCcEM5Szl0Ui94eElkL2M3VDlyTEQ4SjJzZlVkU0tYSjBEU1lpN1h0VzJ0ajBYcFYyY3ltQnpON3ZicVFpUlFaaG1xc1RsWTJicU9pNTVYK2QyWTgiLCJtYWMiOiI4ZTExMTVkODA0NDY2Y2NhNTNhMmE4Y2QzZjdlYmUyZTM4Mzk4ZDE2MmY2MWVhZDEyNzhkNjRjM2RjZWYwYWNiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RJdnC6cSkrpLGuX1xeiuuaM6PXf8DMkdJSliSxsu</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}