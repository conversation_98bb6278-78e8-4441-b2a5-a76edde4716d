{"__meta": {"id": "X2323ec16801f62aa2ecc5b7eb63ca4b0", "datetime": "2025-07-31 04:48:33", "utime": **********.502424, "method": "POST", "uri": "/chats/getContacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[04:48:33] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/getContacts\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.495597, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753937312.472842, "end": **********.502449, "duration": 1.0296070575714111, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1753937312.472842, "relative_start": 0, "end": **********.342053, "relative_end": **********.342053, "duration": 0.869210958480835, "duration_str": "869ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.342071, "relative_start": 0.8692290782928467, "end": **********.502452, "relative_end": 2.86102294921875e-06, "duration": 0.16038084030151367, "duration_str": "160ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50614208, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x vendor.Chatify.layouts.listItem", "param_count": null, "params": [], "start": **********.473576, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/listItem.blade.phpvendor.Chatify.layouts.listItem", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FlistItem.blade.php&line=1", "ajax": false, "filename": "listItem.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.Chatify.layouts.listItem"}]}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03599, "accumulated_duration_str": "35.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.397735, "duration": 0.004690000000000001, "duration_str": "4.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 13.031}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.4194062, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 13.031, "width_percent": 2.278}, {"sql": "select * from `ch_messages` inner join `users` on `ch_messages`.`from_id` = `users`.`id` or `ch_messages`.`to_id` = `users`.`id` where `ch_messages`.`from_id` = 7 or `ch_messages`.`to_id` = 7 order by `ch_messages`.`created_at` desc", "type": "query", "params": [], "bindings": ["7", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.424526, "duration": 0.02961, "duration_str": "29.61ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:340", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:340", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=340", "ajax": false, "filename": "MessagesController.php", "line": "340"}, "connection": "radhe_same", "start_percent": 15.31, "width_percent": 82.273}, {"sql": "select * from `users` where `type` != 'client' and `created_by` = 0 and `id` != 7 or `id` = 0", "type": "query", "params": [], "bindings": ["client", "0", "7", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 368}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.457563, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:368", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=368", "ajax": false, "filename": "MessagesController.php", "line": "368"}, "connection": "radhe_same", "start_percent": 97.583, "width_percent": 2.417}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/system-admin/companies\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-531265627 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-531265627\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-710991662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-710991662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1926470933 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926470933\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1467236481 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhsNG1venRZa0VhRU40WHdmUTFHcnc9PSIsInZhbHVlIjoiRWJiVTFOUkE2SG1nU3lBNXJUMUM5aXpQaklNQVkwZjVyakR1V0hGaUFmaytSSUVpZHBrcUpxRDlob0Zkd25ZUmgyTGUxdFM1TnhObEhjR3gvaU1UNU4xSVRiSmYycXpUcnZhR053Y0lGMlZNNlg2R0hUcjZsNGdQZGVjVlZ3dnNDMnJvZnEvd2dndGhoNjQra1owVkxCSVFSNVpxbG5DTGRkL1JhaFZ4SGFpWWRZN0tQOFRWdUl6T0NqTjdQYzlKajEyaGVhaTZWai9QOTUraE13YnZpK3ZjQ1ZLSW5idjQxUEgydE5GVTN0b1N4dVYxVS9xMC9VSUkxL0xHWm9kQ0V2em0ydlprMmJWNDVXK2NUU2FjWXZXR2VseW5Fb0JlbVVSR1prYlhoVFBPUkdvTTlNeUF0cVBMZDNqcDVBUHNFcGpFdThqRmJWL0NEaVFwZ2lJZHRPR1RESEJTRG8xc2J6MCsrZjN0THRxUTFWOVdrdHIwdlhBa0x3KytZVUs2dWxwZHZ3enFnTkhhNUF0Q1YvcGNWalZCRTBDUWZEOFI0bTdTRStRK1FuRUZFUHcvT0hmVkh0bWhQeitDeXNqVFFlUWhUeURRazd4VzM4ZnF2OGFXWGhMcGNjL2tEM1Bha0hFdXlwNTArV040N3d4NnkvYnVNSS9wQkUrc1pZTnUiLCJtYWMiOiIyOTNhOWVlMWZmOTQ4NjczZDM4MjRmYzcwZjg0ZjY0M2Y4NDQ0NzAzODYwMWFiZGNlMjZlNzQ4NmU5NjY4MjVjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjViR005YTFyL1VnUlozTnpyUDZBZWc9PSIsInZhbHVlIjoiUlRyYmk0ZjJha3hMNzlhcGFlYUhhQWJuQ2J3ZnFvVCtNNVJEMzhwZUc5TldJNmRVUUdxNmoxTGJSTWpsdlhUcDNIME9ud0RVVEtLSmxBZXZ6b0k4aUlGU0dDRzl0TXFQV21mUlpzZEFvelE0eHN1ZzVUbVVvK1ZDejRRbk5PdkV0dDRBV0EwOTBhWk16SXdZejFUeVNrbGRyVW9vZ3UzV0I1Y3Y2WFVuVVZxTEpyUVd1MWtFcm94TCsycHBuV1VZVVB0MFZHaWdaK2JOMTJqRDFCdUxaNFlZZmNtNndtdWZ1Y1R2TUMrQ1RXZWNJcTl0S3A2NXRQcVduTDZCb1IrNFdJeHhCV1I4aXVoTmNHeE5KQXdNUDFkRHoyamVRRTFLMGNzRDJyUXZrOGVSTTFQbzZZa1BLbVgwUzE2a3pYNUQxUVN3NTF6T1F5bUZQNVJtMy9WaGxjUkNoNFFSVW50aC84elYwRTRqM2lrL0ZOQTlOenpJTEpIMnJTeHRmMGExbjVCQzVDNjJCdTJHUEE1aytSMzk0T0srVTV6dzVYLzdmWG1zVnFIbzJPaEgxdkFBVmtXaGxpRXlHV3EwWnJoWVhMQ0hGTGpyMGZjT3RLWjVQYkZRTlhGY3J0b3l6OGJ3R3grTEozeTAvSGhKVGFhbjN2SldjdDkrY2M5d0NLNjYiLCJtYWMiOiIzZmNjMGRiMWViOTg1NjU5ZWZmNGEyMTVhMjY2YjkxZmI3MTdiZmQ1MDY3Y2Y4Y2Q0NTlkOTUxMTlhYTQ5ZjcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467236481\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1335612147 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UVeYwvySKA5PPFAkzc6iMcw1VaHAuVegcIgnjUUq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335612147\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1480811483 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpqNUt5ejlTOHJNUjFuaUtPOXVyZFE9PSIsInZhbHVlIjoiRVFWK2NRZ0ZTc1pTb0tmeFc4eVU0bWIvaDQ4Qk9vd3pjRGZkUWp2bXNQWVYvc29PTCtWMUE0RDMzQy9rUEV5UEFHTFQ2L09TL3pHdndZWmNGeEtwTy9FQVFhandtYnRPb3loQnJSSG1Qc2thelpscjloVXl3bnVuNnJSUVVhQkcvTzBPN1FORUxYclhFZjVHWWUzSWxZaVBKQmQ0bjQrMkZmTnVpRG0vSDJzQUM3RmlEWkVsTDFQdHUvS3VnU0t2NGM1OWpkK3BpYkRnVjQxa1M4QktlcHlSYjI3SzRPT1N0RTN3cnlHS1FBSHlnWktWc2c3aEJWK1puK2JPWmZCbWc2UlFqakRXU3VCRVh4RjRLQ3BqMGd2R3I2TnJMWURkMHNZcTBENWJnL1dIdDB4citaVDhOV1lZVE11OThDRGM5Y0dWcm1kY3I4cDB6SXhuV1l1T2NMZkpjTEhTSjZrTFA0TnFQRFhRWUx3NVRSZ2NKZG5hK05oVmVHOUdlZUkyVW5tN1g5cmRjVHh4Tkk2RmF5RjF6R0M1MDBFR3lyRFFEZWpVMW1icHBXOTVTMGVaN3NCZkZITStXaWVHaFVPTHJtQVoyWUZkajlock9WVjhXcFdqQVBucWYvb2FBRzd6d05rK2xnVDU0dG1pdFRGQUNCb3ZwMVZvamRVeTlFcGQiLCJtYWMiOiJhMjg1OWIxMDQ4MGQzNWNmZDFlNTI0ZDk5ZDYxNTllNGM2N2ZlYzM0ZGRjZGUxYTAzODk4ZjRiNjVlYTk5OTBlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlMvSVFzL2ZHRmR0RnlpOVZVcGlhbGc9PSIsInZhbHVlIjoibTVxOVhVRHl6bEpFU0xTbndycCtVL2dDc1J1OVp6M1FYQU4raTN0UDBabnlMZTVxRWMyWW1BZGJXQ1U0MUVYUzNZY20wMVJ2cldlMkx2TU9uSUZKWlgrMUVVMjB1a1hBS1h4OWZRTVB5WkpzeUFLUDJHMGR1OEFYTDhJQmxaNW50S1BtUGVGMXliaGk0dUg1LzdRNGc4cjY5bnFFWXFjZ3FoMEl3QWlmd29LcUd2QUkzZ1FLbk9ScUxBR3NJNFlkb0xlWGxSbXk5L0Y3ZkRtdU1jWWw1aXloWVZua2ErZFloVmNLTFBqc1hqQ1RHeWx1VDNsZmZBbTM1TVRNaW54QUl6NW14ZjlydnR5bHcrSjFYSkJmckZyTDliejFLRU4wUW1aSHpCMVZTak9HdGFnb3VtdnVYUWp5anRrWG9PaTVuTkFLNEh1WUNHRjhjQ3BLL1d2WlQ0bjBmblVqRHIrQnZ1TWFBYmpLQ2wrbkRma0FFajZOUTMvRHBzRU9xNVB1cVlsNUNmYUY5QisreCs2MmlmVTFhUUdaSjlpS0FBNHVTQ09KaFA1RnFNNmYvWEZGeFBPR2RZbk5iTm5jb0JFOTdMY1AxWVdEVHVLL2dUVm42MWpMbk9tSGV4NVZPYk5MWUFPUkJKV1c2RXgvb3hpYlFDVmtwRUgyZ2JpSmE1T1ciLCJtYWMiOiIyNWZmMzBkOWEzNzE1ZWRlN2NlMzQ0NDE4MGUxZmJkMTMwYTc3MDI1ODJhNzg2OWZiNDQ0NjM0NDVjOGI5MjljIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpqNUt5ejlTOHJNUjFuaUtPOXVyZFE9PSIsInZhbHVlIjoiRVFWK2NRZ0ZTc1pTb0tmeFc4eVU0bWIvaDQ4Qk9vd3pjRGZkUWp2bXNQWVYvc29PTCtWMUE0RDMzQy9rUEV5UEFHTFQ2L09TL3pHdndZWmNGeEtwTy9FQVFhandtYnRPb3loQnJSSG1Qc2thelpscjloVXl3bnVuNnJSUVVhQkcvTzBPN1FORUxYclhFZjVHWWUzSWxZaVBKQmQ0bjQrMkZmTnVpRG0vSDJzQUM3RmlEWkVsTDFQdHUvS3VnU0t2NGM1OWpkK3BpYkRnVjQxa1M4QktlcHlSYjI3SzRPT1N0RTN3cnlHS1FBSHlnWktWc2c3aEJWK1puK2JPWmZCbWc2UlFqakRXU3VCRVh4RjRLQ3BqMGd2R3I2TnJMWURkMHNZcTBENWJnL1dIdDB4citaVDhOV1lZVE11OThDRGM5Y0dWcm1kY3I4cDB6SXhuV1l1T2NMZkpjTEhTSjZrTFA0TnFQRFhRWUx3NVRSZ2NKZG5hK05oVmVHOUdlZUkyVW5tN1g5cmRjVHh4Tkk2RmF5RjF6R0M1MDBFR3lyRFFEZWpVMW1icHBXOTVTMGVaN3NCZkZITStXaWVHaFVPTHJtQVoyWUZkajlock9WVjhXcFdqQVBucWYvb2FBRzd6d05rK2xnVDU0dG1pdFRGQUNCb3ZwMVZvamRVeTlFcGQiLCJtYWMiOiJhMjg1OWIxMDQ4MGQzNWNmZDFlNTI0ZDk5ZDYxNTllNGM2N2ZlYzM0ZGRjZGUxYTAzODk4ZjRiNjVlYTk5OTBlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlMvSVFzL2ZHRmR0RnlpOVZVcGlhbGc9PSIsInZhbHVlIjoibTVxOVhVRHl6bEpFU0xTbndycCtVL2dDc1J1OVp6M1FYQU4raTN0UDBabnlMZTVxRWMyWW1BZGJXQ1U0MUVYUzNZY20wMVJ2cldlMkx2TU9uSUZKWlgrMUVVMjB1a1hBS1h4OWZRTVB5WkpzeUFLUDJHMGR1OEFYTDhJQmxaNW50S1BtUGVGMXliaGk0dUg1LzdRNGc4cjY5bnFFWXFjZ3FoMEl3QWlmd29LcUd2QUkzZ1FLbk9ScUxBR3NJNFlkb0xlWGxSbXk5L0Y3ZkRtdU1jWWw1aXloWVZua2ErZFloVmNLTFBqc1hqQ1RHeWx1VDNsZmZBbTM1TVRNaW54QUl6NW14ZjlydnR5bHcrSjFYSkJmckZyTDliejFLRU4wUW1aSHpCMVZTak9HdGFnb3VtdnVYUWp5anRrWG9PaTVuTkFLNEh1WUNHRjhjQ3BLL1d2WlQ0bjBmblVqRHIrQnZ1TWFBYmpLQ2wrbkRma0FFajZOUTMvRHBzRU9xNVB1cVlsNUNmYUY5QisreCs2MmlmVTFhUUdaSjlpS0FBNHVTQ09KaFA1RnFNNmYvWEZGeFBPR2RZbk5iTm5jb0JFOTdMY1AxWVdEVHVLL2dUVm42MWpMbk9tSGV4NVZPYk5MWUFPUkJKV1c2RXgvb3hpYlFDVmtwRUgyZ2JpSmE1T1ciLCJtYWMiOiIyNWZmMzBkOWEzNzE1ZWRlN2NlMzQ0NDE4MGUxZmJkMTMwYTc3MDI1ODJhNzg2OWZiNDQ0NjM0NDVjOGI5MjljIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480811483\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1137360218 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137360218\", {\"maxDepth\":0})</script>\n"}}