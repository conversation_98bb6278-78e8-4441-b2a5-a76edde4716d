{"__meta": {"id": "X9d6cee7e561294f33f2cb19a295ac328", "datetime": "2025-07-31 04:43:09", "utime": **********.473634, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753936988.538598, "end": **********.47367, "duration": 0.9350719451904297, "duration_str": "935ms", "measures": [{"label": "Booting", "start": 1753936988.538598, "relative_start": 0, "end": **********.395384, "relative_end": **********.395384, "duration": 0.8567860126495361, "duration_str": "857ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.395403, "relative_start": 0.****************, "end": **********.473673, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "78.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0ALIZsEHdETDJ6zw1LvFSbDq5z0aNxTtPtmIBgcZ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-2022511925 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2022511925\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1014903663 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1014903663\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1146536168 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1146536168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1870396049 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1870396049\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1192061026 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1192061026\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1433148218 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:43:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkQ1ci9CblNZL3BPZkpZWjY0cTZ2MXc9PSIsInZhbHVlIjoiSWhueERhQXMvTXhYVW8yVVlBakRwZ1IzdVRwVTJWUDJzNEZTMUtTY3NHK1J6M1JPR09kc0gzYXZIb3Y4cHRhWGg0dURCV3FZREJadzdsdUVZdmJaOUF4V0RyTEl0SVFwalFFbVRmTFFoZUh6czBsbGpqTGpUUUNrb1ZyNW4zd1BTeVV6WXBlUDFSaHkrUlFXaVhCbVdsQXRkaHcvaGlGcmNzZ2JpTlYvMzFPTHRBSUQxcGtsMWozS2FtakZrMkVSWE9QdGpPMkx0TURCTVdxc01yNzROaERBOHI3Y3hrVmxyR2k0Rm16MHMzczFLbVN6S2trUnpUdkQ5K1pqM2tiZG5zd1ZLWnZzWXVrNlExd3ZZTUhQbHc0akZJRUQyK1d3dm5kaEpqc3JqdGtIZlp3UjBRbXlEQUxWTitkNktBS3dPVkRBbWQvN3VXVTA1OFZoakRVYUtLNjdWdUE5MVM2bFU3VHZiZngxYnMrcFVaWGRqR29KeXRzZXNqbEI0MXRBamNHbXZyYUhZY2pRUkZoeGJpMzRkUmxIeGhWWk9hZlg0R094bGNoYmhlVlRBQ1JpckFrKzlhSXNvM2VYUEJSRDZPR0pQM085MGsyUjc3VlpLQ2lacm1weFVZSzFHc2p0KzR5S3lSeGozdndNRWhxSk5rcFhvVTgyaGlndDVoa0QiLCJtYWMiOiIzNjNmYjBjYmQ4ZGQ3M2YwZWU4MmFjOTJkMjEwYmY4YzAwMGVhZTA3NzM1NGIxMTBjYTUwMzZiYTJiMTA5NmQxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjN5ZThxVDhDYjFZUk1UdlcvZlptekE9PSIsInZhbHVlIjoiNTZQc1ZHWENVRTcrbkxmWWkxRStDOXpudFY1b1UyT01OczNuWExYbTZwVzJTeFFYTUpnQjV6WmN2Z0JLc1hlOWtrdXZsb3hRcUxDT0NjNW0wSFErY29pOVE4WSthdXgyTnhRVHRub3lkVzY2cmtCajFmclVjdUI2aDdDUDlWcm0rMUliVmdJNVJnd2xjVzYvYjh2LzFxdk51cGZ3akd0ZGluZStiUUFuZWtiWis2RVdqZldDc2FiUDU1a0oxaEg3akZWeFVWOTRRTVFzVW9MekFuN0ROZjJvaHhIVmQvR2szQjVUeG1PNDlGSVd6R0RXcEpwQkdjV0ozbmJYTURBcjZXVU5scW5qRnN2REN6SFNadlpldG1WK0EwRTVhdm5YMGZZVnVGVS9uZ0RDR1gxZXduWDJINkhUdmFyUUlQQm9oWlAxU2Fka0dYNkR0bk1VZHJESmNXRkg1bU9SWWgyeHdnSFI3Z0xON0tFTldlK1o0S1czWFN2N1lYbEVhNnRWcjNSUEIwYWVEZDJTUXdGcHJYelZlM2RIa1FKYWhxUThQKy90eTJQVkp2NTg2SDFPZTZ1dXVYaHhxK0FhRyt6aGl2SitRd2ZaUm1MQzlUQ01WWVRBb2xaY3lZNVBhenQ1RURmSmNQMURWbVRxQkF4aHI3Y05NeTNtVmFpWkpIOVUiLCJtYWMiOiI1N2I5ZWMzMDIxYzhmNTUzNTUyMGY1NzE3ZmYzMGEwOTNiZjE0MzVjYjhmZWQ3MDE0ZTY2YWJjZmMyMTBjMTljIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkQ1ci9CblNZL3BPZkpZWjY0cTZ2MXc9PSIsInZhbHVlIjoiSWhueERhQXMvTXhYVW8yVVlBakRwZ1IzdVRwVTJWUDJzNEZTMUtTY3NHK1J6M1JPR09kc0gzYXZIb3Y4cHRhWGg0dURCV3FZREJadzdsdUVZdmJaOUF4V0RyTEl0SVFwalFFbVRmTFFoZUh6czBsbGpqTGpUUUNrb1ZyNW4zd1BTeVV6WXBlUDFSaHkrUlFXaVhCbVdsQXRkaHcvaGlGcmNzZ2JpTlYvMzFPTHRBSUQxcGtsMWozS2FtakZrMkVSWE9QdGpPMkx0TURCTVdxc01yNzROaERBOHI3Y3hrVmxyR2k0Rm16MHMzczFLbVN6S2trUnpUdkQ5K1pqM2tiZG5zd1ZLWnZzWXVrNlExd3ZZTUhQbHc0akZJRUQyK1d3dm5kaEpqc3JqdGtIZlp3UjBRbXlEQUxWTitkNktBS3dPVkRBbWQvN3VXVTA1OFZoakRVYUtLNjdWdUE5MVM2bFU3VHZiZngxYnMrcFVaWGRqR29KeXRzZXNqbEI0MXRBamNHbXZyYUhZY2pRUkZoeGJpMzRkUmxIeGhWWk9hZlg0R094bGNoYmhlVlRBQ1JpckFrKzlhSXNvM2VYUEJSRDZPR0pQM085MGsyUjc3VlpLQ2lacm1weFVZSzFHc2p0KzR5S3lSeGozdndNRWhxSk5rcFhvVTgyaGlndDVoa0QiLCJtYWMiOiIzNjNmYjBjYmQ4ZGQ3M2YwZWU4MmFjOTJkMjEwYmY4YzAwMGVhZTA3NzM1NGIxMTBjYTUwMzZiYTJiMTA5NmQxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjN5ZThxVDhDYjFZUk1UdlcvZlptekE9PSIsInZhbHVlIjoiNTZQc1ZHWENVRTcrbkxmWWkxRStDOXpudFY1b1UyT01OczNuWExYbTZwVzJTeFFYTUpnQjV6WmN2Z0JLc1hlOWtrdXZsb3hRcUxDT0NjNW0wSFErY29pOVE4WSthdXgyTnhRVHRub3lkVzY2cmtCajFmclVjdUI2aDdDUDlWcm0rMUliVmdJNVJnd2xjVzYvYjh2LzFxdk51cGZ3akd0ZGluZStiUUFuZWtiWis2RVdqZldDc2FiUDU1a0oxaEg3akZWeFVWOTRRTVFzVW9MekFuN0ROZjJvaHhIVmQvR2szQjVUeG1PNDlGSVd6R0RXcEpwQkdjV0ozbmJYTURBcjZXVU5scW5qRnN2REN6SFNadlpldG1WK0EwRTVhdm5YMGZZVnVGVS9uZ0RDR1gxZXduWDJINkhUdmFyUUlQQm9oWlAxU2Fka0dYNkR0bk1VZHJESmNXRkg1bU9SWWgyeHdnSFI3Z0xON0tFTldlK1o0S1czWFN2N1lYbEVhNnRWcjNSUEIwYWVEZDJTUXdGcHJYelZlM2RIa1FKYWhxUThQKy90eTJQVkp2NTg2SDFPZTZ1dXVYaHhxK0FhRyt6aGl2SitRd2ZaUm1MQzlUQ01WWVRBb2xaY3lZNVBhenQ1RURmSmNQMURWbVRxQkF4aHI3Y05NeTNtVmFpWkpIOVUiLCJtYWMiOiI1N2I5ZWMzMDIxYzhmNTUzNTUyMGY1NzE3ZmYzMGEwOTNiZjE0MzVjYjhmZWQ3MDE0ZTY2YWJjZmMyMTBjMTljIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433148218\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-307106255 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0ALIZsEHdETDJ6zw1LvFSbDq5z0aNxTtPtmIBgcZ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307106255\", {\"maxDepth\":0})</script>\n"}}