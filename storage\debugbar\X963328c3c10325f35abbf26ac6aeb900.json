{"__meta": {"id": "X963328c3c10325f35abbf26ac6aeb900", "datetime": "2025-07-31 04:53:34", "utime": **********.550819, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937613.599074, "end": **********.550848, "duration": 0.9517741203308105, "duration_str": "952ms", "measures": [{"label": "Booting", "start": 1753937613.599074, "relative_start": 0, "end": **********.468101, "relative_end": **********.468101, "duration": 0.8690271377563477, "duration_str": "869ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.468145, "relative_start": 0.****************, "end": **********.55085, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "82.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hRLXEZsNzQbT107aCdGV1EYQl9YInATfFfiwDjO4", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-234675427 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-234675427\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-624690943 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-624690943\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-758150918 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-758150918\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-860471824 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860471824\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1900555447 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1900555447\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1376636106 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:53:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InA3ZGFLbVZXclFkUjJLNVNZUlN5QXc9PSIsInZhbHVlIjoiTnhBWGdiR203Q0x1MmVKVmcxVFFHUXVGUGpodVJLcVdMMVFZSkxlVHhGTllnZmZNS0UzREh4VStmdmFISXZkT2k0QUtlelhBYkZBdWZ2QlcxNWhuMVdoOVdhZ0I3OHVmV0c1enVaaWIrbWI1cnNqVDQ3ZDd1MktrN1Jrcjc1Y1oyMkxSR0J3RDA3MkhjQmJMZ3BHZ0FWa01aMGFGdVVIKyt5NFMwSU1Wdms1OEVWUFFGSk8rcnhYaUJNeFVqdk43ZnJuNENKclVHSHpQMTJTRHpGZktnTWwrY3E2YVU4VU1PRk5OQkpKcUtONTR6ZmRhN0RibkR4TTJWRXRiZE9jOStHOHZuMlZUY0tmb05Yd3ljbjdBRGdVZExrYzc2RnJ6NnRmbzhhbFRVei9TSzgycFlxdXBrcDRTaXJ2Vlc3eXMveW10cURIMDFYSE5zZ0sxNHFTTTN4RXhUTkhIVFlJTDZ2ak9oUHRjUHNWclNjQ3hqR1p0a3JZSzNHUmtDZFRLazZ0MmhFWUNvT0VhTzBzcXZoc2xVdEcvQ3lSMFl3UVhWNFZncDYxbHorVUpQd0grUExQZXNqZEc2ZVkzaXZHY2x6R0RwQkZUdDlRd0ZrZXJucklQMmJ4QnNVbkJ2M092NTVpWHpBdmlnZmxycTU5TDNSVUZpR1ZmMnplUk56dVQiLCJtYWMiOiJlNDBlYTczNGUyNDhjNTU1YTY5MzA3MDQ2NGIzZGFmODc0OGNmMjY0ZTM5NDY0Njk4ZTU4ZDI1NmJjNjMzYmUyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:53:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjFxTm1aUW1rc2M2Y3kyMmFDenNrY0E9PSIsInZhbHVlIjoiZlgxd2VhM3N3QS9kRjhzNlJIS29sRWJDOThuYzYwVWYvT3ZiS1ByZFFtZlVsZXQ1dUtSRkEzc0V4TGpNa0tmOFArcnZwNFRRTFZ4bVUrNDIyQUpHU0R4RVN2R0NYYUpvY1lNWVZIV0FmTDVCQUVhalRmR2FyOUpZNVNEd25VVEZMbTZPUS9ldzlPRW5IQUNMTjJiY3NtY0ZiSVZ6RVZsdlNxbWFWL3ZiK2VrU01zWW5TZ3dIdktoNG1QYXV0aEExekY3VTYyYnlBRlgxa2pPM1QrbjUyaGplSFlnYkFMc2NMWmEyWEQxeTUxb0FDaHFHTld4cWdrYnh2bURxUCt5VG5MSmVLSU13amZnaVZYU2hnRndDZHBGRTBSc1VnQ0tBSVB1QW9sUGRicHU5amhNWDAwTklkMFFIMlF3MWN1MUNoVWxTeGlLa2ZYMkYySi9XRk55NXNEQ1NJUzZueHhnWkxDMXdKWWFsb3lsZFNtSU9HQS9JcndNN3kwQkhwdk5lNFpMTFQvajJRSUFSTWE1V3RmemE4NkFJUW5yOHJETXBVQjVUK2VHRkQ3cURqdHhpNDhXQ0luNHZwWXVCNDJsdm1pVm1DSCtUeVYvcVo3NjAyZVZjMVB5RU5NUEQ5dStybENjanZhVkVRSVkwTWhLRVdjSEhESHpQUHlRY05VTHQiLCJtYWMiOiI5NGEyODg4YjExMGMxMTE0MjExMzJhMzYzYmQxZGM2ZTAwMjI5OWYxYjIyOTZhYWM4ZGE2OTAwMzc4MDY3NWE4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:53:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InA3ZGFLbVZXclFkUjJLNVNZUlN5QXc9PSIsInZhbHVlIjoiTnhBWGdiR203Q0x1MmVKVmcxVFFHUXVGUGpodVJLcVdMMVFZSkxlVHhGTllnZmZNS0UzREh4VStmdmFISXZkT2k0QUtlelhBYkZBdWZ2QlcxNWhuMVdoOVdhZ0I3OHVmV0c1enVaaWIrbWI1cnNqVDQ3ZDd1MktrN1Jrcjc1Y1oyMkxSR0J3RDA3MkhjQmJMZ3BHZ0FWa01aMGFGdVVIKyt5NFMwSU1Wdms1OEVWUFFGSk8rcnhYaUJNeFVqdk43ZnJuNENKclVHSHpQMTJTRHpGZktnTWwrY3E2YVU4VU1PRk5OQkpKcUtONTR6ZmRhN0RibkR4TTJWRXRiZE9jOStHOHZuMlZUY0tmb05Yd3ljbjdBRGdVZExrYzc2RnJ6NnRmbzhhbFRVei9TSzgycFlxdXBrcDRTaXJ2Vlc3eXMveW10cURIMDFYSE5zZ0sxNHFTTTN4RXhUTkhIVFlJTDZ2ak9oUHRjUHNWclNjQ3hqR1p0a3JZSzNHUmtDZFRLazZ0MmhFWUNvT0VhTzBzcXZoc2xVdEcvQ3lSMFl3UVhWNFZncDYxbHorVUpQd0grUExQZXNqZEc2ZVkzaXZHY2x6R0RwQkZUdDlRd0ZrZXJucklQMmJ4QnNVbkJ2M092NTVpWHpBdmlnZmxycTU5TDNSVUZpR1ZmMnplUk56dVQiLCJtYWMiOiJlNDBlYTczNGUyNDhjNTU1YTY5MzA3MDQ2NGIzZGFmODc0OGNmMjY0ZTM5NDY0Njk4ZTU4ZDI1NmJjNjMzYmUyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:53:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjFxTm1aUW1rc2M2Y3kyMmFDenNrY0E9PSIsInZhbHVlIjoiZlgxd2VhM3N3QS9kRjhzNlJIS29sRWJDOThuYzYwVWYvT3ZiS1ByZFFtZlVsZXQ1dUtSRkEzc0V4TGpNa0tmOFArcnZwNFRRTFZ4bVUrNDIyQUpHU0R4RVN2R0NYYUpvY1lNWVZIV0FmTDVCQUVhalRmR2FyOUpZNVNEd25VVEZMbTZPUS9ldzlPRW5IQUNMTjJiY3NtY0ZiSVZ6RVZsdlNxbWFWL3ZiK2VrU01zWW5TZ3dIdktoNG1QYXV0aEExekY3VTYyYnlBRlgxa2pPM1QrbjUyaGplSFlnYkFMc2NMWmEyWEQxeTUxb0FDaHFHTld4cWdrYnh2bURxUCt5VG5MSmVLSU13amZnaVZYU2hnRndDZHBGRTBSc1VnQ0tBSVB1QW9sUGRicHU5amhNWDAwTklkMFFIMlF3MWN1MUNoVWxTeGlLa2ZYMkYySi9XRk55NXNEQ1NJUzZueHhnWkxDMXdKWWFsb3lsZFNtSU9HQS9JcndNN3kwQkhwdk5lNFpMTFQvajJRSUFSTWE1V3RmemE4NkFJUW5yOHJETXBVQjVUK2VHRkQ3cURqdHhpNDhXQ0luNHZwWXVCNDJsdm1pVm1DSCtUeVYvcVo3NjAyZVZjMVB5RU5NUEQ5dStybENjanZhVkVRSVkwTWhLRVdjSEhESHpQUHlRY05VTHQiLCJtYWMiOiI5NGEyODg4YjExMGMxMTE0MjExMzJhMzYzYmQxZGM2ZTAwMjI5OWYxYjIyOTZhYWM4ZGE2OTAwMzc4MDY3NWE4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:53:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376636106\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1976703626 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hRLXEZsNzQbT107aCdGV1EYQl9YInATfFfiwDjO4</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976703626\", {\"maxDepth\":0})</script>\n"}}