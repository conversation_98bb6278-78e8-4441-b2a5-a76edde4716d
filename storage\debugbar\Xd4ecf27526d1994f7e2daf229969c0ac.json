{"__meta": {"id": "Xd4ecf27526d1994f7e2daf229969c0ac", "datetime": "2025-07-31 04:48:30", "utime": **********.537656, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937309.503932, "end": **********.537696, "duration": 1.0337638854980469, "duration_str": "1.03s", "measures": [{"label": "Booting", "start": 1753937309.503932, "relative_start": 0, "end": **********.447701, "relative_end": **********.447701, "duration": 0.9437689781188965, "duration_str": "944ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.447717, "relative_start": 0.****************, "end": **********.5377, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "89.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IG6Gm402sihppsOf4lqXGo0EPJFY604ZibVdEhDo", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-1161751928 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1161751928\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-744285309 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-744285309\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1068082093 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1068082093\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-587117261 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587117261\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1204051755 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1204051755\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1096556689 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilh1OHQwbTJaYkMrbldPSEZhbCt4eHc9PSIsInZhbHVlIjoiVWNYN2M5cHhjcTYrSFdCZlo3Mkg5Y1pLeXczZlV6bFFFdFA1WENqeFJoaThWUkQ2WncxWENTVVNxNURBSTBWa3FMYmovalhGbjl3RGYxdU8wQ1lTekVOd040Nm92TDczeFFlL0doYzhTaUhQK1MvcXlnOXAxSmxDVXdjTlQ2UlIrMngwZ0Q0c0J0UGE0RFM0YldpeTBNbklhdnliTlZCbGwvM1FNZi9MNE1oRExZbUVIaEhkS2R0aXRuSGMydEwvcUtEVUFINUdhc1RkYWIrYUI1VVI0b01kZFNlbUEwMEp6aTBUTVpBZnRKYTN0aERRRVJSUXIrOFpRN29TK25BbDYybStzcGtjRDVuOTJsc0V4K3BTNVJ0RGVFVkIrUkJwNzJCaVJydmgrSFZmcE8vV2lnRWl6WitSV3g5dXNkeXFIc0xPMWpDMGNyMmM2bzRnelZEaDkvS3hFNHU2NklCOGFYMEExZjRzVmRING9uUkFPbUpWbXg1VGp3dW5TN1ZDSEN1Z1htSTZEWVR3VjUzT0NNbk0rUytQVzI2Z0FjNXJhQXcveGdQMC9XSEhkRjNSK2Zqd2ovWHdrd3Nvd1N3cXR2b3BINU14ekhYUG1tZ3ZwalVVKy9TNzEwOU9RbUdCTHBBaDBwY2Z1SUNxKzZ2QU4zM1o1WXpaRGI4UHFmM3AiLCJtYWMiOiJlYjQzMTRlNWYxYjg0MjIzNmUyNGZhNDBhNWMxZmFkYzFkYzY5ZjA2NTY5MGVjOGQ4Zjc3ZjBlNzQ4MWI1ZmZkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZlMXd4eUlzY2g5dmtVelBPeFViV1E9PSIsInZhbHVlIjoiSlVzaVlXcWhjL0I4eFFvV05xVTVkbWQwQ1lMWVFVVFRsR0h4YVFTR2JJczdHNTB4YklmTUNRS1grdzNHY1BjSmxaYTIzQmxmZzNrTkVYUEhMd1JTODF5OGNta0Y3N3dYRVp3emlxekdNZFExUVdEdy85WkcxZ1owb2RXQU9CditrVmU5VUZ5YzV6NmxRa3RVRDRTbTBGZUFYTzdQU0YwRXdQcVpnMmxFS2l5TSsxcFhLUmVFU2VvOEhQU1RYL2tXL1pRazgvQUcyalh5R0M0WU1IQzJiT2lXMklnTDdpblFZVnF3eDNUYjM2S3g0dFR5M2xFQ3BnQVJMZzNpSFRqSDhLekdDbFFpNm9tTFE1eG1sbXYyTzlTVVdLa2d4a2k4RkdVZi9hYkEydVhkaS9UNFhtVzRGcFF3V3QxaW5NdW5KclY2ZytnTjE0NSs3T3RzUXJCa3o2cHVuV3ZtY3dyWkFwT2V2Qk1RZU5QMFhDRWpiblBPd3hmbW5OeEJkd0dic3lFVXZHREdHNlZFczBncjF5OWpJVzFGbk1aRUw0M1BxN3RRRUhBcnBxc2xVbS8zU1RZR09CT1YzY0F6bXlNdlJHVDdnNWt1L0NBNXNad1RxbmFtbWxXR0o2QzdJRnlGWlpxbU85ZHBzQlhHSzlHV3REcUdacVJGYzZiTUxIcFIiLCJtYWMiOiI4NTk0MDE0OTZmZWJkMjhjZmViNDU1NzAxMGI5ZGI2MThlMDFmMmZiZTQzZjE3YjllN2QzNDYzMDAwZTUzZDc3IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilh1OHQwbTJaYkMrbldPSEZhbCt4eHc9PSIsInZhbHVlIjoiVWNYN2M5cHhjcTYrSFdCZlo3Mkg5Y1pLeXczZlV6bFFFdFA1WENqeFJoaThWUkQ2WncxWENTVVNxNURBSTBWa3FMYmovalhGbjl3RGYxdU8wQ1lTekVOd040Nm92TDczeFFlL0doYzhTaUhQK1MvcXlnOXAxSmxDVXdjTlQ2UlIrMngwZ0Q0c0J0UGE0RFM0YldpeTBNbklhdnliTlZCbGwvM1FNZi9MNE1oRExZbUVIaEhkS2R0aXRuSGMydEwvcUtEVUFINUdhc1RkYWIrYUI1VVI0b01kZFNlbUEwMEp6aTBUTVpBZnRKYTN0aERRRVJSUXIrOFpRN29TK25BbDYybStzcGtjRDVuOTJsc0V4K3BTNVJ0RGVFVkIrUkJwNzJCaVJydmgrSFZmcE8vV2lnRWl6WitSV3g5dXNkeXFIc0xPMWpDMGNyMmM2bzRnelZEaDkvS3hFNHU2NklCOGFYMEExZjRzVmRING9uUkFPbUpWbXg1VGp3dW5TN1ZDSEN1Z1htSTZEWVR3VjUzT0NNbk0rUytQVzI2Z0FjNXJhQXcveGdQMC9XSEhkRjNSK2Zqd2ovWHdrd3Nvd1N3cXR2b3BINU14ekhYUG1tZ3ZwalVVKy9TNzEwOU9RbUdCTHBBaDBwY2Z1SUNxKzZ2QU4zM1o1WXpaRGI4UHFmM3AiLCJtYWMiOiJlYjQzMTRlNWYxYjg0MjIzNmUyNGZhNDBhNWMxZmFkYzFkYzY5ZjA2NTY5MGVjOGQ4Zjc3ZjBlNzQ4MWI1ZmZkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZlMXd4eUlzY2g5dmtVelBPeFViV1E9PSIsInZhbHVlIjoiSlVzaVlXcWhjL0I4eFFvV05xVTVkbWQwQ1lMWVFVVFRsR0h4YVFTR2JJczdHNTB4YklmTUNRS1grdzNHY1BjSmxaYTIzQmxmZzNrTkVYUEhMd1JTODF5OGNta0Y3N3dYRVp3emlxekdNZFExUVdEdy85WkcxZ1owb2RXQU9CditrVmU5VUZ5YzV6NmxRa3RVRDRTbTBGZUFYTzdQU0YwRXdQcVpnMmxFS2l5TSsxcFhLUmVFU2VvOEhQU1RYL2tXL1pRazgvQUcyalh5R0M0WU1IQzJiT2lXMklnTDdpblFZVnF3eDNUYjM2S3g0dFR5M2xFQ3BnQVJMZzNpSFRqSDhLekdDbFFpNm9tTFE1eG1sbXYyTzlTVVdLa2d4a2k4RkdVZi9hYkEydVhkaS9UNFhtVzRGcFF3V3QxaW5NdW5KclY2ZytnTjE0NSs3T3RzUXJCa3o2cHVuV3ZtY3dyWkFwT2V2Qk1RZU5QMFhDRWpiblBPd3hmbW5OeEJkd0dic3lFVXZHREdHNlZFczBncjF5OWpJVzFGbk1aRUw0M1BxN3RRRUhBcnBxc2xVbS8zU1RZR09CT1YzY0F6bXlNdlJHVDdnNWt1L0NBNXNad1RxbmFtbWxXR0o2QzdJRnlGWlpxbU85ZHBzQlhHSzlHV3REcUdacVJGYzZiTUxIcFIiLCJtYWMiOiI4NTk0MDE0OTZmZWJkMjhjZmViNDU1NzAxMGI5ZGI2MThlMDFmMmZiZTQzZjE3YjllN2QzNDYzMDAwZTUzZDc3IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096556689\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1950862366 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IG6Gm402sihppsOf4lqXGo0EPJFY604ZibVdEhDo</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950862366\", {\"maxDepth\":0})</script>\n"}}