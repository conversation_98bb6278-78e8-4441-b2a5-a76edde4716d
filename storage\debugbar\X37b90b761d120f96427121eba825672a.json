{"__meta": {"id": "X37b90b761d120f96427121eba825672a", "datetime": "2025-07-31 04:55:37", "utime": **********.386287, "method": "GET", "uri": "/login-with-company/exit", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937736.373089, "end": **********.386317, "duration": 1.013227939605713, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1753937736.373089, "relative_start": 0, "end": **********.245961, "relative_end": **********.245961, "duration": 0.8728718757629395, "duration_str": "873ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.245977, "relative_start": 0.8728878498077393, "end": **********.386321, "relative_end": 4.0531158447265625e-06, "duration": 0.14034414291381836, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44673080, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login-with-company/exit", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@ExitCompany", "namespace": null, "prefix": "", "where": [], "as": "exit.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1476\" onclick=\"\">app/Http/Controllers/UserController.php:1476-1480</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.025299999999999996, "accumulated_duration_str": "25.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 79 limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.315328, "duration": 0.023899999999999998, "duration_str": "23.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 94.466}, {"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Services/ImpersonateManager.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Services\\ImpersonateManager.php", "line": 137}, {"index": 19, "namespace": null, "name": "vendor/lab404/laravel-impersonate/src/Models/Impersonate.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\lab404\\laravel-impersonate\\src\\Models\\Impersonate.php", "line": 64}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1478}], "start": **********.3558471, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 94.466, "width_percent": 5.534}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login-with-company/exit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login-with-company/exit", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1389260709 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1389260709\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1841083559 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImlBWnBrdE5Yb3lPUmJTNG9pMTNFakE9PSIsInZhbHVlIjoiUUJtRDJSSVVWdkExUGRLcStnQzB6RDVTTmpyVkErcUdIZEhrTHlzYnorN1ovOHQ4L3I3dnhna2lLZjlGZUJJYTJaK0lYditJVXAwZlkxVmNLU0k1OWRoU1ZLV0Q1TGE5bnk3ZHpvY3FYRlZqSDU2akVWVE1HUWEzbFBGVCtzQm9DOXFTYTMwNHN0eUpkR0Qyb3hvMm1Ub1dHTnlhZ0JJSm1jME8zYytCVGJRd3pkUHk2alMyejNYalk1QkQyNEVxNGJxRWREM0pLWW5OU0FZSmNhMGFFSlFXb3VQTVNINUxwVHJDNmlCQUUyZmFmY2VpZ24zQjZRVFZsL1hGY082cE5DeWZhRTFmYkdKY0VjZjVRMEhkZVcvV0xwZlVZdXZjV1pWUHErTk9qcFF1cWNDNU9CMm1vUzdZejNOZ3l0VVYzMG0yenVJZFJZTkl4SmxjVTJBcFhGcFFKQStUVlU3K09saGJodlNWMmJzVmhydXpiUDN1SUp1dTIydHFSOUZYMWN4YkR6SGN0SWZvdjQzZ0dXODdFRmFRU2dsQmZCczZmRFRJNHBZL0xSdkEwWFZqaVFYM3I2Z05iU3lhVUJRUDJRMHc5cjJzeUloZFZ6V3Nrb21QYmxUN3d3dUZRaGZFSytoRHAyK3RNbmZMVkpUeW1UV0JDaEJEdkk2ZUdUK1QiLCJtYWMiOiJhZDNhNTYxMzdhNGQ5NjY5N2I0Y2QyMjA3Y2FlZjk3N2VlMDRiY2YzMDI2MjI3ZjY4NzMwNWE0MmYzNTkxYWU5IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkNMYk1XcnUvbkh5V0Z2VWljQzR2TFE9PSIsInZhbHVlIjoiSTBXZFo4cUxTZW9GWVFmNjduWCs2UisxaURDWHB1cWtNNG9HYXZoTm9wOXdLL2ZkakFpWklwcXc0bjBvaUVLL2ZxeVp2RnNUUWxaTGEwQTJON00xT1VNTGRhN2pjaVdyT2VvYm9ZL3dKcG9RcEZOYXp1TE13dWJTdkxMbEVCZ0NOTXVOVTJsUzhpNndpbzlZaDVLQVJUY09SeHRGWkFvdURzaEo1M2tsZTJpUzdtc2MxMjh2bCszV095ZHkwWUlMRG53KzlGVTY1NEU1K1BjcnIvZUhoNkFadlh0NDNUM3ZwdlZlS3Bwdk1URGI0akZrMi9ORUtaTEprWmg4b0dhODJoZHVadjYxa3VjSWtpRnhHYmJDMnRJeEpUY1VHOGUyakJ1ZG9SUTZKTlp4QWdsL3l0cHVsdTJxSEFZN0Zkd3UyMDRweTRZZ2l4d0gwaXlQOGVUc2R1NkluOXlVdCtoUmNQc0dkc3dPTFlVeWhCd0F1NExnS3BhK05ZdjlEVDFPNXFTd3F6RHdhNkdCaUplbnVnU2twYzJFL21pb2V6K0FHalZzcmsxOE95am9yeGZUMUhGN1JHa2o0ejhKYW11bWxIVVhZSFNWUHV3ZW00S3NHWGhPMW1xZFlpTlVmekNSQmFRc3NROFVnbjFJd2hFSXRMNVZPOWpST2FkMk5Dby8iLCJtYWMiOiI4MmM0YmExNDJkMmNlMDBhYzFhOGRjMzI4ODI5NDBhYjJiMTc4NDY1YmM0Y2IzNTIxN2U4MWI2NWEwMDQyNjZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841083559\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-836289845 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5CMjY5xZzCpzyhz20hjbq5xMLnYwYBAUF3viINCI</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836289845\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1966418631 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:55:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IncwelYxUVcvZWU1YjhlVGV4NXBVaVE9PSIsInZhbHVlIjoiNys1SjdSclRTUGQwSGl5Q1dNcUNOa2h4aUNweXpZd0lSbmtQYkZaR0tjalNsNFRZb0tXTkZveUVsU21ZbmhQV2JzdGUvQThDWDlLRGxRdXB6QjVRT3FIWXFucFJGdU4rS3JRUElTUTY3VVVFd1BTclZZRkptbVp1OUNiQk1SL3lvMVprcVBqRXM1eDVpL2Q3RGxrem9YYWNJSzFXUVV5SFFsakFtblVBek5MMWIrMWYvd0pkUTAzeEh5aWdDZVJRTElUZXhPcExCSXBSVGV4aWdQbndKNnllbDhkTzlyVnJ5QnJMbklTTEszTm9GRHRnM2c3MEVtOEV6L0R3S3RyZDVpdzluQ3FqZ1A4bWxPbzZqVVVLaW1QZ1NYdnZvQTBPd0tGdnkrak5tWU1hM3F5aXMxZTNEWG5tSk9NQlhvSksyV0FwQ0JSYjQybmpPK0dBMTloUnBIQVpsUFhHUmVQVGJXQ0lSb2RJb09XU0VOcEtRbzdPMUJEdHh5eE9kM0lORkt1ZHg4RnorWUl2aGJyY0lKT2cwQkQ5T1AvU0wzTVFDNFFPQVNrVGlKdzN0cEk4Q2NDcWNuUUdOK2xwTDhuc1NBb3BSMUhVV1pqM2puWW91WWVGMlBWZVRJSTMwWklZbXhlQ2pRWVpLWmdjWGlldEswMnhnTXpTaU1Yd3FEcFIiLCJtYWMiOiI5Yjk4OGQ2MzNhNjAxOGZmYWY4YTNjYWUxMjJmNmVlN2RlNmE5YjNlMDQ0NmIwMmRmODJiMmFhMmI4Yzc2MmQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:55:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlhKVU90ZFFBenlRK25hdFdyNkNmclE9PSIsInZhbHVlIjoiUWZ4WUMvdmQ2alBVOXYwQnlZWWRqN0Rxd1QvTGVjUU1IU2Q4aHlFT1lYLzFMaVlZVlJIZ2taUFNSUGx3ellkaTNtVVBaM2E4aUZTSi9yVFBtZktqTlhBR2pUUzU4ZThyb0JRcmEra3hqUTFUTC9iTVZwZ0RhN1ROckp6aThzajVRd3Uxb1M1Q0pjUW94U0ZFRlpaK2FBOG42djViaFdOaUtKNmgzaTZkYzJXN1JvWS9ueXpYMEwxVFBwanltdmFOMlMxVnZkRERLR0lwMnQwYVIyRmF3aXh0NytZN3h4djl0aDJWVmN0WmtxVVVSYklZdmRHNmpQSkJjVytxcjdadGlmKytwU2lkVXFiT2RIUXJBa2lzSmhwSnRCUjJ1VjZ5NkhrTGUxTFV1Z093NmMwQ0ZDT0g2bjErNGliTEV0WWNxSFJlNXQ5MHN5RzdiZnlzclZuMkpXczF5K2k5bFhoZmJiY2JhUkRkRldMdkxkb3MxZzhxYTJBdmJ0TG9reE9hOTlzQzhPQ3ByUDJTQWFJeWhhTUFLdWg3R3YzRTZRa3h2TmtmU0NsRVNrNmhoYzNFaWNIYjk3ckpNVWIzWlprelhEQ3ZaVk4rL1AxL25tekFwYVNIZXRhMFZRMUpmR28reGQzc28wRTllZnN6M3M3QUJyU0oycWFBZVJtbzBiS08iLCJtYWMiOiJjOTg3Mjg3YmQ3ZWM1MGU4MzgwMTY5NzI0ZTMwYmM4N2Y2OTJhOGIxY2JiZWY3NmJiMjYzZDQyZTI4OWMwMTM5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:55:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IncwelYxUVcvZWU1YjhlVGV4NXBVaVE9PSIsInZhbHVlIjoiNys1SjdSclRTUGQwSGl5Q1dNcUNOa2h4aUNweXpZd0lSbmtQYkZaR0tjalNsNFRZb0tXTkZveUVsU21ZbmhQV2JzdGUvQThDWDlLRGxRdXB6QjVRT3FIWXFucFJGdU4rS3JRUElTUTY3VVVFd1BTclZZRkptbVp1OUNiQk1SL3lvMVprcVBqRXM1eDVpL2Q3RGxrem9YYWNJSzFXUVV5SFFsakFtblVBek5MMWIrMWYvd0pkUTAzeEh5aWdDZVJRTElUZXhPcExCSXBSVGV4aWdQbndKNnllbDhkTzlyVnJ5QnJMbklTTEszTm9GRHRnM2c3MEVtOEV6L0R3S3RyZDVpdzluQ3FqZ1A4bWxPbzZqVVVLaW1QZ1NYdnZvQTBPd0tGdnkrak5tWU1hM3F5aXMxZTNEWG5tSk9NQlhvSksyV0FwQ0JSYjQybmpPK0dBMTloUnBIQVpsUFhHUmVQVGJXQ0lSb2RJb09XU0VOcEtRbzdPMUJEdHh5eE9kM0lORkt1ZHg4RnorWUl2aGJyY0lKT2cwQkQ5T1AvU0wzTVFDNFFPQVNrVGlKdzN0cEk4Q2NDcWNuUUdOK2xwTDhuc1NBb3BSMUhVV1pqM2puWW91WWVGMlBWZVRJSTMwWklZbXhlQ2pRWVpLWmdjWGlldEswMnhnTXpTaU1Yd3FEcFIiLCJtYWMiOiI5Yjk4OGQ2MzNhNjAxOGZmYWY4YTNjYWUxMjJmNmVlN2RlNmE5YjNlMDQ0NmIwMmRmODJiMmFhMmI4Yzc2MmQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:55:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlhKVU90ZFFBenlRK25hdFdyNkNmclE9PSIsInZhbHVlIjoiUWZ4WUMvdmQ2alBVOXYwQnlZWWRqN0Rxd1QvTGVjUU1IU2Q4aHlFT1lYLzFMaVlZVlJIZ2taUFNSUGx3ellkaTNtVVBaM2E4aUZTSi9yVFBtZktqTlhBR2pUUzU4ZThyb0JRcmEra3hqUTFUTC9iTVZwZ0RhN1ROckp6aThzajVRd3Uxb1M1Q0pjUW94U0ZFRlpaK2FBOG42djViaFdOaUtKNmgzaTZkYzJXN1JvWS9ueXpYMEwxVFBwanltdmFOMlMxVnZkRERLR0lwMnQwYVIyRmF3aXh0NytZN3h4djl0aDJWVmN0WmtxVVVSYklZdmRHNmpQSkJjVytxcjdadGlmKytwU2lkVXFiT2RIUXJBa2lzSmhwSnRCUjJ1VjZ5NkhrTGUxTFV1Z093NmMwQ0ZDT0g2bjErNGliTEV0WWNxSFJlNXQ5MHN5RzdiZnlzclZuMkpXczF5K2k5bFhoZmJiY2JhUkRkRldMdkxkb3MxZzhxYTJBdmJ0TG9reE9hOTlzQzhPQ3ByUDJTQWFJeWhhTUFLdWg3R3YzRTZRa3h2TmtmU0NsRVNrNmhoYzNFaWNIYjk3ckpNVWIzWlprelhEQ3ZaVk4rL1AxL25tekFwYVNIZXRhMFZRMUpmR28reGQzc28wRTllZnN6M3M3QUJyU0oycWFBZVJtbzBiS08iLCJtYWMiOiJjOTg3Mjg3YmQ3ZWM1MGU4MzgwMTY5NzI0ZTMwYmM4N2Y2OTJhOGIxY2JiZWY3NmJiMjYzZDQyZTI4OWMwMTM5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:55:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966418631\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/login-with-company/exit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}