{"__meta": {"id": "Xd00e44f783b04e277cbf39d5413613a4", "datetime": "2025-07-31 04:55:44", "utime": **********.592923, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[04:55:44] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 7,\n    \"user_type\": \"system admin\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": false,\n    \"has_module_permissions\": false\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.589379, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753937743.576644, "end": **********.592948, "duration": 1.0163040161132812, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1753937743.576644, "relative_start": 0, "end": **********.481555, "relative_end": **********.481555, "duration": 0.9049110412597656, "duration_str": "905ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.48157, "relative_start": 0.904926061630249, "end": **********.592951, "relative_end": 3.0994415283203125e-06, "duration": 0.11138105392456055, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45913320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00915, "accumulated_duration_str": "9.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.540834, "duration": 0.0075, "duration_str": "7.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 81.967}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.564579, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "radhe_same", "start_percent": 81.967, "width_percent": 7.76}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5697901, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "radhe_same", "start_percent": 89.727, "width_percent": 5.137}, {"sql": "select * from `ch_favorites` where `user_id` = 7", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.573249, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "radhe_same", "start_percent": 94.863, "width_percent": 5.137}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-305406522 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-305406522\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-417385152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-417385152\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1212282086 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212282086\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-618654508 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlorVlpaWlh0UWY2V0RvY0hlS2NxZnc9PSIsInZhbHVlIjoiQWkxNXhhQXJsL1R0eTVRM0tBSzRLZ2xnL0hzWmVONFh4YWlhV0I0bWdCNlJaclpEbXovb1VXdTRBUWlXSUprbnNKQzRlK3JVaW5oQ2RYU0laRlMvNVBrWUg0MVYweE5wZG9HejA2YXRBTkcyVTBKcTJhV3hsSnYzZFBrZlBvdVkvTHhhQnJZbjQxd3FPSEdyQXlxbzFaM09ONEl2Mk1qb0pmUktFVnJScHpZUnlNNGJSeDJFWlNuSTdDWVpiUkhMdFo0L1grTTBna2hDYk84cGoxVmtUN2JFOE84UVdYQm1FWWlWU2t3SDduSFI0VkE3OWNVYlJFN2RpTk1IM3cwWE5Sai9zenc1NE1BbFo5bjNDQVZrZEV6Ui9yUXM4MEZ3N25YanJ5MTQ2UlBnNWxQN3hpZlJQZzVtb2pGeStJbWpxMHpjOGx6bEc4dTBZQkFWNW45TGNlbFdwMzR6ZmhFWkVSbCtSQkhNMkF5NXQxRE9UcWhSU3Y5ZmxSN051U3hZa3BSU2M5TkUvZlhWVUNFYjR4eDA2ejE3OHlwamlaSzQ4MHY5WTdSaDhEWFE2YkE0a0N5amE0M01BNEl2SzVFVnFmZndFNG81M2lLdkhmbDQ4NkFyNGFDdGJPU2lucjB5c1NLV0xpT1NuMG81TVJXQVJTV2tDY05OckEwckZ6eEYiLCJtYWMiOiI4NTUyOTZlOTFhZTczMWI3OWNhODMxMjc5NzRlOTMyNzQ2NDFlODkyN2YzMGEzMzZlYTNjNDAzNmIzNDY4NTNhIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRkczdQbWJ0MVQwczl3aUpFMWdxeWc9PSIsInZhbHVlIjoiR0hRTzQ2ZEhwZHBoWXN2b2xJcEdXbCsrQTk3bEI1R2kzYzBWbXNxbmk1OVZEc1pib2ZMZFpxb241Sno1SkJubmI3cVgrVml1MHZWckZ1d2ZqWXVVa0dwSHZaa1FZOFNGOG5uMlRPNm5ubHJWVFNCVFA5SlNZOUJESzRuZ1ZQb3MxenVBZ3ZxYTBESUZ6b2JhcVNmSTZ3NXhuUzdUWjJLT05HUGxMN2hMbUNVdWNtam9oSUxrajgzY04rNWhjbHM0aGJmanBrZ1MxRHBWcDZnd1c0eW9lZi9qSk50SFFrZHpnWC9jaEx2YlkvUnRhNEYxc01YZ05yQVRwRFhuandWQVMyc1pwMGdZRWNYWmhYL2xISFBUV0s1dnFpRTFZeURNeEdNQTlMTjZWMWNod1IvaUxNQXRWR1NmbkRoNnlZTmF1dFRYZldFNEY0VWdFM1ZwbEJZTVplNEl6bkRYanpCWXRWTnNwQTN4UEw2VHc3bysxZytSSE1DZDNCUWp6VmFVc0lHWWFOaHJndVZMMnIxY2pSTzdqZHpacTVzYy9WRDB6enIxaFZzU2IvZVBkWnFuWDBOVnZ0ZVExbEgwYk9WZ1FrMm8rbEFRNDQybmlMSXo5NVBLZGx3NjlsTm5JOGs1T0dEM0NpbUlGYThtaGw3T3BLKy9aSFJYQ3lzSEdDekUiLCJtYWMiOiIzOTNhZjg0ZGFjNjYzMDNjYjk4ZTMyYWNjNjQwMzFmZGM0YzhkOGVmMzFlYjYyNWQ3MmM5ZmZjZDEwYTM1OWQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618654508\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-265714590 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xd9NW8hFgkfk3IY3fS1z2N1y4cUVYfmlEnP2kks5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265714590\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1541034341 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:55:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBveE1VdzlIcXZFSzVQRFZiTkVHdlE9PSIsInZhbHVlIjoiTWgySXlGaUxMNGtyTTJmT1F2dWdCcm5BZkZmVGtsSEZOdnZLKzRvZmtPd29sYWdTem5wZ0dWK3AwSDhMa1JKazNGZEJ2Ym5rR0lmZTV1NmpMWmdFcXJoclFucm5hcWxEWkEwNC9jTEtjSEtvNTF5cUNRU1U0TjJFMjRNL1NkZWpVRXhTMUl1RWd6Ump0Y1NLUWhDN2JJeEl6dWp1K0g3cHhNaFVjWWFYaEJKV3kvN3l6M2I2Q2h3QUdJRXZ0Q2t4aVMxdUlIakFMZ0s2NDV2bE5UdVloSDAzbEZmblg2RjdZc1BhcUcwdVJ2N3QzeWorc1NHaFV3bDRJYXVuYlVtY3hCS1k4VkFiTm1sU2kvQVlJVDYxbzF0VTVGYXJxeHhiSzNzSFROdUc2OTBrZ1psa3BQQVZ2ZmFRUVdtZzdFc2swZk10clQ4ekVFRDVpbkVMSjhlMU0wRzNnQTlDamhWQkZZSkwvY1NLREMxdHlPbHNJL3d1bHV6NUFxK1dxSWhFS254dGhTZ0FuR1Bxbmh5ekFmMlRBM0tkVUFtRDdhRWU0R3NuZTBMV01hRk1acU11THpCSWhWTDU1K2VJbTlJSEZUZHYyS0x3eUNIYnlJVE9RdzVEbnlBTEI3aDROcmYwVktpa1hKMis5aWFqY1g3a3liOVVKM3NpVXVlNW1Cb2kiLCJtYWMiOiI1ZTdmZTBkMDFlODRkNDJiNGQ3NmYxNGYzOWRkZDQzODIwZjY2MWE2M2ZhNGRlNzE2MWYwMmEzODIzNGM5YTdjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:55:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkNBVkNWcGdJL1p4MGlGbFByU084Q2c9PSIsInZhbHVlIjoiZm5pYkJwNWJLK3UyamhTNklIMzFmSGlEam5qbElTWVBMMWFwQm05N2F0VUNyRlpBeVdnWGRuSm5ON2xadHRaMk9JQmpzQTAyTE01Unk0TzZuL0c2akdqR1gwRFVXSmpoU0RydFYwcFQvSVJ0dWp0RlNvU1V2WndiLzhjNmdOVEp5YWxaZm5GUnBXR1A5VjF2aDlqVDJFTjlITzJ4QzlDa0hDUkV0TUt5Ukk1Y3pzaGRWR2dHVW1qQy9TYVRiZzNSWFd0d1h4S3dlVjNWeEM0T1FRV3hPWFpScFZOVkoyeENkWWVHY1Q5Q25xN1FKV1UyalFGdmtxTTBXWEFiV01uRzdQWmxXZzZZV3BUZDZ5TzJUQkpGMDExaTdMY0huQ3N3amhCTTNFYUJEaG9YWHROVkNSK015UmU1NDlEWlc1YXFvQWttUTdCbGx6NFZucFpURWNoaTh1Tlg4M01kZFlvRlVMUUk4UldKczhqYXVqS2hWL0hIUytQSjlxWnFiQmRqMnZqM2pIWjNqUUwxcEphNE9lK3NMRVVwQUdVYlFvbW5kUVY4SUQ2S2RwM2cvcisyUGptZXg3SFN3ZkJ6cHIyenJFOWo0VjRjc1lZeTlCMGZuN05HbzFLR1JnSjZJL1JGZ1ZvTmJYZnFHejB4SVdXQ1hUR3VHSytXTFF3ZUNIRmsiLCJtYWMiOiI3N2QzMWY2YTc0NmZkZWQ0YWIyZGIzNTVmMTYwMDdmMjdmNWUyMzhlOGRjODBhZjE1Mzc2M2I3ZTlkYmNkNTc1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:55:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBveE1VdzlIcXZFSzVQRFZiTkVHdlE9PSIsInZhbHVlIjoiTWgySXlGaUxMNGtyTTJmT1F2dWdCcm5BZkZmVGtsSEZOdnZLKzRvZmtPd29sYWdTem5wZ0dWK3AwSDhMa1JKazNGZEJ2Ym5rR0lmZTV1NmpMWmdFcXJoclFucm5hcWxEWkEwNC9jTEtjSEtvNTF5cUNRU1U0TjJFMjRNL1NkZWpVRXhTMUl1RWd6Ump0Y1NLUWhDN2JJeEl6dWp1K0g3cHhNaFVjWWFYaEJKV3kvN3l6M2I2Q2h3QUdJRXZ0Q2t4aVMxdUlIakFMZ0s2NDV2bE5UdVloSDAzbEZmblg2RjdZc1BhcUcwdVJ2N3QzeWorc1NHaFV3bDRJYXVuYlVtY3hCS1k4VkFiTm1sU2kvQVlJVDYxbzF0VTVGYXJxeHhiSzNzSFROdUc2OTBrZ1psa3BQQVZ2ZmFRUVdtZzdFc2swZk10clQ4ekVFRDVpbkVMSjhlMU0wRzNnQTlDamhWQkZZSkwvY1NLREMxdHlPbHNJL3d1bHV6NUFxK1dxSWhFS254dGhTZ0FuR1Bxbmh5ekFmMlRBM0tkVUFtRDdhRWU0R3NuZTBMV01hRk1acU11THpCSWhWTDU1K2VJbTlJSEZUZHYyS0x3eUNIYnlJVE9RdzVEbnlBTEI3aDROcmYwVktpa1hKMis5aWFqY1g3a3liOVVKM3NpVXVlNW1Cb2kiLCJtYWMiOiI1ZTdmZTBkMDFlODRkNDJiNGQ3NmYxNGYzOWRkZDQzODIwZjY2MWE2M2ZhNGRlNzE2MWYwMmEzODIzNGM5YTdjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:55:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkNBVkNWcGdJL1p4MGlGbFByU084Q2c9PSIsInZhbHVlIjoiZm5pYkJwNWJLK3UyamhTNklIMzFmSGlEam5qbElTWVBMMWFwQm05N2F0VUNyRlpBeVdnWGRuSm5ON2xadHRaMk9JQmpzQTAyTE01Unk0TzZuL0c2akdqR1gwRFVXSmpoU0RydFYwcFQvSVJ0dWp0RlNvU1V2WndiLzhjNmdOVEp5YWxaZm5GUnBXR1A5VjF2aDlqVDJFTjlITzJ4QzlDa0hDUkV0TUt5Ukk1Y3pzaGRWR2dHVW1qQy9TYVRiZzNSWFd0d1h4S3dlVjNWeEM0T1FRV3hPWFpScFZOVkoyeENkWWVHY1Q5Q25xN1FKV1UyalFGdmtxTTBXWEFiV01uRzdQWmxXZzZZV3BUZDZ5TzJUQkpGMDExaTdMY0huQ3N3amhCTTNFYUJEaG9YWHROVkNSK015UmU1NDlEWlc1YXFvQWttUTdCbGx6NFZucFpURWNoaTh1Tlg4M01kZFlvRlVMUUk4UldKczhqYXVqS2hWL0hIUytQSjlxWnFiQmRqMnZqM2pIWjNqUUwxcEphNE9lK3NMRVVwQUdVYlFvbW5kUVY4SUQ2S2RwM2cvcisyUGptZXg3SFN3ZkJ6cHIyenJFOWo0VjRjc1lZeTlCMGZuN05HbzFLR1JnSjZJL1JGZ1ZvTmJYZnFHejB4SVdXQ1hUR3VHSytXTFF3ZUNIRmsiLCJtYWMiOiI3N2QzMWY2YTc0NmZkZWQ0YWIyZGIzNTVmMTYwMDdmMjdmNWUyMzhlOGRjODBhZjE1Mzc2M2I3ZTlkYmNkNTc1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:55:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541034341\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1509348161 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509348161\", {\"maxDepth\":0})</script>\n"}}