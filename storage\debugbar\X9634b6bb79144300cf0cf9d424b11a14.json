{"__meta": {"id": "X9634b6bb79144300cf0cf9d424b11a14", "datetime": "2025-07-31 04:53:09", "utime": **********.161093, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937588.394686, "end": **********.161133, "duration": 0.7664470672607422, "duration_str": "766ms", "measures": [{"label": "Booting", "start": 1753937588.394686, "relative_start": 0, "end": **********.071304, "relative_end": **********.071304, "duration": 0.6766180992126465, "duration_str": "677ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.071328, "relative_start": 0.****************, "end": **********.161135, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "89.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "AcNim318hEwhzz82v8jiRES6EvOaF4mD6IFlSVDy", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1557303958 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1557303958\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1682148183 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1682148183\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-247198004 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-247198004\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2102870429 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2102870429\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:53:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRiNkNKS2EyM1ZlRHNxNHExeGdUdVE9PSIsInZhbHVlIjoiZFBCalJ4ZmtTMWQySmVmeHdQTWljZTVsMnkwT1pKbnlndmtTbStlVnhXRFd5b040RW53ZWo2QUR4cHlqLzB4OFNEWVRySktQQjdVQ2NUTlR4R1NoV1ZzVWV5bWQxME5jaHZvbWhlc2o1NVB2UW02MmlISWJTRExVWE81MGtpR0hQbWdFZTdzYjJmZVNPYTZNUmQxaUVnQnF6eXpMZkpKMWdIcHA1ejI3N3NoNmFDQS80Zm5IbWxZRlJSV083QXJiUnRXMm9XVzR1MXhiYU90b2tmNUMxZDUyU2VQM0ZxZ3gvQjlWQUZDVVllZk1BSjJKWUV3eGRJM1FldUZIUmd3aGh4dy9UVkFEQ0NkdVlnT1pRSmhUQ3lzRm8xWjBjZ3VCVEd1eDl4TEllQUVXQTVzYlJiMlplNFFtRDhSM3R6Q1JKTmQySHAzNjZhL1YyZ0lqSDhDR2QvdzB4VElJMlN6MUxwNytUZSsyT0hhZ1dwVVpIZ1JWeEFnTytOLzFHclBEbVRyQ3VmME9oSjRKdStvcno0ZzJ3dktDZWdLVGZaZkczZnZpVFIvOUs4cW5lVGtHYzF4TG9PN0VqbEoxbTEvQU1oTDNJSlhpV3BhMGt3UjljK2tvcWphWXA0N1JoSEtXTzNPOXVHeG96NUJhRzZCZTZYeXdBWURrdm93a3lYS2MiLCJtYWMiOiIwNDRlOWYzZmM5MzhjNzVmNGRjZTQ1NzNjOTdhMGJkYmY5MWU0Yzg1NGFmYjc3ZGIzY2VlMDMwODA1NTlmOTJhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:53:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlpSSGhJTDBTUXZvK0diT2xYbDNIY0E9PSIsInZhbHVlIjoicTJKTXJuMTFRVDRRQkNsTjc3U0dQZHc1ZXg2QXQzS0tzNTdScENOdjQ3ZjZiRUVVS2tUV2U2U3pmaG1YUnR4ZFJqWUdzcUV5azhDRjBhODB3TlMxbEdlVjdLa0QvR0lPVG94WFRMNXhWZDI2UGJHTC9VOVdtNS9idzhyQ1Q0TVdodjlWbnEybDNONWhJVzZKbm5WdDZxY0EzZXQ5NUhLRUN5UkxnWTRUbklzTDM4ZmVueHBnQXJ1UFNYV0RVVXh3bUhyRzBoS0FYMmcrVnlFTHRralhmNDlqNGY0SC9SWUd2dVQveklaZFNQYUc3VHIvQ0tDRjhUeFJwcE9kZEdTSDNhaTZIVzZSUFlnVDR1NEZsMGVxUW5zVW1IaFc1V3dLOWRSRnc2Q2NCUUZ3eDIxVHNSejBSS1dVUGdPMXNhdlpqQVhkeFBRUE5maUlBcUs0NjFEeUZwN252Y0dNaERHUkZqMWNFcVpnRG84MkgxVXgrSmtjWUtrQm1qOFNjUTlOUXY3STYzTmx5MVNPbWIxUktnVnNQMmNXVzEvckZBYW5GR2RobEU1REtOazNVZklFVnN6REVqVnNlWnBkaGcvZ0FtaTR3SDE2ckdJMzNTY2l0UDFETEZiQlFRVi9uKzB0VUZkdEM1bDdOZ0NBZ3g3MGdrcFVDR0tJaUtzb2RmbDkiLCJtYWMiOiJkODAyMDAzMzMwYWNjNzJlZmVmYTlmMjMwY2E2NjU2OTI2ODJmZDY5YjhlMWYyMTVmYWQwNTk2ZTFlNDRiYTBkIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:53:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRiNkNKS2EyM1ZlRHNxNHExeGdUdVE9PSIsInZhbHVlIjoiZFBCalJ4ZmtTMWQySmVmeHdQTWljZTVsMnkwT1pKbnlndmtTbStlVnhXRFd5b040RW53ZWo2QUR4cHlqLzB4OFNEWVRySktQQjdVQ2NUTlR4R1NoV1ZzVWV5bWQxME5jaHZvbWhlc2o1NVB2UW02MmlISWJTRExVWE81MGtpR0hQbWdFZTdzYjJmZVNPYTZNUmQxaUVnQnF6eXpMZkpKMWdIcHA1ejI3N3NoNmFDQS80Zm5IbWxZRlJSV083QXJiUnRXMm9XVzR1MXhiYU90b2tmNUMxZDUyU2VQM0ZxZ3gvQjlWQUZDVVllZk1BSjJKWUV3eGRJM1FldUZIUmd3aGh4dy9UVkFEQ0NkdVlnT1pRSmhUQ3lzRm8xWjBjZ3VCVEd1eDl4TEllQUVXQTVzYlJiMlplNFFtRDhSM3R6Q1JKTmQySHAzNjZhL1YyZ0lqSDhDR2QvdzB4VElJMlN6MUxwNytUZSsyT0hhZ1dwVVpIZ1JWeEFnTytOLzFHclBEbVRyQ3VmME9oSjRKdStvcno0ZzJ3dktDZWdLVGZaZkczZnZpVFIvOUs4cW5lVGtHYzF4TG9PN0VqbEoxbTEvQU1oTDNJSlhpV3BhMGt3UjljK2tvcWphWXA0N1JoSEtXTzNPOXVHeG96NUJhRzZCZTZYeXdBWURrdm93a3lYS2MiLCJtYWMiOiIwNDRlOWYzZmM5MzhjNzVmNGRjZTQ1NzNjOTdhMGJkYmY5MWU0Yzg1NGFmYjc3ZGIzY2VlMDMwODA1NTlmOTJhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:53:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlpSSGhJTDBTUXZvK0diT2xYbDNIY0E9PSIsInZhbHVlIjoicTJKTXJuMTFRVDRRQkNsTjc3U0dQZHc1ZXg2QXQzS0tzNTdScENOdjQ3ZjZiRUVVS2tUV2U2U3pmaG1YUnR4ZFJqWUdzcUV5azhDRjBhODB3TlMxbEdlVjdLa0QvR0lPVG94WFRMNXhWZDI2UGJHTC9VOVdtNS9idzhyQ1Q0TVdodjlWbnEybDNONWhJVzZKbm5WdDZxY0EzZXQ5NUhLRUN5UkxnWTRUbklzTDM4ZmVueHBnQXJ1UFNYV0RVVXh3bUhyRzBoS0FYMmcrVnlFTHRralhmNDlqNGY0SC9SWUd2dVQveklaZFNQYUc3VHIvQ0tDRjhUeFJwcE9kZEdTSDNhaTZIVzZSUFlnVDR1NEZsMGVxUW5zVW1IaFc1V3dLOWRSRnc2Q2NCUUZ3eDIxVHNSejBSS1dVUGdPMXNhdlpqQVhkeFBRUE5maUlBcUs0NjFEeUZwN252Y0dNaERHUkZqMWNFcVpnRG84MkgxVXgrSmtjWUtrQm1qOFNjUTlOUXY3STYzTmx5MVNPbWIxUktnVnNQMmNXVzEvckZBYW5GR2RobEU1REtOazNVZklFVnN6REVqVnNlWnBkaGcvZ0FtaTR3SDE2ckdJMzNTY2l0UDFETEZiQlFRVi9uKzB0VUZkdEM1bDdOZ0NBZ3g3MGdrcFVDR0tJaUtzb2RmbDkiLCJtYWMiOiJkODAyMDAzMzMwYWNjNzJlZmVmYTlmMjMwY2E2NjU2OTI2ODJmZDY5YjhlMWYyMTVmYWQwNTk2ZTFlNDRiYTBkIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:53:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AcNim318hEwhzz82v8jiRES6EvOaF4mD6IFlSVDy</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}