{"__meta": {"id": "X08852bfa3186f35cd194752a6b8d7eac", "datetime": "2025-07-31 04:48:44", "utime": **********.38952, "method": "GET", "uri": "/users/79/login-with-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937323.436138, "end": **********.389557, "duration": 0.9534189701080322, "duration_str": "953ms", "measures": [{"label": "Booting", "start": 1753937323.436138, "relative_start": 0, "end": **********.206514, "relative_end": **********.206514, "duration": 0.7703759670257568, "duration_str": "770ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.206532, "relative_start": 0.7703940868377686, "end": **********.38956, "relative_end": 3.0994415283203125e-06, "duration": 0.183027982711792, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44693048, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1450\" onclick=\"\">app/Http/Controllers/UserController.php:1450-1474</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01724, "accumulated_duration_str": "17.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.266639, "duration": 0.016399999999999998, "duration_str": "16.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 95.128}, {"sql": "select * from `users` where `users`.`id` = '79' limit 1", "type": "query", "params": [], "bindings": ["79"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\UserController.php", "line": 1452}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.290321, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "UserController.php:1452", "source": "app/Http/Controllers/UserController.php:1452", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FUserController.php&line=1452", "ajax": false, "filename": "UserController.php", "line": "1452"}, "connection": "radhe_same", "start_percent": 95.128, "width_percent": 4.872}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/users/79/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "79", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/79/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1216354102 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1216354102\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1279187988 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1279187988\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1537125553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1537125553\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2081542056 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/system-admin/companies</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2383 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9HUDdxN20rbDc0RlFteFJreTNjbnc9PSIsInZhbHVlIjoieTBsMXk3SmYrZ2I2bXZYZTM1WW1ZSiszRzlJQjRpb1FPKzFqZ3cwVHlrUXlQQjZqQ1dKdCtwSTlLaHMwK3hrOVZVMk9Uclo2NWxNdi9XVVNkUFh1d2FwVTRsWWVWVWRyWWovMk5wdG5QNWxneEhMYnFycDdXWW5BaWhNMk9ZYk9MemhuM3RrTXdzdU9vLytReGhXZWNtWUNOcVRiS3VNWWpNMUxzTG0zU0s0b1BoSFA4TysrWmFWMGQrNUU1cG9OU3VLZWVjUUF3VFVVVFpIMDg1czRQRVdjWHRlT2xRZW9oNFcwVkNLd081OD0iLCJtYWMiOiIwZDUwZDA0ZWY0MGUwNWRhN2RiMmJjODQyOTdhMTRkYTI2ZWM4NGRkNTNiMTY1MmRjYzc1ZTA4OWQxOTU0ODVjIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InUxaHVJS2o3dXd1RGxvY0tzMktldXc9PSIsInZhbHVlIjoiVDFZQkZjOWRZUktHSXk3aDN6eUE2cmZHUW1BM1p2WXg1ZXB6WUlxenNWM3VmUkgwY1BTbEdFcmc3M0J1bTN3SEZXWEszeVdYRnhObDVJWGhwcjFWd3ZCU1k0QzRYK3VQRC92ZGx0NmlWZ3BEbFQzbnpiMUtHSnBHdW85aG45UDJGRUd2ZHhuUEVCeFliK3BwY3BIUzc1bGREVncyTlB4VzExV0tvOUE5dFNmWTNXZzFISER2aC9GWkw3dWNYaDJOL21sM2FlUVQzaE95MzBWd0xEL0lZUVVxSWJpUE5mMTNiSThtODlBODdLK2J1ZVVCZXBsdmorK1lleGthdGl5aWdiVmlJNDI5VWh3TS9ucTh4MDBKSFpxdHFmVWpzNnRQSzRiMVp4NjZucldoYk1wZEJlQUtpRHM5ZzVjVUpwMDN0ZFBLTCtDOW05RWNha0wwMTJtY0JOV3A1eThjN2ZoK25WSVJkTGxxTlh1TURNTzdUWjhEK3FTcnYvQjh2Sk8xdWtibXdIZ2VUcUk2RGhCZkVwbmZERng3R1NXTUZzdzJ3K1NRbUpCZldUMEIxZXJPL1hpazlqWEduWDZxNHNhTFJjN0Y2UzczOXdBRytwYTJHMElPeDE5Y1JidVNwRTRLUkQ1YUtnaW1wYi9QeDBCekVVMGVFbVliVU1YUnhJbmciLCJtYWMiOiI2OTQ4ZjAyOGMxODZkMDg4NDUwZmUzODQ1ZTFhNTdmNzUyYjE5YTg2NGIyOTc1NWU1ZDM1MzRmODdmZjY3NTI1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InpMMEhUTkVLNGExNWwzd05NbFM0bEE9PSIsInZhbHVlIjoiaFlnUkRGa2YvL000NWsvaDZoK2w0V0RFcENxN2Vmd0swSXVVbVBUZTJOUWRYYWprTTN0aEhYdngzZk9NZlZ4WHN3djF1RzZ3dEtjM01OanFnVjhxOFBNUzh4ZndZZ3J2bExLVUxURFRKeXlkNGRHL3FRcGVheVJTMHZaVzA2TG9vNUtNR2ZUZ0lXcDJqRTJHRWk0Q2hDOFVqK0I4SVRkNFZrdjRHaEY5ZEk0NjhRQW41S0VTc1RSK0RJQ3hBMTZJTHo1Qy85c2F5S2Nwd3k4b0J6VytiODlGRGE2d3V3cmUrU204Vy9OcXFYbUZyQzYrbDVzZWV0NVc5Q3Z6YldVamRuSFd2TWJWWk45ZGRPY2g0dHRGZzZIVExMTDM0M3VTcVU5OStOaEprU1RMNHV1VE16RXVDTjVxYkwwcVNEcXhtZ1lYUWhkalIzVFRkbThNK0hyZmVTQXp5a3dNUWkyamExVGZWTktxTCtBU3p4d3JJZDlqTVRyRGxDbzJNd1pMOGVNM3EzN0tEWGxybmI5cW1UcW9HQU5PZHdTTmt4c1FJcWRHTVFObURhZ1FFU3d1cHB4VVBqVjhSRHZINzFkRmxqVmZZK2VteVpUMWR4R0Npd1hDWFU5QzNILzcxK0VReGFWY00xS0N0Qkh3b0NhOVR5amtWUm5uN0tCVmZQbzEiLCJtYWMiOiIwOTBlMmMzOTFiNDg1OGE3MTU5ZGVjZGM3YTA4NGQzZGY0NTEyOTc5MGEyMzUwOWZmNDAwMTI1ZDA1ZTAxMzE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081542056\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1576028163 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">UVeYwvySKA5PPFAkzc6iMcw1VaHAuVegcIgnjUUq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576028163\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1016338679 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5oajU1UXAxQ2xlTGxtbkZEc2d5cmc9PSIsInZhbHVlIjoiOStoeXRMNUdrWG1tNU5PQW9Nb0RZemYyNFVYVUNtYlNpK3JrcS9IQkNqKy93WFhFRDU0dFZwZC9OUms4L3Zab2RiUWlrZXhYMUFmWitqOEpSUkIvRnQyUTdwSUV5ajVzT1o4RWZjUHFvMXh6ZXAzUXR5Ukwyb2NuWC9Zd20wcytkQXp4Q28ySlN1cU5UeHB4VnpmY3J1WDdiN1dKNW5nck9aVS9KYm1Qd21YNytxMjY0WTJoWndyRXIveXQxZjZWekFpVHpqSEtsL0tUTGdWRWV5cVZQSGJMRllIKzF2cE1CNFBJYk9OWEdCR0UxaVJvd1hBVzV1dENXVkFKWURCanJrL1liQkVuSGFhVmc2TWM0Nm5aM01DNVhyazE0cWNJUDNOdGhic2NiZEhHeDJVY0d5MmxoNkpseXdMaXZhcWFPbFdSTmorNGVxa0FKWGhiWW9MVDhUUm9Vc1Fad2JLY25FT1J6dDNuSlo4VFd5VlBhNW9kd1ovektPRG1wbGEwRXFMZjdwWDBUa2hkMGZETlRnV01xbFJJeWZRZStFY1BkVUJsMEp2ajNCeW9ubm9oT3pESXZsMUlWaGdHWDZTdmlCVTNTTm9LSllBZWQ0MkJSSWZIak1mdThEWXROZFNYSVBLbDNWbS9TTXdXUlVuYWJVeUJCUVdUTUlYQ1puVWEiLCJtYWMiOiJlNDQ2MjQ1ZjMyYTcxMjA1ZjEwMjgyZWFjYTA4ZTRlNTliNmQyMTNmMGVkNWRhN2Q5OGE0ZmE4N2U2MWQ1YmQzIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikt3ZkNHMXppck9uV2NkT2s4WSszM2c9PSIsInZhbHVlIjoiUjY2eFpYSmlSUGxweStGbmZqd2NyWWdTcHJCeHQ2dzUxMERTMzBSUkJwcHBlbHpEVllXMzFzVlZxVEZianZPejFpWEw0STN4Y0M3ejFnbEFzMjhzUXlxM2p6dUFaNmpBdERQbzNBRDBVWnlFdkI0NGcya0JyMnVTU1Bncys3OGY0Sy92Ynk4SHZKempBTlF3eERURGZWMWYxdklsNCtDTzVQQVdHbDNoNWQyNFJjRGtGWkQ4S0I4YUxLNXY1cTBWczVJYTlrUmVSTkVWbHdMVjg1L1ZpaFV0TlFuYWlVZ2FOVVdCWHJ6eERrOEVteTFXa3NTT2Nic0taUHd5RlNjdEdYT01MZXVESmdNcUMwNy9za3pmUS9mRnliNERWTTRteHVhUlI1MHZIRVVPSG5pNGRSemhxM2JwaE5rMUVKMEtaU3FOQlVNWmV4enZKWmdMWVVHblRlRUZNTGswRTNFYmNsSTVyZ25qTHoyaFpxOW04WTJETlNGL1VFR0ViK0EwZktET1FGVGFCeWxwMjVIUGo0RkdJa1VjNnE3amFRTTJqNUhRVHFuRzgyL2YyYW1iek1aL0lWWFhGVnVyQytZUTZsUE5LcTBXaUZscWZOSEtJNTFURUdGOHV6aFE4N1J1bDVGNmQwaWYvZCtZRDQ2TEpWMnVSM2sybGVCKzFPMHkiLCJtYWMiOiJjMDVjNDlhNTQ5OTA1NmNkZmZmYTk1OGU0ZDBiM2MwZjlhYThjMWE2MzJiYTljNzQ0NGQzYjg5NTNjNzc4MDRlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5oajU1UXAxQ2xlTGxtbkZEc2d5cmc9PSIsInZhbHVlIjoiOStoeXRMNUdrWG1tNU5PQW9Nb0RZemYyNFVYVUNtYlNpK3JrcS9IQkNqKy93WFhFRDU0dFZwZC9OUms4L3Zab2RiUWlrZXhYMUFmWitqOEpSUkIvRnQyUTdwSUV5ajVzT1o4RWZjUHFvMXh6ZXAzUXR5Ukwyb2NuWC9Zd20wcytkQXp4Q28ySlN1cU5UeHB4VnpmY3J1WDdiN1dKNW5nck9aVS9KYm1Qd21YNytxMjY0WTJoWndyRXIveXQxZjZWekFpVHpqSEtsL0tUTGdWRWV5cVZQSGJMRllIKzF2cE1CNFBJYk9OWEdCR0UxaVJvd1hBVzV1dENXVkFKWURCanJrL1liQkVuSGFhVmc2TWM0Nm5aM01DNVhyazE0cWNJUDNOdGhic2NiZEhHeDJVY0d5MmxoNkpseXdMaXZhcWFPbFdSTmorNGVxa0FKWGhiWW9MVDhUUm9Vc1Fad2JLY25FT1J6dDNuSlo4VFd5VlBhNW9kd1ovektPRG1wbGEwRXFMZjdwWDBUa2hkMGZETlRnV01xbFJJeWZRZStFY1BkVUJsMEp2ajNCeW9ubm9oT3pESXZsMUlWaGdHWDZTdmlCVTNTTm9LSllBZWQ0MkJSSWZIak1mdThEWXROZFNYSVBLbDNWbS9TTXdXUlVuYWJVeUJCUVdUTUlYQ1puVWEiLCJtYWMiOiJlNDQ2MjQ1ZjMyYTcxMjA1ZjEwMjgyZWFjYTA4ZTRlNTliNmQyMTNmMGVkNWRhN2Q5OGE0ZmE4N2U2MWQ1YmQzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikt3ZkNHMXppck9uV2NkT2s4WSszM2c9PSIsInZhbHVlIjoiUjY2eFpYSmlSUGxweStGbmZqd2NyWWdTcHJCeHQ2dzUxMERTMzBSUkJwcHBlbHpEVllXMzFzVlZxVEZianZPejFpWEw0STN4Y0M3ejFnbEFzMjhzUXlxM2p6dUFaNmpBdERQbzNBRDBVWnlFdkI0NGcya0JyMnVTU1Bncys3OGY0Sy92Ynk4SHZKempBTlF3eERURGZWMWYxdklsNCtDTzVQQVdHbDNoNWQyNFJjRGtGWkQ4S0I4YUxLNXY1cTBWczVJYTlrUmVSTkVWbHdMVjg1L1ZpaFV0TlFuYWlVZ2FOVVdCWHJ6eERrOEVteTFXa3NTT2Nic0taUHd5RlNjdEdYT01MZXVESmdNcUMwNy9za3pmUS9mRnliNERWTTRteHVhUlI1MHZIRVVPSG5pNGRSemhxM2JwaE5rMUVKMEtaU3FOQlVNWmV4enZKWmdMWVVHblRlRUZNTGswRTNFYmNsSTVyZ25qTHoyaFpxOW04WTJETlNGL1VFR0ViK0EwZktET1FGVGFCeWxwMjVIUGo0RkdJa1VjNnE3amFRTTJqNUhRVHFuRzgyL2YyYW1iek1aL0lWWFhGVnVyQytZUTZsUE5LcTBXaUZscWZOSEtJNTFURUdGOHV6aFE4N1J1bDVGNmQwaWYvZCtZRDQ2TEpWMnVSM2sybGVCKzFPMHkiLCJtYWMiOiJjMDVjNDlhNTQ5OTA1NmNkZmZmYTk1OGU0ZDBiM2MwZjlhYThjMWE2MzJiYTljNzQ0NGQzYjg5NTNjNzc4MDRlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016338679\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1896686306 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">A59q06MV63bJBouPOoBkXQ9CdIKQCAa52UTMrtTR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"49 characters\">http://127.0.0.1:8000/users/79/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>79</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1896686306\", {\"maxDepth\":0})</script>\n"}}