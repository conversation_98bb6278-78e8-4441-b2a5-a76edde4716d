{"__meta": {"id": "Xa96083f014eddc102fc4411c84a3ce90", "datetime": "2025-07-31 04:54:27", "utime": **********.000092, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937665.894168, "end": **********.000149, "duration": 1.1059811115264893, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1753937665.894168, "relative_start": 0, "end": 1753937666.923298, "relative_end": 1753937666.923298, "duration": 1.029129981994629, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753937666.923326, "relative_start": 1.***************, "end": **********.000152, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "76.83ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "tqQkZMsOGxUX1W91xFPnB6IL9FDiQuRFDPl8Yc7m", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-456853963 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-456853963\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-357847723 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-357847723\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1191859936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1191859936\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-956494813 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956494813\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1073122862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1073122862\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1697709220 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:54:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFQbXZhMGZkcFRWRy9lK01XamRoaVE9PSIsInZhbHVlIjoiODZjUnlhejBZU1F3Q2I1R0FKU2owY08rb1M0VkIzSHM2a01WUnMyMENxdGNZRk5SNURBS1BHWFl4dlZBcHZiNXFUdkFqd2hIZm1YWEVDTUp0S0o4WUtzSTg2OGR2S1BpcTk2d3NCZTMxa3QzM0hzYnJtK0dyVElYMVdSWUp0TVNPdG1QODNvRXlpTzJpcE5CY1NPT0tXQksyQ0cwWXFlREZod1hDbFhsbkp2N2M2RE8veFRIb3MyRkNib3ZNelBIRDlPVHBZYU5FeTNpdG56QVBBV0hSbVhFZ1BkUW0rSVF2bFNrSmFER09xcy9QNnNCdjh0UTA1YkpzZE9lcGZ0UmFzcEJRNHFyVXdENXl5OXdWWjc5dDR1SW9vZ0dyKzVrWERHcDZEbHQ1TS9peEhROWFnUnpJWDQxbUJiK0ZTVVFvQlVyaUgwQ1NHNzJDMGExK3Y5NmZWeXVvOHB6cUlnNnpRcEdVVTNTTDVRR0ZNc0U5OHh0bUErNlRGVHhYTFRpckFITXJ2MmZTRTJXWDJQMkZJdkR1em5reFZYSWVjYnlpU0Nld3NaTFlIemVEcDJCSkxzNEs5Y01pbW9xVUV5R2ZrQ2hreXp4aExjNEI2dDFsczYzdU5QVmd4WldJanFSaGo5a2lrbFg4MzRiYnY1OTNpKzJZMVAwQW4vMW5oMmQiLCJtYWMiOiJjNjJkYmZhNjc5ZGE4Mjg1OTVkZWI4ZTIyNDhjZWFkZmNkOTNjNDgwMzZhZmIwZWIwYWY0NTU1MTE0ZmFhMzUxIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:26 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlhhczNLanFBRDFsWStMWEUvTDE3b3c9PSIsInZhbHVlIjoiUlAxWnNmSHFnSzRvMnFFdlcxekNyc2pYRFR0b1FabEZKME1aZlVKcUUyU2syK2p2QldXYktCeDRzdVhqbU9kMGMrQWFLUTRDZXdMSHJyTjJWTmlFRnVhVk5sZGFIRkVJVU5NWkhoQnQ1aWoxR2JJVURCV2tNYTNneHRTQ25Cc0FWbFRPeG1tOGRkbzJZaE9CY1o2a29sS2lYRFpwS2ZqNzJGMm9JYTlzOWFCVnB0YmFvdmJDaEp0c1JaT0tRRjhWb3JnQ1FDODZmSkt4S0hWR1BxZElsd1BLSTY3L1VWL0N2QkpvRmlJTm1lTEdmSlF6MUl1QkZTN1Q5L2NkWXYwakphODFDMHJvMm1xd2JKQmxkdUJna2p0K3FDblhTdmUrMXcyd2lVWVorb1hLeHlWdGsrQzhWUlE3UUVQUHBQdjJ4VmY4bHlTMHAzQXp0eG83aTYwc3d4cnI4L3N6OVJKZjVrMXZCL1diRGRKaDMyRVdzdFJvSXF4QngxcnQ0TjAzeGxuQmJ0SWlUSjNnVnRpbFZhKzRBWHFxU2hoSzIwMytPdE9SOUU4cGI2QlpDRTlNZGlrUWRFOHp0UmNtcjNNRllBcHIrckdyQlM5ekJ1d282aStIWmx2clg3ZVFsbzloQ1ZsTDJVWmtXM3A1bTYyNUtWclhmM3RLSVpHKzFuVE8iLCJtYWMiOiI2YjZiYTJmM2E1ZjJhNWYxOWVkMTYzYWUyYjVkYjg5YjQwYzQ2ZGIxYjdkNGJlYmQyMjVkZTRmZWViYWRiOGNlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:26 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFQbXZhMGZkcFRWRy9lK01XamRoaVE9PSIsInZhbHVlIjoiODZjUnlhejBZU1F3Q2I1R0FKU2owY08rb1M0VkIzSHM2a01WUnMyMENxdGNZRk5SNURBS1BHWFl4dlZBcHZiNXFUdkFqd2hIZm1YWEVDTUp0S0o4WUtzSTg2OGR2S1BpcTk2d3NCZTMxa3QzM0hzYnJtK0dyVElYMVdSWUp0TVNPdG1QODNvRXlpTzJpcE5CY1NPT0tXQksyQ0cwWXFlREZod1hDbFhsbkp2N2M2RE8veFRIb3MyRkNib3ZNelBIRDlPVHBZYU5FeTNpdG56QVBBV0hSbVhFZ1BkUW0rSVF2bFNrSmFER09xcy9QNnNCdjh0UTA1YkpzZE9lcGZ0UmFzcEJRNHFyVXdENXl5OXdWWjc5dDR1SW9vZ0dyKzVrWERHcDZEbHQ1TS9peEhROWFnUnpJWDQxbUJiK0ZTVVFvQlVyaUgwQ1NHNzJDMGExK3Y5NmZWeXVvOHB6cUlnNnpRcEdVVTNTTDVRR0ZNc0U5OHh0bUErNlRGVHhYTFRpckFITXJ2MmZTRTJXWDJQMkZJdkR1em5reFZYSWVjYnlpU0Nld3NaTFlIemVEcDJCSkxzNEs5Y01pbW9xVUV5R2ZrQ2hreXp4aExjNEI2dDFsczYzdU5QVmd4WldJanFSaGo5a2lrbFg4MzRiYnY1OTNpKzJZMVAwQW4vMW5oMmQiLCJtYWMiOiJjNjJkYmZhNjc5ZGE4Mjg1OTVkZWI4ZTIyNDhjZWFkZmNkOTNjNDgwMzZhZmIwZWIwYWY0NTU1MTE0ZmFhMzUxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlhhczNLanFBRDFsWStMWEUvTDE3b3c9PSIsInZhbHVlIjoiUlAxWnNmSHFnSzRvMnFFdlcxekNyc2pYRFR0b1FabEZKME1aZlVKcUUyU2syK2p2QldXYktCeDRzdVhqbU9kMGMrQWFLUTRDZXdMSHJyTjJWTmlFRnVhVk5sZGFIRkVJVU5NWkhoQnQ1aWoxR2JJVURCV2tNYTNneHRTQ25Cc0FWbFRPeG1tOGRkbzJZaE9CY1o2a29sS2lYRFpwS2ZqNzJGMm9JYTlzOWFCVnB0YmFvdmJDaEp0c1JaT0tRRjhWb3JnQ1FDODZmSkt4S0hWR1BxZElsd1BLSTY3L1VWL0N2QkpvRmlJTm1lTEdmSlF6MUl1QkZTN1Q5L2NkWXYwakphODFDMHJvMm1xd2JKQmxkdUJna2p0K3FDblhTdmUrMXcyd2lVWVorb1hLeHlWdGsrQzhWUlE3UUVQUHBQdjJ4VmY4bHlTMHAzQXp0eG83aTYwc3d4cnI4L3N6OVJKZjVrMXZCL1diRGRKaDMyRVdzdFJvSXF4QngxcnQ0TjAzeGxuQmJ0SWlUSjNnVnRpbFZhKzRBWHFxU2hoSzIwMytPdE9SOUU4cGI2QlpDRTlNZGlrUWRFOHp0UmNtcjNNRllBcHIrckdyQlM5ekJ1d282aStIWmx2clg3ZVFsbzloQ1ZsTDJVWmtXM3A1bTYyNUtWclhmM3RLSVpHKzFuVE8iLCJtYWMiOiI2YjZiYTJmM2E1ZjJhNWYxOWVkMTYzYWUyYjVkYjg5YjQwYzQ2ZGIxYjdkNGJlYmQyMjVkZTRmZWViYWRiOGNlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697709220\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1369998122 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">tqQkZMsOGxUX1W91xFPnB6IL9FDiQuRFDPl8Yc7m</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369998122\", {\"maxDepth\":0})</script>\n"}}