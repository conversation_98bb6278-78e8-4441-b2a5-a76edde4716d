{"__meta": {"id": "Xcc15cadb96ce7b9f5ec3ac78a750c73d", "datetime": "2025-07-31 04:55:40", "utime": **********.658952, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937739.818308, "end": **********.658991, "duration": 0.8406829833984375, "duration_str": "841ms", "measures": [{"label": "Booting", "start": 1753937739.818308, "relative_start": 0, "end": **********.597657, "relative_end": **********.597657, "duration": 0.7793488502502441, "duration_str": "779ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.597672, "relative_start": 0.****************, "end": **********.658993, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "61.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "B3I7P0g8QBzDW0bBFHXz6UoxFMcTUOCfJEg9bIgN", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1860796963 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1860796963\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-323306749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-323306749\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-954326009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-954326009\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-377745449 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377745449\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-398691721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-398691721\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-394874002 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:55:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtxK2hhYjEwSVE3SHFYZ05SdU1BWkE9PSIsInZhbHVlIjoiRDlFWVV2NmtRSUV6bm82ZTJHUk83U05QOUUvWDkrbjJxaU5KNzZ2dmZuL1hCb2hsTkxTWllSejIzb1p5K2NONVB3d3JhWlpvWnNlNlFPMSs5bm9hU24zSEtCMjE5YXhpMFcrd09mVzVZSWs0RWdIc3U4VDV6cXNxM29YSGE0SElBcFNlMnYySWZrZXYwUjlsdE9xaFlMbHZyYS9FYVVjRDF5d3AxMklXQ0F5ZHczNHZZeDBTRVYzS1Bhd2cycWRWaW0vZCt2eEd2TmhSWTFGZWJ5TTNzWHRNeFU0ME1vZ0dKWmVWTGptRnNrMGNsaWJHd0x2Z3E4U2J4eFBmK1loSjFEdE0zNEJzZFZDRlVhcFl5WlhBZVBWbXArQkMyZGlWYU5MeVNUbGJhRnMya3p5Z1NGWENGZFNrclFWZzgwR3B5TEhkcTN3Y1VFbzBhRitjTDF2YUNXczhBc3RvZS8xM0tlMDhVZVpiMEpYSlg4WDcyRkUrVFJWak95cWlTUytPMnZNK25DRHVDaDBqNnBxbWhSa1ZEL0lhVWFkSC9VclhzK01yaVFpNERHVWxaOWVkRXNBcGtHaFF4MHd2UWxyQ0ViZTNYSDZtVWdvQlRKTnVSeHRyZklLbVUxRmNFNEtjSE1jYTAwT1oreU00Nk9wQk00cUlFTGU1UTJlaDBmaUUiLCJtYWMiOiI5MGFiYjk5YTU5MWY1NDUwZjllY2RlMmRmNzg3OTE3ZDM2YWNkMzEwN2NhMGNmMjQ1NGRhZjY0NDY4MWEyZTdiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:55:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlJGZnFqR2hiMVUrSzBYWWxSdVVvY1E9PSIsInZhbHVlIjoianZyZkxxeHBKdEVJOGtTYzZXM1g3S0hrT2VwRTQ3aElGbFlETkNFbnVtajhWbmFzMUpodXhIaGVDdCtiVVZNeVZidDI3bnVuOWYwQjJLMUhacDZ5MWRxSDBySWlLeGJNZEdqdlRBRmoxUzFoMzQrZ3Z2VkVBZWt0OGJ1aFBTbDNFYWdiUHZyc2N3aXl4ZE1VSVZBQURzVTlHdEFreUphSHBmaDFUQlVrb1ZFMGkreW94MEIvOFV2TEo2RlFZWE9tRHhXT1hOeVI0c0M3MU9ZaXcxeUNwMW1MMmdDS2srTGMzM2F2RmVSWTJ6TkhuREdEdFQrNFZJWFFybERjUmUxU25sUVd2V3RpbW9LRkhxZmZldWRyK3k4L1BYejdZSnFLZzkvWDdqM1NpdkUxSGZZUGZQRGVlbUJ5Z05EVDdlaE45SjFjUFFRWEcxcnY5Nk03RUYrbUV2cWxsSng3d3Nsc2loVGdMQ0ttdWkzM3VXSndPeWZRemJINk9qRlZteHpuUytjblF3VU5GRjRFcC9jUWlvRWpoQm4ra2NTUkVSMXhlOW43SVovb0J5dlI4ZCszZU1BMVlTbGZ1bng4bjBySTM4bkQrQzN0MkRHRWw4eU53L1A0K21MYkh1cWM5OGpyVGk5dzN5ajZaVXVXR0NKeFpGcnR5V2tTbzJrcWxJbm8iLCJtYWMiOiJhMjVmNDc1YWU4YThmNzFjNDAyNGJmODZjMDVlODBiNzJkMGVjZDlkOGIxZWJiNDU1MjZmYjViMjg5ODFjZWM5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:55:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtxK2hhYjEwSVE3SHFYZ05SdU1BWkE9PSIsInZhbHVlIjoiRDlFWVV2NmtRSUV6bm82ZTJHUk83U05QOUUvWDkrbjJxaU5KNzZ2dmZuL1hCb2hsTkxTWllSejIzb1p5K2NONVB3d3JhWlpvWnNlNlFPMSs5bm9hU24zSEtCMjE5YXhpMFcrd09mVzVZSWs0RWdIc3U4VDV6cXNxM29YSGE0SElBcFNlMnYySWZrZXYwUjlsdE9xaFlMbHZyYS9FYVVjRDF5d3AxMklXQ0F5ZHczNHZZeDBTRVYzS1Bhd2cycWRWaW0vZCt2eEd2TmhSWTFGZWJ5TTNzWHRNeFU0ME1vZ0dKWmVWTGptRnNrMGNsaWJHd0x2Z3E4U2J4eFBmK1loSjFEdE0zNEJzZFZDRlVhcFl5WlhBZVBWbXArQkMyZGlWYU5MeVNUbGJhRnMya3p5Z1NGWENGZFNrclFWZzgwR3B5TEhkcTN3Y1VFbzBhRitjTDF2YUNXczhBc3RvZS8xM0tlMDhVZVpiMEpYSlg4WDcyRkUrVFJWak95cWlTUytPMnZNK25DRHVDaDBqNnBxbWhSa1ZEL0lhVWFkSC9VclhzK01yaVFpNERHVWxaOWVkRXNBcGtHaFF4MHd2UWxyQ0ViZTNYSDZtVWdvQlRKTnVSeHRyZklLbVUxRmNFNEtjSE1jYTAwT1oreU00Nk9wQk00cUlFTGU1UTJlaDBmaUUiLCJtYWMiOiI5MGFiYjk5YTU5MWY1NDUwZjllY2RlMmRmNzg3OTE3ZDM2YWNkMzEwN2NhMGNmMjQ1NGRhZjY0NDY4MWEyZTdiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:55:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlJGZnFqR2hiMVUrSzBYWWxSdVVvY1E9PSIsInZhbHVlIjoianZyZkxxeHBKdEVJOGtTYzZXM1g3S0hrT2VwRTQ3aElGbFlETkNFbnVtajhWbmFzMUpodXhIaGVDdCtiVVZNeVZidDI3bnVuOWYwQjJLMUhacDZ5MWRxSDBySWlLeGJNZEdqdlRBRmoxUzFoMzQrZ3Z2VkVBZWt0OGJ1aFBTbDNFYWdiUHZyc2N3aXl4ZE1VSVZBQURzVTlHdEFreUphSHBmaDFUQlVrb1ZFMGkreW94MEIvOFV2TEo2RlFZWE9tRHhXT1hOeVI0c0M3MU9ZaXcxeUNwMW1MMmdDS2srTGMzM2F2RmVSWTJ6TkhuREdEdFQrNFZJWFFybERjUmUxU25sUVd2V3RpbW9LRkhxZmZldWRyK3k4L1BYejdZSnFLZzkvWDdqM1NpdkUxSGZZUGZQRGVlbUJ5Z05EVDdlaE45SjFjUFFRWEcxcnY5Nk03RUYrbUV2cWxsSng3d3Nsc2loVGdMQ0ttdWkzM3VXSndPeWZRemJINk9qRlZteHpuUytjblF3VU5GRjRFcC9jUWlvRWpoQm4ra2NTUkVSMXhlOW43SVovb0J5dlI4ZCszZU1BMVlTbGZ1bng4bjBySTM4bkQrQzN0MkRHRWw4eU53L1A0K21MYkh1cWM5OGpyVGk5dzN5ajZaVXVXR0NKeFpGcnR5V2tTbzJrcWxJbm8iLCJtYWMiOiJhMjVmNDc1YWU4YThmNzFjNDAyNGJmODZjMDVlODBiNzJkMGVjZDlkOGIxZWJiNDU1MjZmYjViMjg5ODFjZWM5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:55:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-394874002\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-791144365 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">B3I7P0g8QBzDW0bBFHXz6UoxFMcTUOCfJEg9bIgN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-791144365\", {\"maxDepth\":0})</script>\n"}}