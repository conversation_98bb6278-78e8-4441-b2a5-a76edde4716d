{"__meta": {"id": "Xb37742865a5841121844de9282fffa4e", "datetime": "2025-07-31 04:48:27", "utime": **********.201167, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937306.148033, "end": **********.20124, "duration": 1.0532071590423584, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1753937306.148033, "relative_start": 0, "end": **********.099158, "relative_end": **********.099158, "duration": 0.9511251449584961, "duration_str": "951ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.099303, "relative_start": 0.****************, "end": **********.201264, "relative_end": 2.384185791015625e-05, "duration": 0.*****************, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "K1tJQtAMl1OxWmQZP3uPoNIRzkjf6AEdQwWtj2dp", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-1919322949 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1919322949\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1538635480 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1538635480\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2097658023 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2097658023\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1770723469 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770723469\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1208997451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1208997451\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZldUFNNUlCeHlDTkhlYjRHWnpoMFE9PSIsInZhbHVlIjoiM0lWZTJDWEhYWmdvd1FJOGlLa2N0dExNSWR3M1pOVUhjT3ErZ1F2c3MzelhydUZQMkgvZkRPMHN6UmdzODMybkJwMFkzd1Nlbmp2WEZCZnpsTzBkU3F2akFROS95dDJyNEdtd0N1OFFPeEZwb2xUNGxPMkVnUjlQVEEybWQ0RWsrNnk3OG1HaTNTU1ZKcG5DYmcwZGdETS8rWUdMcmJwcXM0UFpGR1puaXFTNWk1WTZVQkFKVE9sK2YwWjZqK1NpZXl3OUhlYXYxek9IU3lYQ0NMd1kxWGFFSnVLekhCdjFMcStNV1E1dGFEMzVaaDcrVnlCaE5ValVQNkZNWHJXcEJkdkw4MTNvb01UUkZzZHFjaXlMaU5zMkM2R0liZmFXS3RPbnp2d1YvVlRySXZTMExKR0tXNVhuOTc1REJrdDlTZzg5NnNqenJvMWpudXcyUndTV1BKSVNnUzdlUUJZT2ZSakNLNDBkblJOOXZJMDN1QytOODYvU0l4N05lWkR6dU9NQXJWWlpUR2hYY2MvUktrelBuN3phaGRqM290dnkxM3JTUHRnRXlkVFhVMm0vaG10TjEraGg4WUUrMlM5dlhBTnZvZW5GcW1tZGpsS3ZpUUhqSjF1MEZIQjlnc3BPUUlsQ1JCeW9peUgxSW5MNDcvdkVzTXJVK3BBL0RJQWgiLCJtYWMiOiI1N2VkYzMzNzFlNWJmMGI4NTcwMDVlYjg2MzJjNDBhNmJhMDYzYWZjNDRhNjliOWU0ZWJlNDEzMWM5ZGFmNTIwIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImVMMEtaY3FhZ1BpRU1iVjZ3VUFzdnc9PSIsInZhbHVlIjoiVXhIYnFtczRvYmVFLzVLSUVwbXN5OHdIM25HSzFqd1NvRUVrOW5kSVV4WUVBTUtvTWlMdEFuVUw1ZzNGYTNRVzk1dEpCdDFWVUFNNHc4b0R0Nk1UQk4rczdlMERGV2JkYjZrb25FdTV1emY1eUYxWStLc0NZeDRIVVFXN25vRWpUbXNWRXJOcElqSWFqK0IreHhaTkVhNjAyVFBMbklQcE54bVBHeUY3d2tlc3hiNEppeGJtcjRJb0ZUQ2hDSlJjalYwbW5Bc1VUOXdtUmcybVBrR3JESENndVVRYmoyV1RiQkVjendxM0dsdExCSWlzaVk0bFBCL3hpSm4wT3pSMUxta0crUThHWjEwNzdCR3BOdS9YblRJNzBRRTVZOE03SGRUdG1PMzdZWGNYUjVsMzN4REl5YkgvQmVVVVd0UjZjejhIUjJlVkRYSlo2SGtNbElyZThGbm9XUnBYcHNvclJTb1RTWGZ4WjBPQWN1RGlYS0JYSGtiM1VVUVhITFE2NlJmQnZKSnFNZmppdlpsL2lLODE3V2xKUCt0UGNUQUZmTFU3am1GVHcwMVE0UlFQWG1mS0I2cFl2NW5uSW1oeGdRL1hUVXFYODNtVnpiNDJvam9FTWxFWmNVK2pEdnJWK0ZPU2JVU1B3V0M1eXVwTXV3bUdtNWhMSmNsUElkZDkiLCJtYWMiOiIwNDRiZWZjMzZhNDAwZjQ1ZTRjMzU0MjRlY2Y2Zjg3YjQwZDg0NWUxNmRkMWU5ZmRmZjI4NGQwOTQwOTU2YzMyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZldUFNNUlCeHlDTkhlYjRHWnpoMFE9PSIsInZhbHVlIjoiM0lWZTJDWEhYWmdvd1FJOGlLa2N0dExNSWR3M1pOVUhjT3ErZ1F2c3MzelhydUZQMkgvZkRPMHN6UmdzODMybkJwMFkzd1Nlbmp2WEZCZnpsTzBkU3F2akFROS95dDJyNEdtd0N1OFFPeEZwb2xUNGxPMkVnUjlQVEEybWQ0RWsrNnk3OG1HaTNTU1ZKcG5DYmcwZGdETS8rWUdMcmJwcXM0UFpGR1puaXFTNWk1WTZVQkFKVE9sK2YwWjZqK1NpZXl3OUhlYXYxek9IU3lYQ0NMd1kxWGFFSnVLekhCdjFMcStNV1E1dGFEMzVaaDcrVnlCaE5ValVQNkZNWHJXcEJkdkw4MTNvb01UUkZzZHFjaXlMaU5zMkM2R0liZmFXS3RPbnp2d1YvVlRySXZTMExKR0tXNVhuOTc1REJrdDlTZzg5NnNqenJvMWpudXcyUndTV1BKSVNnUzdlUUJZT2ZSakNLNDBkblJOOXZJMDN1QytOODYvU0l4N05lWkR6dU9NQXJWWlpUR2hYY2MvUktrelBuN3phaGRqM290dnkxM3JTUHRnRXlkVFhVMm0vaG10TjEraGg4WUUrMlM5dlhBTnZvZW5GcW1tZGpsS3ZpUUhqSjF1MEZIQjlnc3BPUUlsQ1JCeW9peUgxSW5MNDcvdkVzTXJVK3BBL0RJQWgiLCJtYWMiOiI1N2VkYzMzNzFlNWJmMGI4NTcwMDVlYjg2MzJjNDBhNmJhMDYzYWZjNDRhNjliOWU0ZWJlNDEzMWM5ZGFmNTIwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImVMMEtaY3FhZ1BpRU1iVjZ3VUFzdnc9PSIsInZhbHVlIjoiVXhIYnFtczRvYmVFLzVLSUVwbXN5OHdIM25HSzFqd1NvRUVrOW5kSVV4WUVBTUtvTWlMdEFuVUw1ZzNGYTNRVzk1dEpCdDFWVUFNNHc4b0R0Nk1UQk4rczdlMERGV2JkYjZrb25FdTV1emY1eUYxWStLc0NZeDRIVVFXN25vRWpUbXNWRXJOcElqSWFqK0IreHhaTkVhNjAyVFBMbklQcE54bVBHeUY3d2tlc3hiNEppeGJtcjRJb0ZUQ2hDSlJjalYwbW5Bc1VUOXdtUmcybVBrR3JESENndVVRYmoyV1RiQkVjendxM0dsdExCSWlzaVk0bFBCL3hpSm4wT3pSMUxta0crUThHWjEwNzdCR3BOdS9YblRJNzBRRTVZOE03SGRUdG1PMzdZWGNYUjVsMzN4REl5YkgvQmVVVVd0UjZjejhIUjJlVkRYSlo2SGtNbElyZThGbm9XUnBYcHNvclJTb1RTWGZ4WjBPQWN1RGlYS0JYSGtiM1VVUVhITFE2NlJmQnZKSnFNZmppdlpsL2lLODE3V2xKUCt0UGNUQUZmTFU3am1GVHcwMVE0UlFQWG1mS0I2cFl2NW5uSW1oeGdRL1hUVXFYODNtVnpiNDJvam9FTWxFWmNVK2pEdnJWK0ZPU2JVU1B3V0M1eXVwTXV3bUdtNWhMSmNsUElkZDkiLCJtYWMiOiIwNDRiZWZjMzZhNDAwZjQ1ZTRjMzU0MjRlY2Y2Zjg3YjQwZDg0NWUxNmRkMWU5ZmRmZjI4NGQwOTQwOTU2YzMyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-116684538 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K1tJQtAMl1OxWmQZP3uPoNIRzkjf6AEdQwWtj2dp</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116684538\", {\"maxDepth\":0})</script>\n"}}