{"__meta": {"id": "Xf1af0c735bb5e15fd8b2931acb80f00a", "datetime": "2025-07-31 04:43:08", "utime": **********.51989, "method": "GET", "uri": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753936987.293667, "end": **********.51992, "duration": 1.2262530326843262, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": 1753936987.293667, "relative_start": 0, "end": **********.433961, "relative_end": **********.433961, "duration": 1.140293836593628, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.433977, "relative_start": 1.****************, "end": **********.519923, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "85.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "EllIJfpOf8kgdVTFMkUO4B9GphXBgGHFQDdspyBx", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png", "status_code": "<pre class=sf-dump id=sf-dump-345362839 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-345362839\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-130539179 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-130539179\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-268207192 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-268207192\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1978726438 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978726438\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1998219818 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1998219818\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1314901906 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:43:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijd2ald2dW9rOWRvVUZ5NUd6Q29qd3c9PSIsInZhbHVlIjoiUTA4dlZBRnJqWDVsTW9CMzN0eG1HQTc4VFlTc0V1dGxSS0ZldkxhL0J6enRLZFBZTDRoVjZVdmgzRjJtemdOM21uUHJwL25MR05qdzZFdmU5eFIxTU1hWEdGQ0JTbVM1cHR0SjRDL1NoS21ockt1V2ZTaldsb0FTcm5vTXloUVRid0REOVNnRlpZOVAxZVdCSTA4dTRIMzk2b3lLdkVRRXdmZmxDbjA4SkdVTHRKYXladUdWTWU1OUY1NFVlaVpLbTkwa1JWVHNzNWZpUVNMdEc1cHJJTjRUVTR1V0lCczR6UmZTQlpRdEo5M28wbkFWcUVScVNYT3htVjNZeUl0M2FhWWRzSFVFOFBTNDZ3MFovWXU1eC9tN3hhd0JvTHAwQWdMdWRUUzlRT3lWSnJtSHRRdHpid1R3NWxKTmtEZDBUUjVBV1ZjNE5FWW0yaWlTL0lhbTRSZGRNUCt0MDZZUHVUSFcwalVRWnRWT2pZWnVSNUUzL0RVbWEvYVhtWlg5VStTN3VPZGkwZloyVWdjb0V1T0FnWTJYOUZzY2dTdHREK1FkN1FPWVNLZnBkaWVHZVhjVlMzblg2Wmc4M2hpUkZ6S2YrMjNOR2RFRktpSFR6ZWM4eTFKQXVCbmt4RlZRTk92dndiMi95WXdVMjc3T0plZGFPT1JkSU1uamZKNmEiLCJtYWMiOiI3NGE4MjgxMTg3Yzc5OWMxNWJlOWUxYzQ3M2Y0MjFmMDdmYThiZjc5YzUyNzI5MTAyOTljODFlYmZkNTk1NjliIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNDaVlrTFlFWDRlSGpPYmdtT1N2Vnc9PSIsInZhbHVlIjoiOHdxeGloTWpBOFhUTFgxM3VVTWdmNG03TE9lWFUrZElBSDYwSlJLVno4WUxyblJkTVpGWUZGMXMrbFF2eC9DS3VYa2VmbEQrK1lFT0E2ZE1uZTloMDRyVHV2MnlEcHROVVd4RE85UWx3VHIxWmtlSzVmNGpnWHZPei82SE9lQjA0RXFDTzJLT1BCZ2twbFZKTFNoY2F6VXRKZWlQNTREdzVTaXdTN0x0cVRjQ1JrQlBLTk9MOE4vUStZTkxZQ2tNQmkwamhuVCsvaFA4aXBSek4vRzF1bGErWEVMdm0yMmluWnZ2dXlZK1VxZ3N4T0UwQTNLRmdtZGZwcFJzUkF6MmtTL242WGdobjVXakpoakZwUTQ0Z2Rlei9jeUZ2K0NuMEpDMnplVDNlenB2ekxTUmMrbTdOaGJXRGIwMit5WCtTQUVpSlBDd2UyaDJKZzh1d09veFZPTFkvYjJIbjVuYkxrOGhPQWhFRUJQdWQ4T3l0a0huV3BrOEZBa280bkN0cmYrWkFSYUFRRExHRHJJUFFjK21JSEo0bEgraUwwbWczcTZjSG56dzRwTmIvclhqV0ZSVEwwRk0weW16VUZwUllZWWI4akZRN3BNaW1qVDBLYTlxWWgrUGFrUVBJN21pT0FEVnJ5Z25KSmUvdVZzYmt0UHN5MW85REdzck5USmUiLCJtYWMiOiI4YTgxZDhjNmQ2YTExOTE3Zjg3YmZkNzViMWVmMzIxYjIzMjdhYmEwNmY4MTRjZjEwZDIwMzJlYTk0M2I1YjcyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:43:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijd2ald2dW9rOWRvVUZ5NUd6Q29qd3c9PSIsInZhbHVlIjoiUTA4dlZBRnJqWDVsTW9CMzN0eG1HQTc4VFlTc0V1dGxSS0ZldkxhL0J6enRLZFBZTDRoVjZVdmgzRjJtemdOM21uUHJwL25MR05qdzZFdmU5eFIxTU1hWEdGQ0JTbVM1cHR0SjRDL1NoS21ockt1V2ZTaldsb0FTcm5vTXloUVRid0REOVNnRlpZOVAxZVdCSTA4dTRIMzk2b3lLdkVRRXdmZmxDbjA4SkdVTHRKYXladUdWTWU1OUY1NFVlaVpLbTkwa1JWVHNzNWZpUVNMdEc1cHJJTjRUVTR1V0lCczR6UmZTQlpRdEo5M28wbkFWcUVScVNYT3htVjNZeUl0M2FhWWRzSFVFOFBTNDZ3MFovWXU1eC9tN3hhd0JvTHAwQWdMdWRUUzlRT3lWSnJtSHRRdHpid1R3NWxKTmtEZDBUUjVBV1ZjNE5FWW0yaWlTL0lhbTRSZGRNUCt0MDZZUHVUSFcwalVRWnRWT2pZWnVSNUUzL0RVbWEvYVhtWlg5VStTN3VPZGkwZloyVWdjb0V1T0FnWTJYOUZzY2dTdHREK1FkN1FPWVNLZnBkaWVHZVhjVlMzblg2Wmc4M2hpUkZ6S2YrMjNOR2RFRktpSFR6ZWM4eTFKQXVCbmt4RlZRTk92dndiMi95WXdVMjc3T0plZGFPT1JkSU1uamZKNmEiLCJtYWMiOiI3NGE4MjgxMTg3Yzc5OWMxNWJlOWUxYzQ3M2Y0MjFmMDdmYThiZjc5YzUyNzI5MTAyOTljODFlYmZkNTk1NjliIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNDaVlrTFlFWDRlSGpPYmdtT1N2Vnc9PSIsInZhbHVlIjoiOHdxeGloTWpBOFhUTFgxM3VVTWdmNG03TE9lWFUrZElBSDYwSlJLVno4WUxyblJkTVpGWUZGMXMrbFF2eC9DS3VYa2VmbEQrK1lFT0E2ZE1uZTloMDRyVHV2MnlEcHROVVd4RE85UWx3VHIxWmtlSzVmNGpnWHZPei82SE9lQjA0RXFDTzJLT1BCZ2twbFZKTFNoY2F6VXRKZWlQNTREdzVTaXdTN0x0cVRjQ1JrQlBLTk9MOE4vUStZTkxZQ2tNQmkwamhuVCsvaFA4aXBSek4vRzF1bGErWEVMdm0yMmluWnZ2dXlZK1VxZ3N4T0UwQTNLRmdtZGZwcFJzUkF6MmtTL242WGdobjVXakpoakZwUTQ0Z2Rlei9jeUZ2K0NuMEpDMnplVDNlenB2ekxTUmMrbTdOaGJXRGIwMit5WCtTQUVpSlBDd2UyaDJKZzh1d09veFZPTFkvYjJIbjVuYkxrOGhPQWhFRUJQdWQ4T3l0a0huV3BrOEZBa280bkN0cmYrWkFSYUFRRExHRHJJUFFjK21JSEo0bEgraUwwbWczcTZjSG56dzRwTmIvclhqV0ZSVEwwRk0weW16VUZwUllZWWI4akZRN3BNaW1qVDBLYTlxWWgrUGFrUVBJN21pT0FEVnJ5Z25KSmUvdVZzYmt0UHN5MW85REdzck5USmUiLCJtYWMiOiI4YTgxZDhjNmQ2YTExOTE3Zjg3YmZkNzViMWVmMzIxYjIzMjdhYmEwNmY4MTRjZjEwZDIwMzJlYTk0M2I1YjcyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:43:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314901906\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EllIJfpOf8kgdVTFMkUO4B9GphXBgGHFQDdspyBx</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">http://localhost:8000/storage/uploads/avatar/ChatGPT%20Image%20Apr%203,%202025,%2011_09_24%20PM_1752838008.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}