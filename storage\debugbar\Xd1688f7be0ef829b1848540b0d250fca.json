{"__meta": {"id": "Xd1688f7be0ef829b1848540b0d250fca", "datetime": "2025-07-31 04:42:58", "utime": **********.862388, "method": "GET", "uri": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753936977.619033, "end": **********.862435, "duration": 1.2434020042419434, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1753936977.619033, "relative_start": 0, "end": **********.772385, "relative_end": **********.772385, "duration": 1.1533517837524414, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.772402, "relative_start": 1.****************, "end": **********.86244, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "90.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IGSGK7GR3zxZ4FM90DZjQOPBCIECBtTkott81hZ4", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/download%20(12)_1751908471.jpeg", "status_code": "<pre class=sf-dump id=sf-dump-1750787860 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1750787860\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-322407343 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-322407343\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1088708194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1088708194\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1782604418 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1782604418\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1070277953 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1070277953\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:42:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlkrOW1MTU9EdU5FR1d2Tm9GY3Vqcmc9PSIsInZhbHVlIjoieUd2eUN0MFBkYXBBYi9FOUQ2aUpFeVYvTFdCZEFGL2lzZFJuTUxZYXJqYWdmL2RtMEJLc0tOZlpXSUZrRjFTZWtOS0dSRmplcGlwelE2cG1NcEhQSFM5UDVFbkpzWkNMTEdYOWxyVklzTFVnZmluV1JFNGhRdWo5MHZjUDFrWmFKUmoyUm5lN3JpWXFCRkpvOG82Z2dCaUE2eWNqcUp6azFnQm5hdUc0dk9GbWJibEpNQWFYM25MZ0FKa3RpbVRvYW15VWVQNmJRRUN2dVNBcGtydXR5Q0xEUTdlZk9QMzM0NlVHTHNBd0FKUFNUdWJMR0h1MUJJNEdxUEpydXYrRlV1Y1JBdE1neWZNWVJqOGQvWDVLRVNRbkIvK2ZVUFVPM2ptajN4OGd4bUd6WEZZdVVzUFY1bUprSDErS2NOSmdtU1BVMnBvelNjM0JwM3JGWno4T282bllpcnQ4ck5wZjFvYTY1N1VseWN4UjJPay9mZ1NHc1BnUG9rU0FaRHo0eDVoa21TTjJPUVBLcUljQVdsOXBFU3hlQVJCZzFHeStRLzY5TjZQL0lyQ0ZkRmU0dEgxTFJDd0FLb0FMMzViblVZenpsckE3WTJWUFo0QjJyWW9WbHBhaXFDckZCdHowWEk0akVBUkd3MjgyWXpjTEpjOS9hemhYTVR1a2lpeU4iLCJtYWMiOiIzYzk4ZDA0MmMwNGVhNGE5OWFlODBiNWUzZDFmM2UzZjZiZDA0ZGQwNmNmNTYzODBiM2ZhMWUxNTRkNDdlZDM2IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:42:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InBPZmRJUWUyTFFlSm9IaHd2cCtVMEE9PSIsInZhbHVlIjoibHFDdWdOSUxlOFJDRkF3czA4OVZGREEzVnQxejV0eHhhRE5POGQwRlZrK1ZiMCt5WjRlbEV0NTQ1UnpMRFhkOEJHS3RkWWd3YkJhZDhIQXlrVEx4QXBkemFpN2FXUUQwK0cyQkJvSDZmKzlWbkllSGE2SVNLdHpxNmJWbkFKUTd2T2lCVGF2U0dHMzRmeHVPeC9Rd1luT3ZGQ29BVGVtci9QbUV5M1hRa0lpTm0wemJWUTEzWkJnc3RwNHVnRitEOW9MZ0lZT0ZXbi9qYkZ6TDAvdk1rcmZFNkxWQUxObjliWjcrT1BsVTZHQUxOSi9JY1BNVElJb01QbGYxMXk3enpDaGZuYlNPUU0ya3lubHgwWnArcWNyNkhqekhiYVN3R044VFV2QUtHTFRuOVNCV3dtaXBpTCthVnl6T3A2REhFUjJPTlA1akdCT2xzUEptT1JkZ3IxZHRJbTg3MnIwcG52OHhxdkVKSm1iOHZuSzR4OUZGZlIrVFdmdlFQMi9DVng5bUVEZVkydnFXS0cwbEdHeTlCbjFSM3Z4cSsrV2E4dHVrNGpQRGk5OW8zQW4yN1VqU0xpamJLQU5RMXZ2dHkzMWptL05aOUw1UXdoa1p6TUJDcG04WThCTWlFa1ZqdGE0eUVCNGFZbE5EYmdvUWxlUEZldUQ1ekpQN2Z4dmIiLCJtYWMiOiI4OGFlOGZkOTczMWNlNWUzMDlhMDYwMmIwMmVmODMxMjI0YmZjOTY4NjFhOTAyNGQ5ZDE4YWM3ODRkZGVhMmQ0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:42:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlkrOW1MTU9EdU5FR1d2Tm9GY3Vqcmc9PSIsInZhbHVlIjoieUd2eUN0MFBkYXBBYi9FOUQ2aUpFeVYvTFdCZEFGL2lzZFJuTUxZYXJqYWdmL2RtMEJLc0tOZlpXSUZrRjFTZWtOS0dSRmplcGlwelE2cG1NcEhQSFM5UDVFbkpzWkNMTEdYOWxyVklzTFVnZmluV1JFNGhRdWo5MHZjUDFrWmFKUmoyUm5lN3JpWXFCRkpvOG82Z2dCaUE2eWNqcUp6azFnQm5hdUc0dk9GbWJibEpNQWFYM25MZ0FKa3RpbVRvYW15VWVQNmJRRUN2dVNBcGtydXR5Q0xEUTdlZk9QMzM0NlVHTHNBd0FKUFNUdWJMR0h1MUJJNEdxUEpydXYrRlV1Y1JBdE1neWZNWVJqOGQvWDVLRVNRbkIvK2ZVUFVPM2ptajN4OGd4bUd6WEZZdVVzUFY1bUprSDErS2NOSmdtU1BVMnBvelNjM0JwM3JGWno4T282bllpcnQ4ck5wZjFvYTY1N1VseWN4UjJPay9mZ1NHc1BnUG9rU0FaRHo0eDVoa21TTjJPUVBLcUljQVdsOXBFU3hlQVJCZzFHeStRLzY5TjZQL0lyQ0ZkRmU0dEgxTFJDd0FLb0FMMzViblVZenpsckE3WTJWUFo0QjJyWW9WbHBhaXFDckZCdHowWEk0akVBUkd3MjgyWXpjTEpjOS9hemhYTVR1a2lpeU4iLCJtYWMiOiIzYzk4ZDA0MmMwNGVhNGE5OWFlODBiNWUzZDFmM2UzZjZiZDA0ZGQwNmNmNTYzODBiM2ZhMWUxNTRkNDdlZDM2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:42:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InBPZmRJUWUyTFFlSm9IaHd2cCtVMEE9PSIsInZhbHVlIjoibHFDdWdOSUxlOFJDRkF3czA4OVZGREEzVnQxejV0eHhhRE5POGQwRlZrK1ZiMCt5WjRlbEV0NTQ1UnpMRFhkOEJHS3RkWWd3YkJhZDhIQXlrVEx4QXBkemFpN2FXUUQwK0cyQkJvSDZmKzlWbkllSGE2SVNLdHpxNmJWbkFKUTd2T2lCVGF2U0dHMzRmeHVPeC9Rd1luT3ZGQ29BVGVtci9QbUV5M1hRa0lpTm0wemJWUTEzWkJnc3RwNHVnRitEOW9MZ0lZT0ZXbi9qYkZ6TDAvdk1rcmZFNkxWQUxObjliWjcrT1BsVTZHQUxOSi9JY1BNVElJb01QbGYxMXk3enpDaGZuYlNPUU0ya3lubHgwWnArcWNyNkhqekhiYVN3R044VFV2QUtHTFRuOVNCV3dtaXBpTCthVnl6T3A2REhFUjJPTlA1akdCT2xzUEptT1JkZ3IxZHRJbTg3MnIwcG52OHhxdkVKSm1iOHZuSzR4OUZGZlIrVFdmdlFQMi9DVng5bUVEZVkydnFXS0cwbEdHeTlCbjFSM3Z4cSsrV2E4dHVrNGpQRGk5OW8zQW4yN1VqU0xpamJLQU5RMXZ2dHkzMWptL05aOUw1UXdoa1p6TUJDcG04WThCTWlFa1ZqdGE0eUVCNGFZbE5EYmdvUWxlUEZldUQ1ekpQN2Z4dmIiLCJtYWMiOiI4OGFlOGZkOTczMWNlNWUzMDlhMDYwMmIwMmVmODMxMjI0YmZjOTY4NjFhOTAyNGQ5ZDE4YWM3ODRkZGVhMmQ0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:42:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1578962830 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IGSGK7GR3zxZ4FM90DZjQOPBCIECBtTkott81hZ4</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"76 characters\">http://localhost:8000/storage/uploads/avatar/download%20(12)_1751908471.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578962830\", {\"maxDepth\":0})</script>\n"}}