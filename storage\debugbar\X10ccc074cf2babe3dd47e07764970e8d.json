{"__meta": {"id": "X10ccc074cf2babe3dd47e07764970e8d", "datetime": "2025-07-31 04:48:45", "utime": **********.990525, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937324.905948, "end": **********.99057, "duration": 1.0846221446990967, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1753937324.905948, "relative_start": 0, "end": **********.776052, "relative_end": **********.776052, "duration": 0.8701040744781494, "duration_str": "870ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.776067, "relative_start": 0.8701190948486328, "end": **********.990573, "relative_end": 2.86102294921875e-06, "duration": 0.21450591087341309, "duration_str": "215ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46614336, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.924357, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.936701, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.969463, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=263\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:263-278</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.040809999999999985, "accumulated_duration_str": "40.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 545}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 267}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8430579, "duration": 0.02657, "duration_str": "26.57ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "radhe_same", "start_percent": 0, "width_percent": 65.107}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'radhe_same' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.885099, "duration": 0.0095, "duration_str": "9.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": "app/Models/Utility.php:527", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "radhe_same", "start_percent": 65.107, "width_percent": 23.279}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 270}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.901161, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": "app/Models/Utility.php:533", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "radhe_same", "start_percent": 88.385, "width_percent": 1.764}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.925313, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 90.149, "width_percent": 2.083}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 7}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.937544, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 92.232, "width_percent": 1.985}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3940}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9546082, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3940", "source": "app/Models/Utility.php:3940", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3940", "ajax": false, "filename": "Utility.php", "line": "3940"}, "connection": "radhe_same", "start_percent": 94.217, "width_percent": 1.985}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3943}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 3988}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 9}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9598181, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3943", "source": "app/Models/Utility.php:3943", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=3943", "ajax": false, "filename": "Utility.php", "line": "3943"}, "connection": "radhe_same", "start_percent": 96.202, "width_percent": 1.544}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\resources\\views/layouts/auth.blade.php", "line": 12}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9650369, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": "app/Models/Utility.php:4359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "radhe_same", "start_percent": 97.746, "width_percent": 2.254}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "W23ZTTruJo2IL0dbKUE5NwVj3qXRmWtrsNQNZxCZ", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1879694660 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1879694660\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1909191950 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1909191950\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-936888197 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-936888197\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-548494707 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548494707\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-842318193 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-842318193\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-845082310 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjAxazh3SWpjRVczbjBseFpzZDJ3eGc9PSIsInZhbHVlIjoiU0hyVHR1R08xeVhIdEZ2bTFSU2wweTlhMFhYTkw0TUVuTmY0Rnkya0hadGFTWUtab2c5cTZnMHRqTUVpcG91bGRWUXpOcHcwQkpPOFRIQ1lHQ0kxeHdSSjhtVjBENnQ0WUpMYUpQMXRoM083NE4zSVRnS2hiNW1qL0RBbnlzRGZOM1pkSTdzclo3VjFpV1RCQnlRVnBBUitiMTV2M1A3N0hNMjN0ZEM4OFNjSWo0a1dCRmpnbTV1bnN0d3piRXozQ2JGdVF3OTNYQngzbm5oMWxTNFRFN01SMHhpRm44NjlQNWxxMXRLR2ZUZUJTYTZaMmxsbkhvc2xybEE5Rlp4R0lTSFVLRHVTcDNJY0d2bmxTc3BIc0l5SGFtWjJmczk1VjM5ZnRqT1FreVVjMmk2Snpsd2JlcVdWd2VFYmdxMXpna0RraGl4MXVvazcyRE5XSHhONWlMQ2I5bnhxNWRtTEhGR3BMeUQ5aU5aMnhndnNWQThCWVYxVjB0SkRTZ1lZVEdOZmdEVVdUalc0bWFHK3VKUTBWWXIvMXJPYjhJZnRkaDBYM1kvcHA1aHNOd2dwbk1HVDBTY2trOHlhNnZYdU90MGdKNDFQemh2Z0d3M1haNFd3eEJNRlp5NG5LMHk3RkNySkEycmJ2c1M3UTh6MGpoUytWc1Y1TzhIck9aTzAiLCJtYWMiOiIwMzAxYmYyN2UxZTEzMTU0M2VlODM1OTZjMzgyY2VlM2E4NjU5NmNhMzI0MmQ2ZDEyZGI5ZGNjZGU4OGFkZmFjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IngrM0RENm9hbVR2Qk85elljalNzdVE9PSIsInZhbHVlIjoiMlJ2YkNSZU9HcVdhM2UrSkZwbEowL1pKRDVkUW9tYjJOSFR0aFlzZVpCSC8wem9wSUQvb2pjZEVvZlJaQ0I2T2czbE1CdWtPQjVnR2VzeldmQVdNMSt5VVRVQWh2cGVndWNLWXR1WUt5bjhXSlhnUHNQaDB6S2tzK1RTQ1dDaUVqekZ1akt5Zy9JQ3dGTnhubnhMMjF6TTNnbE5FOWNIcGpNNlJjbXJ3U0lpejJzQVl4NU91YW02d1MwVzJCcVcxVTlwTVYwajFrV2JIRHFDRWNQU3BER242dmNXdWlQV3JmRVRObEpvcDNVTFlWdWFCbVVIdWszcmtZYnZIRVFvZkpEVGc2U0xpM0lwZlc2c1RUV0dUR1Juc0lOR1pvb1k1RG9iVWNWcWRxSUZrZzFSSThJdW83ZVJMWC9HR2IzcDBzMkdTclNESXAyMG14VDg0VHE3eWg2ZXR1ZnMwUS93N3BMd3NMUFo4UTNTWXh6eFNwQkt5eVYvcTJsYXJaYVZBdE5JQlllb0lmWmFSdVljMFRlbFRpYlU0endxLzNtTnN6ZFRTZ2t6aVFDckFJVm1BWmJET29qelFXSFhYMHFjYlB3TWU2TFpIaXl5Mm9iTDhyM2VNcWViSVY2SXR2SnFaQzczTTYyTnMxTUtQd2FhK3h4WVRHRFl2eWlKU2YySkYiLCJtYWMiOiI0Zjc0MmQzZjYyMjQxMzQwZDZhZDY4MWIzZWNhYzk2YWVmZmIyMzExODFlMDg4MjZjMjNiOWQyMmU5MjhlNTQ0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjAxazh3SWpjRVczbjBseFpzZDJ3eGc9PSIsInZhbHVlIjoiU0hyVHR1R08xeVhIdEZ2bTFSU2wweTlhMFhYTkw0TUVuTmY0Rnkya0hadGFTWUtab2c5cTZnMHRqTUVpcG91bGRWUXpOcHcwQkpPOFRIQ1lHQ0kxeHdSSjhtVjBENnQ0WUpMYUpQMXRoM083NE4zSVRnS2hiNW1qL0RBbnlzRGZOM1pkSTdzclo3VjFpV1RCQnlRVnBBUitiMTV2M1A3N0hNMjN0ZEM4OFNjSWo0a1dCRmpnbTV1bnN0d3piRXozQ2JGdVF3OTNYQngzbm5oMWxTNFRFN01SMHhpRm44NjlQNWxxMXRLR2ZUZUJTYTZaMmxsbkhvc2xybEE5Rlp4R0lTSFVLRHVTcDNJY0d2bmxTc3BIc0l5SGFtWjJmczk1VjM5ZnRqT1FreVVjMmk2Snpsd2JlcVdWd2VFYmdxMXpna0RraGl4MXVvazcyRE5XSHhONWlMQ2I5bnhxNWRtTEhGR3BMeUQ5aU5aMnhndnNWQThCWVYxVjB0SkRTZ1lZVEdOZmdEVVdUalc0bWFHK3VKUTBWWXIvMXJPYjhJZnRkaDBYM1kvcHA1aHNOd2dwbk1HVDBTY2trOHlhNnZYdU90MGdKNDFQemh2Z0d3M1haNFd3eEJNRlp5NG5LMHk3RkNySkEycmJ2c1M3UTh6MGpoUytWc1Y1TzhIck9aTzAiLCJtYWMiOiIwMzAxYmYyN2UxZTEzMTU0M2VlODM1OTZjMzgyY2VlM2E4NjU5NmNhMzI0MmQ2ZDEyZGI5ZGNjZGU4OGFkZmFjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IngrM0RENm9hbVR2Qk85elljalNzdVE9PSIsInZhbHVlIjoiMlJ2YkNSZU9HcVdhM2UrSkZwbEowL1pKRDVkUW9tYjJOSFR0aFlzZVpCSC8wem9wSUQvb2pjZEVvZlJaQ0I2T2czbE1CdWtPQjVnR2VzeldmQVdNMSt5VVRVQWh2cGVndWNLWXR1WUt5bjhXSlhnUHNQaDB6S2tzK1RTQ1dDaUVqekZ1akt5Zy9JQ3dGTnhubnhMMjF6TTNnbE5FOWNIcGpNNlJjbXJ3U0lpejJzQVl4NU91YW02d1MwVzJCcVcxVTlwTVYwajFrV2JIRHFDRWNQU3BER242dmNXdWlQV3JmRVRObEpvcDNVTFlWdWFCbVVIdWszcmtZYnZIRVFvZkpEVGc2U0xpM0lwZlc2c1RUV0dUR1Juc0lOR1pvb1k1RG9iVWNWcWRxSUZrZzFSSThJdW83ZVJMWC9HR2IzcDBzMkdTclNESXAyMG14VDg0VHE3eWg2ZXR1ZnMwUS93N3BMd3NMUFo4UTNTWXh6eFNwQkt5eVYvcTJsYXJaYVZBdE5JQlllb0lmWmFSdVljMFRlbFRpYlU0endxLzNtTnN6ZFRTZ2t6aVFDckFJVm1BWmJET29qelFXSFhYMHFjYlB3TWU2TFpIaXl5Mm9iTDhyM2VNcWViSVY2SXR2SnFaQzczTTYyTnMxTUtQd2FhK3h4WVRHRFl2eWlKU2YySkYiLCJtYWMiOiI0Zjc0MmQzZjYyMjQxMzQwZDZhZDY4MWIzZWNhYzk2YWVmZmIyMzExODFlMDg4MjZjMjNiOWQyMmU5MjhlNTQ0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-845082310\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1001337062 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W23ZTTruJo2IL0dbKUE5NwVj3qXRmWtrsNQNZxCZ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1001337062\", {\"maxDepth\":0})</script>\n"}}