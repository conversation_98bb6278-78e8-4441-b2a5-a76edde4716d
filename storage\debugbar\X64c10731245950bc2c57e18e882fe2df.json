{"__meta": {"id": "X64c10731245950bc2c57e18e882fe2df", "datetime": "2025-07-31 04:42:57", "utime": **********.592101, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753936975.687582, "end": **********.592129, "duration": 1.9045469760894775, "duration_str": "1.9s", "measures": [{"label": "Booting", "start": 1753936975.687582, "relative_start": 0, "end": **********.482091, "relative_end": **********.482091, "duration": 1.794508934020996, "duration_str": "1.79s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.482111, "relative_start": 1.****************, "end": **********.592132, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WWw6nIGfyLKHHkcE0X1E5G5nLnLFkw166XMmgFex", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png", "status_code": "<pre class=sf-dump id=sf-dump-737645367 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-737645367\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-610071872 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-610071872\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-60613789 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-60613789\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-313311383 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-313311383\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1916998786 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1916998786\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-298754929 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:42:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdEdjRVY05KVVlPTGk4R2wzeno3M2c9PSIsInZhbHVlIjoib3Q1NFhLR1JxRFVDRWIyQkI4SkFIQkwyMkhkQ29oci9Ld3VHQ2F3aGZ0Q3p4TnpWR0l5OXd4a2gwdEs5ZzQxUzZjL3hvMFRpYk1mZk5DbTlZcWdPZmE0UHZzcmFGSFZpd0xvc2E5cC9RZVNNV3pnU01ITUptR1FVNlE0SVpDSXdoTlFuKy9OdHZJWWxDeitRdEltRFg0ZVIza2JTWmhCa25hWEZoL2E0d0NwV2Rkdm5SeG1uNUUxYzloN0padU4zVmFVVDlWKzF6M1JmQUc0dHVtWDVxZFpWNkZENDVmOWpqMXM2d1Z3elNOUE1QNnhTUDBveTBZbUd6VTJxR3RmeVBvQjBQODB6UzQ3N1dwQ2ttb0MrSW1lRVBYUXhPdS84S3RNZ1BJRmR2QlFFSDVhSDZUNXpwd1Jsb011TGJxMVhQYmx3TzlzWU1aL3hsUmFXbXBpdmFiaUw1aTJEVEYwNVVxc2lyWU02WExEcVdEKzhFa1hRSUY1WDV6QWthclNBSmxRRDRsMGc3bWdIQ0xTNXBER3NsOUFHZUVQdjBORFpZSDhTWGh1ZW4xMlBqSWt5bDJmOUV2eTVGSjBlMmFpa2xqVzljZEJXSlFZS3RDcXVwVHhCV3RCdGpaSDhJcUFQMXNNM0FhU3VHay9XM3FyK2o3UFNaUGo1YTlOV0NXVXQiLCJtYWMiOiIxMmM5YWM0NTJiMzEwZGRlNDFlMDY3OTdmMjBhNmRlMjk5MTc2MGIwZjRjZWU2MTliZDM5N2MxNDM0MDhkNTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:42:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImF1a3lVUHYwRTFmZU9ibUtkNjlpbmc9PSIsInZhbHVlIjoiYUVNRW5tSUlvTWVmSWI1aGVBcUc5T2xZZ0xqcGk3b0JHV3VuZXh0UVJKbGtBMTA1b01RUzMwSlYzc25jQ1hONnEyckpTdFZhbkRoQkE3R0lCVU0yNXZsdE1tdDhXT1dOMkhhYkUrYXRlaTRWdjRXRW1XWXpvVEEvZVkwZElOY3BHRTFPcTROV0tKTENXTlFRWkRXN2hJMDNvQWRzaVJpR1k2alRpeSs4Y3pIeDZmWnFxbGtnL25SOWlHZUFjVllJb1NEQ2licGMrQWliblBjVzIwOU4zZzFIOG5JVXFEVmMwcmdrdlBYdXRkQUt0Q3lLRHlyVTBMMXhSNW04NnZEUjlGV1FwQms4VlgrNXBtQi83WVBVVmMzYWVIaXptNzdyUnNLREpHM3Q3a0ZlTWpPV01BRHE2UWJjOWFKdXJ6a0VXVGhPTFFhaU1TQjZaSFZIWEVVMTdSYkUwRVdzMzQ1cW9FY1hOTGdKb2xDRmxSZkpaVnJYRGFZYkNPN3BDWUdxa1BTZEl4WWExdmJkUmdyazd2QmVLb3RlOFpQcDZaR2JESlRtVGp4TVY2ejg4MFoxWTk5blZDcTJUK1FiejY4dUF4VGtHTm51eFBWWXlwK2J6UksxVjdycEFzQThqTzMxZVRsS3F1Mm5sYk1PeW0yNWl1WnovUTg5dkQ4TllrNW0iLCJtYWMiOiIwM2JlOTU5NTM5NGEyNTcyOTE1MmIwOTU1ZjNjYTc0YzA0MmM0YjM2NzlhNDk5OGNhZDAxMzgwZjRkOGExOGJjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:42:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdEdjRVY05KVVlPTGk4R2wzeno3M2c9PSIsInZhbHVlIjoib3Q1NFhLR1JxRFVDRWIyQkI4SkFIQkwyMkhkQ29oci9Ld3VHQ2F3aGZ0Q3p4TnpWR0l5OXd4a2gwdEs5ZzQxUzZjL3hvMFRpYk1mZk5DbTlZcWdPZmE0UHZzcmFGSFZpd0xvc2E5cC9RZVNNV3pnU01ITUptR1FVNlE0SVpDSXdoTlFuKy9OdHZJWWxDeitRdEltRFg0ZVIza2JTWmhCa25hWEZoL2E0d0NwV2Rkdm5SeG1uNUUxYzloN0padU4zVmFVVDlWKzF6M1JmQUc0dHVtWDVxZFpWNkZENDVmOWpqMXM2d1Z3elNOUE1QNnhTUDBveTBZbUd6VTJxR3RmeVBvQjBQODB6UzQ3N1dwQ2ttb0MrSW1lRVBYUXhPdS84S3RNZ1BJRmR2QlFFSDVhSDZUNXpwd1Jsb011TGJxMVhQYmx3TzlzWU1aL3hsUmFXbXBpdmFiaUw1aTJEVEYwNVVxc2lyWU02WExEcVdEKzhFa1hRSUY1WDV6QWthclNBSmxRRDRsMGc3bWdIQ0xTNXBER3NsOUFHZUVQdjBORFpZSDhTWGh1ZW4xMlBqSWt5bDJmOUV2eTVGSjBlMmFpa2xqVzljZEJXSlFZS3RDcXVwVHhCV3RCdGpaSDhJcUFQMXNNM0FhU3VHay9XM3FyK2o3UFNaUGo1YTlOV0NXVXQiLCJtYWMiOiIxMmM5YWM0NTJiMzEwZGRlNDFlMDY3OTdmMjBhNmRlMjk5MTc2MGIwZjRjZWU2MTliZDM5N2MxNDM0MDhkNTZhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:42:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImF1a3lVUHYwRTFmZU9ibUtkNjlpbmc9PSIsInZhbHVlIjoiYUVNRW5tSUlvTWVmSWI1aGVBcUc5T2xZZ0xqcGk3b0JHV3VuZXh0UVJKbGtBMTA1b01RUzMwSlYzc25jQ1hONnEyckpTdFZhbkRoQkE3R0lCVU0yNXZsdE1tdDhXT1dOMkhhYkUrYXRlaTRWdjRXRW1XWXpvVEEvZVkwZElOY3BHRTFPcTROV0tKTENXTlFRWkRXN2hJMDNvQWRzaVJpR1k2alRpeSs4Y3pIeDZmWnFxbGtnL25SOWlHZUFjVllJb1NEQ2licGMrQWliblBjVzIwOU4zZzFIOG5JVXFEVmMwcmdrdlBYdXRkQUt0Q3lLRHlyVTBMMXhSNW04NnZEUjlGV1FwQms4VlgrNXBtQi83WVBVVmMzYWVIaXptNzdyUnNLREpHM3Q3a0ZlTWpPV01BRHE2UWJjOWFKdXJ6a0VXVGhPTFFhaU1TQjZaSFZIWEVVMTdSYkUwRVdzMzQ1cW9FY1hOTGdKb2xDRmxSZkpaVnJYRGFZYkNPN3BDWUdxa1BTZEl4WWExdmJkUmdyazd2QmVLb3RlOFpQcDZaR2JESlRtVGp4TVY2ejg4MFoxWTk5blZDcTJUK1FiejY4dUF4VGtHTm51eFBWWXlwK2J6UksxVjdycEFzQThqTzMxZVRsS3F1Mm5sYk1PeW0yNWl1WnovUTg5dkQ4TllrNW0iLCJtYWMiOiIwM2JlOTU5NTM5NGEyNTcyOTE1MmIwOTU1ZjNjYTc0YzA0MmM0YjM2NzlhNDk5OGNhZDAxMzgwZjRkOGExOGJjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:42:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298754929\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1815816682 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WWw6nIGfyLKHHkcE0X1E5G5nLnLFkw166XMmgFex</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1753074802.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1815816682\", {\"maxDepth\":0})</script>\n"}}