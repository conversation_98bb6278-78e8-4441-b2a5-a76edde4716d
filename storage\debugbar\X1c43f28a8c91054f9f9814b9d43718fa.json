{"__meta": {"id": "X1c43f28a8c91054f9f9814b9d43718fa", "datetime": "2025-07-31 04:54:05", "utime": **********.583632, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937644.594147, "end": **********.583659, "duration": 0.9895119667053223, "duration_str": "990ms", "measures": [{"label": "Booting", "start": 1753937644.594147, "relative_start": 0, "end": **********.471043, "relative_end": **********.471043, "duration": 0.8768961429595947, "duration_str": "877ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.471079, "relative_start": 0.****************, "end": **********.583661, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pI60fKIZYMBaCxuK1ZHISt8YfUzS3X1xp4gtrmgK", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-528930819 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-528930819\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-963155129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-963155129\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1375500660 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1375500660\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1758484670 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1758484670\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1651987863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1651987863\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1020683295 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:54:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRqSjBPSHlKVG90SlJNZVEybEQ5YXc9PSIsInZhbHVlIjoiQ2RjMkN0NHZQclNrK1YxUFh1WXEvU2xaMEx3dWVCekYxcjZCMVVuaHk2LzNHa2RUWnAwZ3FUM1RXRWptdDZ1bzhqTkliaXpBU0xrbTVYSGlxZW4vd0ZmMThGSEVDK09YbDU0bm55RkE3VU9lNEJvM2tLNVBmTFprNVNLUU5kWlNRNmxmQ1JFUVFWUENlNEVCUVZQN3VRSVJqNmJlVjF4bTBlV0pxOFNWTjFHT2hMV3FUTnhhTHRKQVpuWlJDc0UwU2FiSFhndEg4WXFIVHJIR1d0ZWVGUkFTUWR1bDBPS0tkZXpUT0d0d2F1NmZCd01qM2F6aXliUGZON250WmtrV3NxbUR0RUpFY3liTU1Dd1YvNTNzdTh2TXdJajAvQmVuOUJETDNSa2N6dld6bDJkYjVvTStiQkVldFZTSUYxUENyYU9uZGtwcEdkWnJQMkNya1hYSHBjY0t2S1p2RVlicTJxcWw1M0F1cHg3dXNFWSt2UktVc3lBeTJDV3NwN0FEcGlVSWxMOGlJNFlyY1RyeGloTk1xcVBKTXBjeU1ySlVyMlpNeWNCM281RXV3TTd2ZXlNaVVLZkZiajdQWlBhNEsyUDZuWjl4VkZvYldSS3lKejR2OEtaNVBxekVKVm5ISW1GM0RkVFRTenZIQk1oamliS3ZlUXFkeEU3LzVRZXUiLCJtYWMiOiIxMTlmMjdkNmFmMWU5NzI1MzJjN2E1ZWIyNDI3MWUyNzgwNGFmZWNiZGI4MzM5N2UzZmQwMjgyMTU2ZDkzMmZlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik1lOEtXRmJyMmR6S2ZvMkorMjJ1TGc9PSIsInZhbHVlIjoiQXRnUGJsVFhKMWVBN2x0TkdaakJjQW9VaWl2N01jWXl3RkNzd2huekFTblpVTVdMazZST3NRaVRCZElzUmRra0h0YjRQRk1GYnpsTkJRbGQ0QjhWSE1hQmM0Wkw2cnhjKzVKTFU0U2FneXJ2UjZBY1JCYXlSTVg2OWlCQ2tEQi9Lem42TnY3WkRyQkxwSS9RckU2Ny83cjJKVldpYmxRZGNXTDRpc2xicWMxSGJ3R1dJSkMrUUNlQzgzakJMaWtPS08yc0kxbkhKY2JKZWFUMDRPVURvcFpzNzFMdTdiVUtGa3V3eWpSMEZrQ3A2b2N2MHBvaEJiQmhEaEdzN2F2ZHJ6T2IzZXpuWE1yelpOWWpwbXhYQnFuRjliZHN0cFhybkhhSy9iaE5VNWpuRVJNMCtycm9mM0VieU9ZL3kwdGp5eExuN0Q1N1gyOUhVcXhWQzk2VmVQaFFOay9kcFpQbUppY3NtdzJCQVl4SmxWenVobGpiaC9kcndTelZuNUtUaE5XNzVHeFNwVWFrczczUEdXU3VodCtXa04vZngvdE53eUdrL3RTQWhpWEI0bmxWWDZlSnFFR0drYy9HaituYXhmWTJLSmhwd3JVWk0zYlJDaGN3bjRTblJnTkNXZFphamVBUTJhZWNJWGEybFF6NC9RL2F6U29TeWw5UTJidlEiLCJtYWMiOiI2OTIwYTFlNzc3ODc0ZTUwNGQxMWE3OGViYWRkNWQ2YmY5NzEwNTk2N2NiYTQ4ZWQxOTZkNGY3NTJhMjIzNWI4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRqSjBPSHlKVG90SlJNZVEybEQ5YXc9PSIsInZhbHVlIjoiQ2RjMkN0NHZQclNrK1YxUFh1WXEvU2xaMEx3dWVCekYxcjZCMVVuaHk2LzNHa2RUWnAwZ3FUM1RXRWptdDZ1bzhqTkliaXpBU0xrbTVYSGlxZW4vd0ZmMThGSEVDK09YbDU0bm55RkE3VU9lNEJvM2tLNVBmTFprNVNLUU5kWlNRNmxmQ1JFUVFWUENlNEVCUVZQN3VRSVJqNmJlVjF4bTBlV0pxOFNWTjFHT2hMV3FUTnhhTHRKQVpuWlJDc0UwU2FiSFhndEg4WXFIVHJIR1d0ZWVGUkFTUWR1bDBPS0tkZXpUT0d0d2F1NmZCd01qM2F6aXliUGZON250WmtrV3NxbUR0RUpFY3liTU1Dd1YvNTNzdTh2TXdJajAvQmVuOUJETDNSa2N6dld6bDJkYjVvTStiQkVldFZTSUYxUENyYU9uZGtwcEdkWnJQMkNya1hYSHBjY0t2S1p2RVlicTJxcWw1M0F1cHg3dXNFWSt2UktVc3lBeTJDV3NwN0FEcGlVSWxMOGlJNFlyY1RyeGloTk1xcVBKTXBjeU1ySlVyMlpNeWNCM281RXV3TTd2ZXlNaVVLZkZiajdQWlBhNEsyUDZuWjl4VkZvYldSS3lKejR2OEtaNVBxekVKVm5ISW1GM0RkVFRTenZIQk1oamliS3ZlUXFkeEU3LzVRZXUiLCJtYWMiOiIxMTlmMjdkNmFmMWU5NzI1MzJjN2E1ZWIyNDI3MWUyNzgwNGFmZWNiZGI4MzM5N2UzZmQwMjgyMTU2ZDkzMmZlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik1lOEtXRmJyMmR6S2ZvMkorMjJ1TGc9PSIsInZhbHVlIjoiQXRnUGJsVFhKMWVBN2x0TkdaakJjQW9VaWl2N01jWXl3RkNzd2huekFTblpVTVdMazZST3NRaVRCZElzUmRra0h0YjRQRk1GYnpsTkJRbGQ0QjhWSE1hQmM0Wkw2cnhjKzVKTFU0U2FneXJ2UjZBY1JCYXlSTVg2OWlCQ2tEQi9Lem42TnY3WkRyQkxwSS9RckU2Ny83cjJKVldpYmxRZGNXTDRpc2xicWMxSGJ3R1dJSkMrUUNlQzgzakJMaWtPS08yc0kxbkhKY2JKZWFUMDRPVURvcFpzNzFMdTdiVUtGa3V3eWpSMEZrQ3A2b2N2MHBvaEJiQmhEaEdzN2F2ZHJ6T2IzZXpuWE1yelpOWWpwbXhYQnFuRjliZHN0cFhybkhhSy9iaE5VNWpuRVJNMCtycm9mM0VieU9ZL3kwdGp5eExuN0Q1N1gyOUhVcXhWQzk2VmVQaFFOay9kcFpQbUppY3NtdzJCQVl4SmxWenVobGpiaC9kcndTelZuNUtUaE5XNzVHeFNwVWFrczczUEdXU3VodCtXa04vZngvdE53eUdrL3RTQWhpWEI0bmxWWDZlSnFFR0drYy9HaituYXhmWTJLSmhwd3JVWk0zYlJDaGN3bjRTblJnTkNXZFphamVBUTJhZWNJWGEybFF6NC9RL2F6U29TeWw5UTJidlEiLCJtYWMiOiI2OTIwYTFlNzc3ODc0ZTUwNGQxMWE3OGViYWRkNWQ2YmY5NzEwNTk2N2NiYTQ4ZWQxOTZkNGY3NTJhMjIzNWI4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1020683295\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2093701001 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pI60fKIZYMBaCxuK1ZHISt8YfUzS3X1xp4gtrmgK</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2093701001\", {\"maxDepth\":0})</script>\n"}}