@extends('layouts.admin')
@section('page-title')
    {{ $lead->name }}
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/summernote/summernote-bs4.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/plugins/dropzone.min.css') }}">
    <style>
        .tab-btn {
            border: 1px solid #0f766e;
            background: #fff;
            color: #0f766e !important;
            padding: 10px 28px;
            border-radius: 14px;
            font-weight: 500;
            font-size: 1rem;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s, border 0.2s;
            box-shadow: 0 2px 6px rgba(0,0,0,0.04);
            outline: none;
        }
        .tab-btn.active, .tab-btn:focus {
            background: none;
            color: #fff !important;
            border: 1px solid transparent;
            box-shadow: 0 4px 12px rgba(6,95,70,0.10);
        }
        .tab-btn:not(.active):hover {
            background: none;
            color: #fff !important;
            border-color: #43b37a;
        }
        .tab-section { display: block; }
        .tab-section.d-none { display: none !important; }
        @media (max-width: 767px) {
            .tab-btn { width: 100%; margin-bottom: 8px; }
            .tab-menu-responsive { flex-direction: column !important; align-items: stretch !important; }
        }
    </style>
    <style>
        .comments-youtube-ui { background: #fff; border-radius: 12px; }
        .comments-youtube-ui .form-control, .comments-youtube-ui textarea { background: #fff; color: #222; border: 1px solid #e0e0e0; }
        .comments-youtube-ui .form-control:focus, .comments-youtube-ui textarea:focus { background: #f8f9fa; color: #222; border-color: #0f766e; }
        .comments-youtube-ui .btn { border-radius: 20px; }
        .comments-youtube-ui .comment-item, .comments-youtube-ui .reply-item { transition: background 0.2s; }
        .comments-youtube-ui .comment-item:hover { background: #f8f9fa; }
        .comments-youtube-ui .reply-item { background: #f5f5f5; border-radius: 8px; }
        @media (max-width: 767px) {
            .comments-youtube-ui { padding: 1rem !important; }
            .comments-youtube-ui .theme-avtar { width: 32px !important; height: 32px !important; font-size: 1rem !important; }
        }
    </style>
@endpush
@push('script-page')
    <script src="{{ asset('css/summernote/summernote-bs4.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/dropzone-amd-module.min.js') }}"></script>
    <script>
        var scrollSpy = new bootstrap.ScrollSpy(document.body, {
            target: '#lead-sidenav',
            offset: 300
        })
        Dropzone.autoDiscover = false;
        myDropzone = new Dropzone("#dropzonewidget", {
            maxFiles: 20,
            maxFilesize: 20, // 20MB max
            parallelUploads: 1,
            filename: false,
            acceptedFiles: ".jpeg,.jpg,.png,.pdf,.doc,.docx,.txt,.xlsx,.xls,.ppt,.pptx,.zip,.rar",
            url: "{{ route('leads.file.upload', $lead->id) }}",
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            init: function() {
                this.on("addedfile", function(file) {
                    // Add loading state
                    $(file.previewElement).addClass('uploading');
                });

                this.on("uploadprogress", function(file, progress) {
                    // Update progress
                    $(file.previewElement).find('.dz-upload').css('width', progress + '%');
                });
            },
            success: function(file, response) {
                $(file.previewElement).removeClass('uploading');

                if (response.is_success) {
                    if (response.status == 1) {
                        show_toastr('success', response.success_msg, 'success');
                    } else {
                        show_toastr('success', 'File uploaded successfully', 'success');
                    }
                    dropzoneBtn(file, response);

                    // Add uploaded file to the table instantly
                    if (response.uploaded_file) {
                        addUploadedFileRow(response.uploaded_file);
                    }
                } else {
                    myDropzone.removeFile(file);
                    show_toastr('error', response.error || 'Upload failed', 'error');
                }
            },
            error: function(file, response) {
                $(file.previewElement).removeClass('uploading');
                myDropzone.removeFile(file);

                let errorMessage = 'Upload failed';

                if (typeof response === 'string') {
                    try {
                        response = JSON.parse(response);
                    } catch (e) {
                        errorMessage = response;
                    }
                }

                if (response && response.error) {
                    errorMessage = response.error;
                } else if (response && response.message) {
                    errorMessage = response.message;
                } else if (file.status === 'error' && file.xhr) {
                    if (file.xhr.status === 413) {
                        errorMessage = 'File too large. Maximum size is 20MB.';
                    } else if (file.xhr.status === 422) {
                        errorMessage = 'Invalid file type or validation error.';
                    } else if (file.xhr.status === 500) {
                        errorMessage = 'Server error. Please try again.';
                    }
                }

                show_toastr('error', errorMessage, 'error');
            },
            maxfilesexceeded: function(file) {
                show_toastr('error', 'Maximum number of files exceeded', 'error');
                this.removeFile(file);
            }
        });
        myDropzone.on("sending", function(file, xhr, formData) {
            formData.append("_token", $('meta[name="csrf-token"]').attr('content'));
            formData.append("lead_id", {{ $lead->id }});

            // Add file validation on client side
            if (file.size > 20 * 1024 * 1024) { // 20MB
                show_toastr('error', 'File size exceeds 20MB limit', 'error');
                myDropzone.removeFile(file);
                return false;
            }
        });

        function dropzoneBtn(file, response) {
            var download = document.createElement('a');
            download.setAttribute('href', response.download);
            download.setAttribute('class', "badge bg-info mx-1");
            download.setAttribute('data-toggle', "tooltip");
            download.setAttribute('data-original-title', "{{ __('Download') }}");
            download.innerHTML = "<i class='ti ti-download'></i>";

            var del = document.createElement('a');
            del.setAttribute('href', response.delete);
            del.setAttribute('class', "badge bg-danger mx-1");
            del.setAttribute('data-toggle', "tooltip");
            del.setAttribute('data-original-title', "{{ __('Delete') }}");
            del.innerHTML = "<i class='ti ti-trash'></i>";

            del.addEventListener("click", function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (confirm("Are you sure ?")) {
                    var btn = $(this);
                    $.ajax({
                        url: btn.attr('href'),
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        type: 'DELETE',
                        success: function(response) {
                            if (response.is_success) {
                                btn.closest('.dz-image-preview').remove();
                            } else {
                                show_toastr('error', response.error, 'error');
                            }
                        },
                        error: function(response) {
                            response = response.responseJSON;
                            if (response.is_success) {
                                show_toastr('error', response.error, 'error');
                            } else {
                                show_toastr('error', response, 'error');
                            }
                        }
                    })
                }
            });

            var html = document.createElement('div');
            html.appendChild(download);
            @if (Auth::user()->type != 'client')
                @can('edit lead')
                    html.appendChild(del);
                @endcan
            @endif

            file.previewTemplate.appendChild(html);
        }

        // Add this helper function after Dropzone config in the same script block
        function addUploadedFileRow(file) {
            // file: {id, file_name, file_path, created_at, download_url, delete_url, ext, size, formatted_size}
            let icon = 'ti-file';
            if(['jpg','jpeg','png','gif'].includes((file.ext||'').toLowerCase())) icon = 'ti ti-photo';
            else if(['pdf'].includes((file.ext||'').toLowerCase())) icon = 'ti ti-file-text';
            else if(['doc','docx'].includes((file.ext||'').toLowerCase())) icon = 'ti ti-file-description';
            else if(['txt'].includes((file.ext||'').toLowerCase())) icon = 'ti ti-file';

            let sizeInfo = file.formatted_size ? `<br><small class="text-muted">${file.formatted_size}</small>` : '';
            let uploaderInfo = `<br><small class="text-muted">by {{ Auth::user()->name }}</small>`;

            let row = `<tr>
                <td><i class="ti ${icon} fs-4 text-primary"></i></td>
                <td class="text-break">${file.file_name}${sizeInfo}</td>
                <td><small class="text-muted">just now</small>${uploaderInfo}</td>
                <td>
                    <a href="${file.download_url}" class="btn btn-sm btn-outline-info me-1" title="Download">
                        <i class="ti ti-download"></i>
                    </a>
                    ${(file.can_delete ? `<button type='button' class='btn btn-sm btn-outline-danger delete-file-btn' data-file-id='${file.id}' data-delete-url='${file.delete_url}' title='Delete'><i class='ti ti-trash'></i></button>` : '')}
                </td>
            </tr>`;
            $('#uploaded-files-table tbody').prepend(row);
        }

        // Handle delete button clicks for all files (both existing and newly uploaded)
        $(document).on('click', '.delete-file-btn', function(e) {
            e.preventDefault();

            // Store delete information for the modal
            const deleteUrl = $(this).data('delete-url');
            const fileName = $(this).closest('tr').find('td:nth-child(2)').text().trim().split('\n')[0];
            const row = $(this).closest('tr');

            // Set modal content
            $('#deleteFileName').text(fileName);
            $('#confirmDeleteBtn').data('delete-url', deleteUrl);
            $('#confirmDeleteBtn').data('target-row', row);

            // Show the delete modal
            $('#deleteFileModal').modal('show');
        });

        // Handle confirm delete button in modal
        $('#confirmDeleteBtn').on('click', function() {
            const deleteUrl = $(this).data('delete-url');
            const row = $(this).data('target-row');
            const modal = $('#deleteFileModal');

            // Add loading state to modal button
            $(this).prop('disabled', true).html('<i class="ti ti-loader me-1"></i>{{ __("Deleting...") }}');

            $.ajax({
                url: deleteUrl,
                type: 'POST',
                data: {
                    _method: 'DELETE',
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.is_success) {
                        // Hide modal first
                        modal.modal('hide');

                        // Remove row with animation
                        row.fadeOut(300, function() {
                            $(this).remove();
                        });

                        show_toastr('success', response.message || 'File deleted successfully', 'success');
                    } else {
                        show_toastr('error', response.error || 'Failed to delete file', 'error');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Failed to delete file';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    show_toastr('error', errorMessage, 'error');
                },
                complete: function() {
                    // Reset modal button state
                    $('#confirmDeleteBtn').prop('disabled', false).html('<i class="ti ti-trash me-1"></i>{{ __("Delete") }}');
                }
            });
        });

        // Reset modal data when hidden
        $('#deleteFileModal').on('hidden.bs.modal', function() {
            $('#confirmDeleteBtn').removeData('delete-url').removeData('target-row');
            $('#deleteFileName').text('');
        });

        @foreach ($lead->files as $file)
            @if (\Illuminate\Support\Facades\Storage::disk('public')->exists($file->file_path))
                // Create the mock file:
                var mockFile = {
                    name: "{{ $file->original_name ?? $file->file_name }}",
                    size: {{ $file->file_size ?? 0 }}
                };
                // Call the default addedfile event handler
                myDropzone.emit("addedfile", mockFile);
                // And optionally show the thumbnail of the file:
                myDropzone.emit("thumbnail", mockFile, "{{ \Illuminate\Support\Facades\Storage::disk('public')->url($file->file_path) }}");
                myDropzone.emit("complete", mockFile);

                dropzoneBtn(mockFile, {
                    download: "{{ route('leads.file.download', [$lead->id, $file->id]) }}",
                    delete: "{{ route('leads.file.delete', [$lead->id, $file->id]) }}"
                });
            @endif
        @endforeach

        @can('edit lead')
            $('.summernote-simple').on('summernote.blur', function() {

                $.ajax({
                    url: "{{ route('leads.note.store', $lead->id) }}",
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        notes: $(this).val()
                    },
                    type: 'POST',
                    success: function(response) {
                        if (response.is_success) {
                            // show_toastr('Success', response.success,'success');
                        } else {
                            show_toastr('error', response.error, 'error');
                        }
                    },
                    error: function(response) {
                        response = response.responseJSON;
                        if (response.is_success) {
                            show_toastr('error', response.error, 'error');
                        } else {
                            show_toastr('error', response, 'error');
                        }
                    }
                })
            });
        @else
            $('.summernote-simple').summernote('disable');
        @endcan

        @can('edit lead task')
            $(document).on("click", ".task-checkbox", function() {
                var chbox = $(this);
                var lbl = chbox.parent().parent().find('label');

                $.ajax({
                    url: chbox.attr('data-url'),
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        status: chbox.val()
                    },
                    type: 'PUT',
                    success: function(response) {
                        if (response.is_success) {
                            chbox.val(response.status);
                            if (response.status) {
                                lbl.addClass('strike');
                                lbl.find('.badge').removeClass('badge-warning').addClass(
                                    'badge-success');
                            } else {
                                lbl.removeClass('strike');
                                lbl.find('.badge').removeClass('badge-success').addClass(
                                    'badge-warning');
                            }
                            lbl.find('.badge').html(response.status_label);

                            show_toastr('success', response.success);
                        } else {
                            show_toastr('error', response.error);
                        }
                    },
                    error: function(response) {
                        response = response.responseJSON;
                        if (response.is_success) {
                            show_toastr('success', response.success);
                        } else {
                            show_toastr('error', response.error);
                        }
                    }
                })
            });
        @endcan
    </script>
    <script>
    $(document).ready(function() {
        // Tab switching logic
        $('.tab-btn').on('click', function() {
            $('.tab-btn').removeClass('active bg-primary bg-gradient text-white');
            $('.tab-btn').css({'color':'#0f766e','background':'#fff'});
            $(this).addClass('active bg-primary bg-gradient text-white');
            $(this).css({'color':'#fff'});
            $('.tab-section').addClass('d-none');
            $('#tab-' + $(this).data('tab')).removeClass('d-none');
        });
        // Optionally, show the first tab by default
        $('.tab-btn[data-tab="followups"]').trigger('click');
        $('.tab-btn').hover(
            function() {
                if (!$(this).hasClass('active')) {
                    $(this).addClass('bg-primary bg-gradient text-white');
                    $(this).css({'color':'#fff'});
                }
            },
            function() {
                if (!$(this).hasClass('active')) {
                    $(this).removeClass('bg-primary bg-gradient text-white');
                    $(this).css({'color':'#0f766e','background':'#fff'});
                }
            }
        );
    });
    </script>
@endpush
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('leads.index') }}">{{ __('Lead') }}</a></li>
    <li class="breadcrumb-item"> {{ $lead->name }}</li>
@endsection
@section('action-btn')
    <div class="float-end">
        @can('convert lead to deal')
            @if (!empty($deal))
                <a href="@can('View Deal') @if ($deal->is_active) {{ route('deals.show', $deal->id) }} @else # @endif @else # @endcan"
                    data-size="lg" data-bs-toggle="tooltip" title=" {{ __('Already Converted To Deal') }}"
                    class="btn btn-sm bg-warning-subtle me-1">
                    <i class="ti ti-exchange"></i>
                </a>
            @endif
        @endcan

        <a href="#"
            data-url="{{ URL::to('leads/' . $lead->id . '/labels') }}"
            data-ajax-popup="true"
            data-size="lg"
            data-bs-toggle="tooltip"
            title="{{ __('Label') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-bookmark"></i>
        </a>

        <a href="#"
            data-size="lg"
            data-url="{{ route('leads.edit', $lead->id) }}"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            title="{{ __('Edit') }}"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-pencil"></i>
        </a>

    </div>
@endsection

@section('content')
<div class="row">
    <div class="col-sm-12">
        <div class="row">

            <div class="col-xl-9 w-100">
                <?php
                $tasks = $lead->tasks;
                $products = $lead->products();
                $sources = $lead->sources();
                $calls = $lead->calls;
                $emails = $lead->emails;
                ?>
                <div id="general" class="row">
                    <!-- First row: Stage, Phone, Pipeline, Created -->
                    <div class="col-lg-3 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="fa fa-layer-group text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold">{{ __('Stage') }}</h6>
                                    <p class="text-muted mb-0 text-break">
                                        {{ $lead->stage->name }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="ti ti-phone text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold">{{ __('Phone') }}</h6>
                                    <p class="text-muted mb-0 text-break">
                                        {{ !empty($lead->phone) ? $lead->phone : 'Not Available' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="fa fa-project-diagram text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold">{{ __('Pipeline') }}</h6>
                                    <p class="text-muted mb-0 text-break">
                                        {{ $lead->pipeline->name }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="ti ti-calendar text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold">{{ __('Created') }}</h6>
                                    <p class="text-muted mb-0 text-break">
                                        {{ \Auth::user()->dateFormat($lead->created_at) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Second row: Email, Source, (optionally) first custom field -->
                    <div class="col-lg-4 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="ti ti-mail text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold">{{ __('Email') }}</h6>
                                    <p class="text-muted mb-0 text-break">
                                        {{ !empty($lead->email) ? $lead->email : 'Not Available' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="fas fa-sitemap text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold">{{ __('Source') }}</h6>  
                                    <p class="text-muted mb-0 text-break">
                                         {{ !empty($lead->opportunity_source) ? $lead->opportunity_source : 'Not Available' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="fas fa-users-cog text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold">{{ __('Name') }}</h6>
                                    <p class="text-muted text-break mb-0">
                                        {{ !empty($lead->name) ? $lead->name : 'Not Available' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                   
                    <!-- Additional custom fields (if any) -->
                    @if(!$customFields->isEmpty() && $customFields->count() > 1)
                    <div id="custom_fields" class="row mt-4">
                        <div class="col-12">
                            <h4 class="mb-3">{{ __('Additional Information') }}</h4>
                        </div>
                        @foreach($customFields->slice(1) as $field)
                        <div class="col-md-4 col-sm-6 col-12 mb-4">
                            <div class="card report-card h-100 mb-0 border-0 rounded-3"
                                style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                                onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                                <div class="card-body d-flex align-items-center gap-3 p-4">
                                    <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                        style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                        <i class="fas fa-users-cog text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                    </div>
                                    <div class="report-info flex-1">
                                        <h6 class="mb-1 text-dark fw-semibold">{{ $field->name }}</h6>
                                        <p class="text-muted text-break mb-0">
                                            @php
                                            $value = $leadCustomValues[$field->id] ?? null;
                                            @endphp
                                            @if(is_array($value))
                                            {{ implode(', ', $value) }}
                                            @elseif(!is_null($value))
                                            {{ $value }}
                                            @else
                                            {{ __('Not set') }}
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @endif
                </div>
                <!-- Horizontal Tab Menu -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex flex-wrap justify-content-center gap-2 tab-menu-responsive ">
                            <button class="tab-btn active bg-primary bg-gradient" data-tab="followups">{{ __('Follow-Ups') }}</button>
                            <button class="tab-btn bg-primary bg-gradient" data-tab="usersproducts">{{ __('Assigned To') }}</button>
                            <button class="tab-btn bg-primary bg-gradient" data-tab="appointments">{{ __('Appointments') }}</button>
                            <button class="tab-btn bg-primary bg-gradient" data-tab="notesfiles">{{ __('Notes & Files') }}</button> 
                            <button class="tab-btn bg-primary bg-gradient" data-tab="activity">{{ __('Activity') }}</button>
                            <button class="tab-btn bg-primary bg-gradient" data-tab="comments">{{ __('Comments') }}</button>
                        </div>
                    </div>
                </div>
                <!-- Tab Content Sections -->
                <div class="tab-section mt-4" id="tab-followups">
                    <!-- Follow-Ups section (moved from #tasks) -->
                    <div class="card mb-2">
                        <div class="card-header">
                            <div class="d-flex align-items-center justify-content-between">
                                <h5>{{ __('Follow-Ups') }}</h5>
                                @can('create lead task')
                                <div class="float-end">
                                    <a href="#" data-size="lg" data-url="{{ route('leads.tasks.create', $lead->id) }}" data-ajax-popup="true" data-bs-toggle="tooltip" title="{{ __('Follow-Ups') }}" class="btn btn-sm btn-primary">
                                        <i class="ti ti-plus"></i>
                                    </a>
                                </div>
                                @endcan
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Name') }}</th>
                                            <th>{{ __('Status') }}</th>
                                            <th>{{ __('Due Date') }}</th>
                                            <th>{{ __('Assigned to') }}</th>
                                            <th>{{ __('Action') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($lead->tasks as $task)
                                        <tr>
                                            <td>
                                                <a href="#" class="lead-task-details-link text-primary" data-url="{{ route('leads.tasks.show', [$lead->id, $task->id]) }}" style="text-decoration:underline;cursor:pointer;">{{ $task->name }}</a>
                                            </td>
                                            <td>
                                                @if (isset($task->status) && isset(\App\Models\LeadTask::$status[$task->status]))
                                                    <div class="badge bg-success p-2 px-3 rounded">{{ __(\App\Models\LeadTask::$status[$task->status]) }}</div>
                                                @else
                                                    <div class="badge bg-warning p-2 px-3 rounded">{{ __('Unknown') }}</div>
                                                @endif
                                            </td>
                                            <td>{{ Auth::user()->dateFormat($task->date) }}</td>
                                            <td>{{ $task->assignedUser ? $task->assignedUser->name : '' }}</td>
                                            <td>
                                                @can('edit lead task')
                                                <div class="action-btn bg-info ms-2">
                                                    <a href="#" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-url="{{ route('leads.tasks.edit', [$lead->id, $task->id]) }}" data-ajax-popup="true" data-size="lg" data-bs-toggle="tooltip" title="{{ __('Edit') }}" data-title="{{ __('Edit Task') }}">
                                                        <i class="ti ti-pencil text-white"></i>
                                                    </a>
                                                </div>
                                                @endcan
                                                @can('delete lead task')
                                                <div class="action-btn bg-danger ms-2">
                                                    {!! Form::open(['method' => 'DELETE', 'route' => ['leads.tasks.destroy', $lead->id, $task->id], 'id' => 'delete-form-'.$task->id]) !!}
                                                    <a href="#" class="mx-3 btn btn-sm  align-items-center bs-pass-para" data-bs-toggle="tooltip" title="{{ __('Delete') }}">
                                                        <i class="ti ti-trash text-white"></i>
                                                    </a>
                                                    {!! Form::close() !!}
                                                </div>
                                                @endcan
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-section mt-4 d-none" id="tab-usersproducts">
                    <!-- Users & Products section (moved from #users_products) -->
                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <h5>{{ __('Users') }}</h5>
                                        <div class="float-end">
                                            <a href="#"
                                                data-size="md"
                                                data-url="{{ route('leads.users.edit', $lead->id) }}"
                                                data-ajax-popup="true"
                                                data-bs-toggle="tooltip"
                                                title="{{ __('Add User') }}"
                                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                                                onmouseover="this.style.transform='scale(1.1)'"
                                                onmouseout="this.style.transform='scale(1)'">
                                                <i class="ti ti-plus"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('Name') }}</th>
                                                    <th>{{ __('Action') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($lead->users as $user)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div>
                                                                <img @if ($user->avatar) src="{{ asset('/storage/uploads/avatar/' . $user->avatar) }}" @else src="{{ asset('/storage/uploads/avatar/avatar.png') }}" @endif
                                                                class=" rounded border-2 border border-primary wid-40 me-3"
                                                                alt="avatar image">
                                                            </div>
                                                            <p class="mb-0">{{ $user->name }}</p>
                                                        </div>
                                                    </td>
                                                    @can('edit lead')
                                                    <td>
                                                        <div class="action-btn me-2">
                                                            {!! Form::open([
                                                            'method' => 'DELETE',
                                                            'route' => ['leads.users.destroy', $lead->id, $user->id],
                                                            'id' => 'delete-form-' . $lead->id,
                                                            ]) !!}
                                                            <a href="#"
                                                                class="mx-3 btn btn-sm  align-items-center bs-pass-para bg-danger"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('Delete') }}"><i
                                                                    class="ti ti-trash text-white"></i></a>

                                                            {!! Form::close() !!}
                                                        </div>
                                                    </td>
                                                    @endcan
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-md-6 col-12 mb-4">
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <h5>{{ __('Products') }}</h5>
                                        <div class="float-end">
                                            <a href="#"
                                                data-size="md"
                                                data-url="{{ route('leads.products.edit', $lead->id) }}"
                                                data-ajax-popup="true"
                                                data-bs-toggle="tooltip"
                                                title="{{ __('Add Product') }}"
                                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                                                onmouseover="this.style.transform='scale(1.1)'"
                                                onmouseout="this.style.transform='scale(1)'">
                                                <i class="ti ti-plus"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('Name') }}</th>
                                                    <th>{{ __('Price') }}</th>
                                                    <th>{{ __('Action') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($lead->products() as $product)
                                                <tr>
                                                    <td>
                                                        {{ $product->name }}
                                                    </td>
                                                    <td>
                                                        {{ \Auth::user()->priceFormat($product->sale_price) }}
                                                    </td>
                                                    @can('edit lead')
                                                    <td>
                                                        <div class="action-btn me-2">
                                                            {!! Form::open(['method' => 'DELETE', 'route' => ['leads.products.destroy', $lead->id, $product->id]]) !!}
                                                            <a href="#"
                                                                class="mx-3 btn btn-sm  align-items-center bs-pass-para bg-danger"
                                                                data-bs-toggle="tooltip"
                                                                title="{{ __('Delete') }}"><i
                                                                    class="ti ti-trash text-white"></i></a>

                                                            {!! Form::close() !!}
                                                        </div>
                                                    </td>
                                                    @endcan
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>
                <!-- Appointments -->
                <div class="tab-section mt-4" id="tab-appointments">
                    <!-- Appointments Tab Switcher -->
                    <div class="card mb-2">
                        <div class="card-header">
                            <div class="d-flex align-items-center justify-content-between">
                                <h5>{{ __('Appointments') }}</h5>
                                <div class="float-end">
                                    <button type="button" class="btn btn-sm btn-primary" id="createAppointmentBtn" data-bs-toggle="modal" data-bs-target="#leadAppointmentBookingModal">
                                        <i class="ti ti-plus"></i> {{ __('Create Appointment') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Tab Switcher -->
                            <ul class="nav nav-pills mb-3 pb-3" id="appointments-tab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="upcoming-tab" data-bs-toggle="pill" data-bs-target="#upcoming-appointments" type="button" role="tab" aria-controls="upcoming-appointments" aria-selected="true">{{ __('Upcoming') }}</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="past-tab" data-bs-toggle="pill" data-bs-target="#past-appointments" type="button" role="tab" aria-controls="past-appointments" aria-selected="false">{{ __('Past') }}</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="daterange-tab" data-bs-toggle="pill" data-bs-target="#daterange-appointments" type="button" role="tab" aria-controls="daterange-appointments" aria-selected="false">{{ __('Date Range') }}</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="canceled-tab" data-bs-toggle="pill" data-bs-target="#canceled-appointments" type="button" role="tab" aria-controls="canceled-appointments" aria-selected="false">{{ __('Canceled') }}</button>
                                </li>
                            </ul>
                            <div class="tab-content" id="appointments-tabContent">
                                <!-- Upcoming Appointments -->
                                <div class="tab-pane fade show active" id="upcoming-appointments" role="tabpanel" aria-labelledby="upcoming-tab">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="upcoming-appointments-table">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('Event') }}</th>
                                                    <th>{{ __('Date') }}</th>
                                                    <th>{{ __('Time') }}</th>
                                                    <th>{{ __('Status') }}</th>
                                                    <th>{{ __('Actions') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Appointments will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center py-5 d-none" id="upcoming-no-appointments">
                                        <span class="text-muted">{{ __('No upcoming appointments.') }}</span>
                                    </div>
                                </div>
                                <!-- Past Appointments -->
                                <div class="tab-pane fade" id="past-appointments" role="tabpanel" aria-labelledby="past-tab">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="past-appointments-table">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('Event') }}</th>
                                                    <th>{{ __('Date') }}</th>
                                                    <th>{{ __('Time') }}</th>
                                                    <th>{{ __('Status') }}</th>
                                                    <th>{{ __('Actions') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Appointments will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center py-5 d-none" id="past-no-appointments">
                                        <span class="text-muted">{{ __('No past appointments.') }}</span>
                                    </div>
                                </div>
                                <!-- Date Range Appointments -->
                                <div class="tab-pane fade" id="daterange-appointments" role="tabpanel" aria-labelledby="daterange-tab">
                                    <div class="row mb-3 justify-content-center">
                                        <div class="col-md-4">
                                            <input type="date" class="form-control" id="date-range-from" placeholder="{{ __('From') }}">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="date" class="form-control" id="date-range-to" placeholder="{{ __('To') }}">
                                        </div>
                                        <div class="col-md-4">
                                            <button class="btn btn-primary w-100" id="filter-date-range">{{ __('Filter') }}</button>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="daterange-appointments-table">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('Event') }}</th>
                                                    <th>{{ __('Date') }}</th>
                                                    <th>{{ __('Time') }}</th>
                                                    <th>{{ __('Status') }}</th>
                                                    <th>{{ __('Actions') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Appointments will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center py-5 d-none" id="daterange-no-appointments">
                                        <span class="text-muted">{{ __('No appointments found for the selected date range.') }}</span>
                                    </div>
                                </div>
                                <!-- Canceled Appointments -->
                                <div class="tab-pane fade" id="canceled-appointments" role="tabpanel" aria-labelledby="canceled-tab">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="canceled-appointments-table">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('Event') }}</th>
                                                    <th>{{ __('Date') }}</th>
                                                    <th>{{ __('Time') }}</th>
                                                    <th>{{ __('Status') }}</th>
                                                    <th>{{ __('Actions') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Appointments will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center py-5 d-none" id="canceled-no-appointments">
                                        <span class="text-muted">{{ __('No canceled appointments.') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @push('script-page')
                <script>
                $(document).ready(function() {
                    // Date Range Filter for Appointments
                    $('#filter-date-range').on('click', function() {
                        var from = $('#date-range-from').val();
                        var to = $('#date-range-to').val();
                        $('#daterange-appointments-table tbody tr').each(function() {
                            var date = $(this).data('date');
                            if (from && date < from) {
                                $(this).hide();
                                return;
                            }
                            if (to && date > to) {
                                $(this).hide();
                                return;
                            }
                            $(this).show();
                        });
                    });
                });
                </script>
                @endpush
                <div class="tab-section mt-4 d-none" id="tab-notesfiles">
                    <!-- Notes & Files section (moved from #discussion_note) -->
                    <div class="row">
                        <div class="col-md-6 col-12 mb-4">
                            <!-- Notes Section -->
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                        <h5>{{ __('Notes') }}</h5>
                                        @php
                                        $user = \App\Models\User::find(\Auth::user()->creatorId());
                                        $plan = \App\Models\Plan::getPlan($user->plan);
                                        @endphp
                                        @if ($plan && $plan->chatgpt == 1)
                                        <div class="float-end d-flex flex-wrap align-items-center gap-2">
                                            <a href="#" data-size="md"
                                                class="btn btn-primary btn-icon btn-sm m-0"
                                                data-ajax-popup-over="true" id="grammarCheck"
                                                data-url="{{ route('grammar', ['grammar']) }}"
                                                data-bs-placement="top"
                                                data-title="{{ __('Grammar check with AI') }}">
                                                <i class="ti ti-rotate"></i>
                                                <span>{{ __('Grammar check with AI') }}</span>
                                            </a>
                                            <a href="#" data-size="md"
                                                class="btn btn-primary btn-icon btn-sm m-0"
                                                data-ajax-popup-over="true"
                                                data-url="{{ route('generate', ['lead']) }}"
                                                data-bs-placement="top"
                                                data-title="{{ __('Generate content with AI') }}">
                                                <i class="fas fa-robot"></i>
                                                <span>{{ __('Generate with AI') }}</span>
                                            </a>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="card-body">
                                    <textarea class="summernote-simple" name="note">{!! $lead->notes !!}</textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12 mb-4">
                            <!-- Files Section -->
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <h5>{{ __('Files') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="col-md-12 dropzone top-5-scroll browse-file" id="dropzonewidget" style="max-height: 150px; overflow-y: auto;">
                                        <div class="text-center text-muted py-4" style="pointer-events: none;">
                                            <i class="ti ti-upload" style="font-size: 2rem;"></i>
                                            <div>{{ __('Upload file or drag file to upload here') }}</div>
                                        </div>
                                    </div>
                                    <style>
                                        /* Hide Dropzone file previews */
                                        #dropzonewidget .dz-preview {
                                            display: none !important;
                                        }
                                    </style>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-4">
                            <!-- Uploaded Files List Section -->
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <h5>{{ __('Uploaded Files') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive" style="max-height: 250px; overflow-y: auto;">
                                        <table class="table table-hover align-middle mb-0" id="uploaded-files-table">
                                            <thead>
                                                <tr>
                                                    <th style="width:40px;"></th>
                                                    <th>{{ __('File Name') }}</th>
                                                    <th>{{ __('Uploaded At') }}</th>
                                                    <th>{{ __('Action') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($lead->files as $file)
                                                @if (\Illuminate\Support\Facades\Storage::disk('public')->exists($file->file_path))
                                                <tr>
                                                    <td>
                                                        <i class="ti {{ $file->icon }} fs-4 text-primary"></i>
                                                    </td>
                                                    <td class="text-break">
                                                        {{ $file->original_name ?? $file->file_name }}
                                                        @if($file->file_size)
                                                            <br><small class="text-muted">{{ $file->formatted_size }}</small>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <small class="text-muted">{{ $file->created_at ? $file->created_at->diffForHumans() : '-' }}</small>
                                                        @if($file->uploader)
                                                            <br><small class="text-muted">by {{ $file->uploader->name }}</small>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <a href="{{ route('leads.file.download', [$lead->id, $file->id]) }}" class="btn btn-sm btn-outline-info me-1" title="{{ __('Download') }}">
                                                            <i class="ti ti-download"></i>
                                                        </a>
                                                        @can('edit lead')
                                                        <button type="button" class="btn btn-sm btn-outline-danger delete-file-btn"
                                                            data-file-id="{{ $file->id }}"
                                                            data-delete-url="{{ route('leads.file.delete', [$lead->id, $file->id]) }}"
                                                            title="{{ __('Delete') }}">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                        @endcan
                                                    </td>
                                                </tr>
                                                @endif
                                                @empty
                                                <tr>
                                                    <td colspan="4" class="text-center text-muted">{{ __('No files uploaded yet.') }}</td>
                                                </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-section mt-4 d-none" id="tab-activity">
                    <!-- Activity section (moved from #activity) -->
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Activity') }}</h5>
                        </div>
                        <div class="card-body ">
                            <div class="row leads-scroll">
                                <ul class="event-cards list-group list-group-flush mt-3 w-100" style="max-height: 250px; overflow-y: auto;">
                                    @if (!$lead->activities->isEmpty())
                                    @foreach ($lead->activities as $activity)
                                    <li class="list-group-item card mb-3">
                                        <div class="row align-items-center justify-content-between">
                                            <div class="col-auto mb-3 mb-sm-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="theme-avtar bg-primary badge">
                                                        <i class="ti {{ $activity->logIcon() }}"></i>
                                                    </div>
                                                    <div class="ms-3">
                                                        <span
                                                            class="text-dark text-sm">{{ __($activity->log_type) }}</span>
                                                        <h6 class="m-0">{!! $activity->getLeadRemark() !!}</h6>
                                                        <small
                                                            class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-auto">

                                            </div>
                                        </div>
                                    </li>
                                    @endforeach
                                    @else
                                    No activity found yet.
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-section mt-4 d-none" id="tab-comments">
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="comments-youtube-ui bg-white text-dark p-3 rounded shadow-sm">
                                        <!-- Total Comments Count -->
                                        @php
                                            $totalComments = $lead->comments->where('parent_id', null)->count();
                                        @endphp
                                        <div class="d-flex align-items-center mb-3">
                                            <h5 class="mb-0">
                                                <i class="ti ti-message-circle me-1"></i>
                                                {{ __('Comments') }}
                                                <span class="badge bg-primary align-middle ms-2" style="font-size:1rem;">{{ $totalComments }}</span>
                                            </h5>
                                        </div>
                                        <!-- Comment Input -->
                                        <div class="d-flex align-items-start mb-4">
                                            <div class="flex-shrink-0">
                                                <div class="theme-avtar bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; font-size: 1.2rem; border-radius: 50%;">
                                                    {{ substr(Auth::user()->name ?? 'U', 0, 1) }}
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <form id="commentForm" action="{{route('leads.comments.store', $lead->id)}}" method="POST" enctype="multipart/form-data" class="w-100">
                                                    @csrf
                                                    <div class="mb-2">
                                                        <textarea name="comment" id="comment" class="form-control bg-white text-dark border-secondary" rows="2" placeholder="Add a comment..." required style="resize:none;"></textarea>
                                                    </div>
                                                    <div class="d-flex align-items-center gap-2">
                                                        <button type="submit" class="btn btn-primary btn-sm ms-auto px-4">Comment</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                        <!-- Comments List -->
                                        <div class="comments-list">
                                            @if(!empty($lead->comments) && count($lead->comments) > 0)
                                            @foreach($lead->comments->where('parent_id', null)->sortByDesc('created_at') as $comment)
                                            <div class="comment-item py-3 border-bottom border-secondary bg-white">
                                                <div class="d-flex align-items-start">
                                                    <div class="flex-shrink-0">
                                                        <div class="theme-avtar bg-primary text-white d-flex align-items-center justify-content-center" style="width: 36px; height: 36px; font-size: 1.1rem; border-radius: 50%;">
                                                            {{ substr($comment->user->name ?? 'U', 0, 1) }}
                                                        </div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex align-items-center gap-2 mb-1">
                                                            <span class="fw-semibold">{{$comment->user->name ?? __('Unknown User')}}</span>
                                                            <span class="text-muted small">{{$comment->created_at->diffForHumans()}}</span>
                                                        </div>
                                                        <div class="mb-2">{!! preg_replace('/@(\w+)/', '<span class="mentioned-user">@$1</span>', $comment->comment) !!}</div>
                                                        @if(isset($comment->files) && count($comment->files) > 0)
                                                        <div class="comment-files mb-2">
                                                            <small class="text-muted d-block mb-1">{{__('Attachments')}}:</small>
                                                            <div class="d-flex flex-wrap gap-1">
                                                                @foreach($comment->files as $file)
                                                                <a href="#" class="badge bg-info text-decoration-none" title="{{$file->original_name ?? $file->name}}">
                                                                    <i class="ti ti-paperclip"></i> {{Str::limit($file->original_name ?? $file->name, 15)}}
                                                                </a>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                        @endif
                                                        <div class="d-flex align-items-center gap-2 mt-1">
                                                            <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1 reaction-btn" data-comment-id="{{$comment->id}}" data-reaction="like"><i class="ti ti-thumb-up"></i></button>
                                                            <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1 reaction-btn" data-comment-id="{{$comment->id}}" data-reaction="love"><i class="ti ti-heart"></i></button>
                                                            <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1 reply-btn" data-comment-id="{{$comment->id}}" data-user-name="{{$comment->user->name}}"><i class="ti ti-message-reply"></i> Reply</button>
                                                            <div class="dropdown ms-auto">
                                                                <button class="btn btn-sm btn-outline-secondary px-2 py-1 dropdown-toggle" type="button" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical"></i></button>
                                                                <ul class="dropdown-menu">
                                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteComment({{$comment->id}})"><i class="ti ti-trash"></i> {{__('Delete')}}</a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="reaction-counts mt-1" id="reaction-counts-{{$comment->id}}">
                                                            @php
                                                            $reactions = $comment->comment_reaction ?? [];
                                                            $counts = [];
                                                            foreach ($reactions as $reaction_data) {
                                                            $reaction = $reaction_data['reaction'];
                                                            if (!isset($counts[$reaction])) {
                                                            $counts[$reaction] = 0;
                                                            }
                                                            $counts[$reaction]++;
                                                            }
                                                            @endphp
                                                            @if(!empty($counts))
                                                            @foreach($counts as $reaction => $count)
                                                            <span class="badge bg-light text-dark me-1">
                                                                @if($reaction == 'like') 👍 @elseif($reaction == 'love') ❤️ @endif
                                                                {{$count}}
                                                            </span>
                                                            @endforeach
                                                            @endif
                                                        </div>
                                                        <!-- Reply Form (hidden by default) -->
                                                        <div class="reply-form mt-3" id="reply-form-{{$comment->id}}" style="display: none;">
                                                            <div class="d-flex align-items-start">
                                                                <div class="theme-avtar bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 1rem; border-radius: 50%;">
                                                                    {{ substr(Auth::user()->name ?? 'U', 0, 1) }}
                                                                </div>
                                                                <div class="flex-grow-1 ms-2">
                                                                    <textarea class="form-control reply-textarea bg-white text-dark border-secondary" rows="2" placeholder="Reply..." data-comment-id="{{$comment->id}}"></textarea>
                                                                    <div class="d-flex justify-content-end gap-2 mt-2">
                                                                        <button type="button" class="btn btn-sm btn-secondary cancel-reply" data-comment-id="{{$comment->id}}">{{__('Cancel')}}</button>
                                                                        <button type="button" class="btn btn-sm btn-primary submit-reply" data-comment-id="{{$comment->id}}">{{__('Reply')}}</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <!-- Replies -->
                                                        <div class="replies mt-3 ms-4" id="replies-{{$comment->id}}">
                                                            @php
                                                            $replies = $lead->comments->where('parent_id', $comment->id)->sortBy('created_at');
                                                            @endphp
                                                            @if($replies && count($replies) > 0)
                                                            @foreach($replies as $reply)
                                                            <div class="reply-item d-flex align-items-start mb-2 bg-light">
                                                                <div class="theme-avtar bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 1rem; border-radius: 50%;">
                                                                    {{ substr($reply->user->name ?? 'U', 0, 1) }}
                                                                </div>
                                                                <div class="flex-grow-1 ms-2">
                                                                    <div class="d-flex align-items-center gap-2 mb-1">
                                                                        <span class="fw-semibold small">{{$reply->user->name ?? __('Unknown User')}}</span>
                                                                        <span class="text-muted small">{{$reply->created_at->diffForHumans()}}</span>
                                                                    </div>
                                                                    <div class="mb-1 small">{!! preg_replace('/@(\w+)/', '<span class="mentioned-user">@$1</span>', $reply->comment) !!}</div>
                                                                    <div class="d-flex align-items-center gap-2 mt-1">
                                                                        <!-- <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1 reply-btn" data-comment-id="{{$reply->id}}" data-user-name="{{$reply->user->name}}"><i class="ti ti-message-reply"></i> Reply</button> -->
                                                                        <div class="dropdown ms-auto">
                                                                            <button class="btn btn-sm btn-outline-secondary px-2 py-1 dropdown-toggle" type="button" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical"></i></button>
                                                                            <ul class="dropdown-menu">
                                                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteComment({{$reply->id}})"><i class="ti ti-trash"></i> {{__('Delete')}}</a></li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            @endforeach
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                            @else
                                            <div class="text-center py-4">
                                                <i class="ti ti-message-circle text-muted" style="font-size: 3rem;"></i>
                                                <p class="text-muted mt-2">{{__('No comments yet')}}</p>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{{-- Add Comment Modal --}}
<div class="modal fade" id="addCommentModal" tabindex="-1" aria-labelledby="addCommentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{route('leads.comments.store', $lead->id)}}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="addCommentModalLabel">{{__('Add Comment')}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="comment" class="form-label">{{__('Comment')}}</label>
                        <textarea name="comment" id="comment" class="form-control mention-textarea" rows="4" placeholder="{{__('Enter your comment... Type @ to mention users')}}" required></textarea>
                        <small class="text-muted">{{__('Type @ followed by a name to mention users')}}</small>
                    </div>

                    <div class="form-group mt-3">
                        <label for="comment_files" class="form-label">{{__('Attach Files (Optional)')}}</label>
                        <input type="file" name="comment_files[]" id="comment_files" class="form-control" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.txt">
                        <small class="text-muted">{{__('You can upload multiple files (Images, PDFs, Documents)')}}</small>
                    </div>

                    <div id="mention-dropdown" class="mention-dropdown" style="display: none;">
                        <ul class="list-group" id="mention-list"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{__('Cancel')}}</button>
                    <button type="submit" class="btn btn-primary">{{__('Add Comment')}}</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- Delete File Confirmation Modal --}}
<div class="modal fade" id="deleteFileModal" tabindex="-1" aria-labelledby="deleteFileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h5 class="modal-title" id="deleteFileModalLabel">
                    <i class="ti ti-alert-triangle text-warning me-2"></i>
                    {{ __('Delete File') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">
                <div class="text-center mb-3">
                    <div class="avatar avatar-lg bg-danger-light rounded-circle mx-auto mb-3">
                        <i class="ti ti-trash fs-2 text-danger"></i>
                    </div>
                    <h6 class="mb-2">{{ __('Are you sure?') }}</h6>
                    <p class="text-muted mb-0">
                        {{ __('You are about to delete') }} "<span id="deleteFileName" class="fw-bold"></span>".
                        <br>{{ __('This action cannot be undone.') }}
                    </p>
                </div>
            </div>
            <div class="modal-footer border-0 pt-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>{{ __('Cancel') }}
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="ti ti-trash me-1"></i>{{ __('Delete') }}
                </button>
            </div>
        </div>
    </div>
</div>

{{-- Add this modal at the end of the file if not present --}}
<div class="modal fade" id="leadTaskDetailsModal" tabindex="-1" aria-labelledby="leadTaskDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leadTaskDetailsModalLabel">{{__('Follow Up Details')}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
@push('script-page')
<script>
$(document).ready(function() {
    // Get real users for mentions
    let users = @json($mentionUsers ?? []);

    // Reaction functionality
    $(document).on('click', '.reaction-btn', function() {
        let btn = $(this);
        let commentId = btn.data('comment-id');
        let reaction = btn.data('reaction');
        
        $.ajax({
            url: `/leads/comments/${commentId}/react`,
            method: 'POST',
            data: { reaction: reaction },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            },
            success: function(response) {
                if (response.success) {
                    updateReactionCounts(commentId, response.reactions);
                    btn.removeClass('btn-outline-primary btn-outline-danger').addClass('btn-' + getReactionColor(reaction));
                }
            },
            error: function(xhr, status, error) {
                console.error('Reaction error:', xhr.responseText);
                alert('Error updating reaction');
            }
        });
    });

    // Reply functionality
    $(document).on('click', '.reply-btn', function() {
        let commentId = $(this).data('comment-id');
        let userName = $(this).data('user-name');
        
        // Hide all other reply forms
        $('.reply-form').hide();
        
        // Show this reply form
        $(`#reply-form-${commentId}`).show();
        
        // Focus on textarea and add @mention
        let textarea = $(`#reply-form-${commentId} .reply-textarea`);
        textarea.focus();
        textarea.val(`@${userName} `);
    });

    $(document).on('click', '.cancel-reply', function() {
        let commentId = $(this).data('comment-id');
        $(`#reply-form-${commentId}`).hide();
        $(`#reply-form-${commentId} .reply-textarea`).val('');
    });

    $(document).on('click', '.submit-reply', function() {
    let commentId = $(this).data('comment-id');
    let replyText = $(`#reply-form-${commentId} .reply-textarea`).val();
    
    if (replyText.trim() === '') {
        alert('Please enter a reply');
        return;
    }
    
    // Submit reply to backend instead of just frontend
    submitReplyToBackend(commentId, replyText);
});

// Add this new function to actually save to database
function submitReplyToBackend(parentId, replyText) {
    $.ajax({
        url: '{{route("leads.comments.store", $lead->id)}}',
        method: 'POST',
        data: {
            comment: replyText,
            parent_id: parentId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            console.log('Reply saved successfully');
            // Hide reply form and clear textarea
            $(`#reply-form-${parentId}`).hide();
            $(`#reply-form-${parentId} .reply-textarea`).val('');
            // Reload page to show new reply
            location.reload();
        },
        error: function(xhr, status, error) {
            console.error('Reply error:', xhr.responseText);
            console.log('Data sent:', {
                comment: replyText,
                parent_id: parentId
            });
            alert('Error submitting reply');
        }
    });
}

    // Mention functionality
    $('#comment').on('input', function(e) {
        let text = $(this).val();
        let cursorPos = this.selectionStart;
        let atIndex = text.lastIndexOf('@', cursorPos - 1);
        
        if (atIndex !== -1) {
            let textAfterAt = text.substring(atIndex + 1, cursorPos);
            if (!textAfterAt.includes(' ')) {
                showMentions(textAfterAt);
            } else {
                $('#mention-dropdown').hide();
            }
        } else {
            $('#mention-dropdown').hide();
        }
    });

    function showMentions(query) {
        let filtered = users.filter(u => u.name.toLowerCase().includes(query.toLowerCase()));
        let html = '';
        filtered.forEach(user => {
            html += `<li class="list-group-item mention-item" data-name="${user.name}">${user.name}</li>`;
        });
        $('#mention-list').html(html);
        if (filtered.length > 0) {
            $('#mention-dropdown').show();
        }
    }

    $(document).on('click', '.mention-item', function() {
        let name = $(this).data('name');
        let text = $('#comment').val();
        let newText = text.replace(/@\w*$/, `@${name} `);
        $('#comment').val(newText);
        $('#mention-dropdown').hide();
    });

    // File deletion functionality is now handled in the main script section above
});

// Add this function after the document ready block or inside it
function deleteComment(commentId) {
    // Removed confirm dialog as requested
    $.ajax({
        url: '/leads/comments/' + commentId,
        type: 'DELETE',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // Remove the comment or reply from the DOM
                // Try to remove reply-item first, if not found, remove comment-item
                var el = $(
                    '.reply-item:has([onclick*="deleteComment(' + commentId + ')"])'
                );
                if (el.length === 0) {
                    el = $(
                        '.comment-item:has([onclick*="deleteComment(' + commentId + ')"])'
                    );
                }
                el.fadeOut(300, function() { $(this).remove(); });
                // Show success message
                if (typeof show_toastr === 'function') {
                    show_toastr('success', response.message || 'Comment deleted successfully', 'success');
                } else {
                    alert(response.message || 'Comment deleted successfully');
                }
            } else {
                if (typeof show_toastr === 'function') {
                    show_toastr('error', response.message || 'Error deleting comment', 'error');
                } else {
                    alert(response.message || 'Error deleting comment');
                }
            }
        },
        error: function(xhr) {
            if (typeof show_toastr === 'function') {
                show_toastr('error', 'Error deleting comment', 'error');
            } else {
                alert('Error deleting comment');
            }
        }
    });
}

// Add or update this function after document ready
function updateReactionCounts(commentId, reactions) {
    let countsHtml = '';
    const icons = { 'like': '👍', 'love': '❤️' };
    for (let reaction in reactions) {
        if (reactions[reaction] > 0) {
            countsHtml += `<span class="badge bg-light text-dark me-1">${icons[reaction] || ''} ${reactions[reaction]}</span>`;
        }
    }
    $(`#reaction-counts-${commentId}`).html(countsHtml);
}
</script>
@endpush
    <!-- Lead Appointment Booking Modal -->
    <div class="modal fade" id="leadAppointmentBookingModal" tabindex="-1" aria-labelledby="leadAppointmentBookingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="leadAppointmentBookingModalLabel">{{ __('Create Appointment for') }} {{ $lead->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="leadAppointmentBookingForm">
                        @csrf
                        <!-- Lead Information Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="lead_appointment_name" class="form-label">{{ __('Lead Name') }}</label>
                                    <input type="text" class="form-control" id="lead_appointment_name" name="name" value="{{ $lead->name }}" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="lead_appointment_email" class="form-label">{{ __('Email') }}</label>
                                    <input type="email" class="form-control" id="lead_appointment_email" name="email" value="{{ $lead->email }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="lead_appointment_phone" class="form-label">{{ __('Phone') }}</label>
                                    <input type="text" class="form-control" id="lead_appointment_phone" name="phone" value="{{ $lead->phone }}" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Calendar Event Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="lead_calendar_event_select" class="form-label">{{ __('Calendar Event') }}</label>
                                    <select class="form-control" id="lead_calendar_event_select" name="event_id" required>
                                        <option value="">{{ __('Select Event') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Event Location and Value Section -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="lead_event_location_select" class="form-label">{{ __('Event Location') }}</label>
                                    <select class="form-control" id="lead_event_location_select" name="event_location" required>
                                        <option value="">{{ __('Please select event') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="lead_event_location_value" class="form-label">{{ __('Event Location Value') }}</label>
                                    <input type="text" class="form-control" id="lead_event_location_value" name="event_location_value" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Event Date and Timezone Section -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="lead_event_date" class="form-label">{{ __('Event Date') }}</label>
                                    <input type="date" class="form-control" id="lead_event_date" name="event_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="lead_timezone_select" class="form-label">{{ __('Timezone') }}</label>
                                    <select class="form-control" id="lead_timezone_select" name="time_zone" required>
                                        <option value="Asia/Calcutta">Asia/Calcutta</option>
                                        <option value="America/New_York">America/New_York</option>
                                        <option value="Europe/London">Europe/London</option>
                                        <option value="Asia/Tokyo">Asia/Tokyo</option>
                                        <option value="Australia/Sydney">Australia/Sydney</option>
                                        <option value="America/Los_Angeles">America/Los_Angeles</option>
                                        <option value="Europe/Paris">Europe/Paris</option>
                                        <option value="Asia/Dubai">Asia/Dubai</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Timeslots Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="lead_timeslots_select" class="form-label">{{ __('Timeslots') }}</label>
                                    <select class="form-control" id="lead_timeslots_select" name="time_slots" required>
                                        <option value="">{{ __('Please select event and date') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" id="lead_id" name="lead_id" value="{{ $lead->id }}">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                    <button type="button" class="btn btn-success" id="saveLeadAppointmentBtn">{{ __('Create Appointment') }}</button>
                </div>
            </div>
            </div>
</div>

<!-- View Appointment Modal -->
<div class="modal fade" id="viewAppointmentModal" tabindex="-1" aria-labelledby="viewAppointmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewAppointmentModalLabel">{{ __('Appointment Details') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Event') }}</label>
                            <p id="view_event_title" class="mb-0"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Status') }}</label>
                            <p id="view_appointment_status" class="mb-0"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Date') }}</label>
                            <p id="view_appointment_date" class="mb-0"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Time') }}</label>
                            <p id="view_appointment_time" class="mb-0"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Customer Name') }}</label>
                            <p id="view_customer_name" class="mb-0"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Email') }}</label>
                            <p id="view_customer_email" class="mb-0"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Phone') }}</label>
                            <p id="view_customer_phone" class="mb-0"></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Location') }}</label>
                            <p id="view_appointment_location" class="mb-0"></p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group mb-3">
                            <label class="form-label fw-bold">{{ __('Notes') }}</label>
                            <p id="view_appointment_notes" class="mb-0"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>

@push('script-page')
<script>
$(document).on('click', '.lead-task-details-link', function(e) {
    e.preventDefault();
    var url = $(this).data('url');
    $('#leadTaskDetailsModal .modal-body').html('<div class="text-center py-5"><i class="ti ti-loader"></i></div>');
    $('#leadTaskDetailsModal').modal('show');
    $.get(url, function(data) {
        $('#leadTaskDetailsModal .modal-body').html(data);
    });
});

// Lead Appointment Booking JavaScript
$(document).ready(function() {
    // Prevent multiple event bindings
    $('#saveLeadAppointmentBtn').off('click').on('click', function() {
        const form = $('#leadAppointmentBookingForm');

        // Validate required fields
        const eventId = $('#lead_calendar_event_select').val();
        const eventLocation = $('#lead_event_location_select').val();
        const eventDate = $('#lead_event_date').val();
        const timeSlot = $('#lead_timeslots_select').val();
        const timezone = $('#lead_timezone_select').val();

        if (!eventId || !eventLocation || !eventDate || !timeSlot || !timezone) {
            show_toastr('error', 'Please fill in all required fields');
            return;
        }

        // Disable button to prevent double submission
        $(this).prop('disabled', true).text('{{ __("Creating...") }}');

        // Format data for the booking API
        const bookingData = {
            _token: $('meta[name="csrf-token"]').attr('content'),
            name: $('#lead_appointment_name').val(),
            email: $('#lead_appointment_email').val(),
            phone: $('#lead_appointment_phone').val(),
            event_id: eventId,
            date: eventDate,
            time: timeSlot,
            selected_location: {
                type: eventLocation,
                value: $('#lead_event_location_value').val()
            },
            lead_id: $('#lead_id').val()
        };

        $.ajax({
            url: '{{ route("public-bookings.store") }}',
            method: 'POST',
            data: bookingData,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message || 'Appointment created successfully');
                    $('#leadAppointmentBookingModal').modal('hide');

                    // Reset form
                    form[0].reset();
                    $('#lead_calendar_event_select').html('<option value="">{{ __("Select Event") }}</option>');
                    $('#lead_event_location_select').html('<option value="">{{ __("Please select event") }}</option>');
                    $('#lead_timeslots_select').html('<option value="">{{ __("Please select event and date") }}</option>');
                    $('#lead_event_location_value').val('');

                    // Reload appointments
                    loadLeadAppointments();
                } else {
                    show_toastr('error', response.message || 'Failed to create appointment');
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to create appointment:', error);
                let errorMessage = 'Failed to create appointment';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                $('#saveLeadAppointmentBtn').prop('disabled', false).text('{{ __("Create Appointment") }}');
            }
        });
    });

    // Load available events when modal is shown
    $('#leadAppointmentBookingModal').on('shown.bs.modal', function() {
        loadAvailableEventsForLead();
    });

    // Load available events
    function loadAvailableEventsForLead() {
        $.ajax({
            url: '{{ route("calendar-events.index") }}',
            method: 'GET',
            success: function(response) {
                const eventSelect = $('#lead_calendar_event_select');
                eventSelect.html('<option value="">{{ __("Select Event") }}</option>');

                if (response.success && response.data.length > 0) {
                    response.data.forEach(function(event) {
                        eventSelect.append(`<option value="${event.id}" data-location="${event.location || ''}" data-location-value="${event.location === 'in_person' ? (event.physical_address || '') : (event.meet_link || '')}">${event.title} (${event.duration || 60} min)</option>`);
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading events:', error);
            }
        });
    }

    // Event selection change handler
    $('#lead_calendar_event_select').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const eventLocation = $('#lead_event_location_select');
        const locationValue = $('#lead_event_location_value');

        eventLocation.html('<option value="">{{ __("Please select event") }}</option>');

        if (selectedOption.val()) {
            const location = selectedOption.data('location');
            const locationValueText = selectedOption.data('location-value');

            if (location) {
                eventLocation.append(`<option value="${location}">${location}</option>`);
                locationValue.val(locationValueText);
            }
        } else {
            locationValue.val('');
        }
    });

    // Event date change handler
    $('#lead_event_date').on('change', function() {
        const eventId = $('#lead_calendar_event_select').val();
        const selectedDate = $(this).val();

        if (eventId && selectedDate) {
            loadTimeSlotsForLead(eventId, selectedDate);
        } else {
            $('#lead_timeslots_select').html('<option value="">{{ __("Please select event and date") }}</option>');
        }
    });

    function loadTimeSlotsForLead(eventId, date) {
        $.ajax({
            url: '{{ route("calendar-events.timeslots") }}',
            method: 'GET',
            data: {
                event_id: eventId,
                date: date
            },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                const timeslotsSelect = $('#lead_timeslots_select');
                timeslotsSelect.html('<option value="">{{ __("Select Time Slot") }}</option>');

                if (response.success && response.data && response.data.length > 0) {
                    response.data.forEach(function(slot) {
                        timeslotsSelect.append(`<option value="${slot.time}">${slot.time}</option>`);
                    });
                } else {
                    timeslotsSelect.html('<option value="">{{ __("No available slots") }}</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Failed to load time slots:', error);
                $('#lead_timeslots_select').html('<option value="">{{ __("Error loading slots") }}</option>');
            }
        });
    }



    // Reset appointment modal when closed
    $('#leadAppointmentBookingModal').on('hidden.bs.modal', function() {
        const form = $('#leadAppointmentBookingForm');
        form[0].reset();
        $('#lead_calendar_event_select').html('<option value="">{{ __("Select Event") }}</option>');
        $('#lead_event_location_select').html('<option value="">{{ __("Please select event") }}</option>');
        $('#lead_timeslots_select').html('<option value="">{{ __("Please select event and date") }}</option>');
        $('#lead_event_location_value').val('');
        $('#saveLeadAppointmentBtn').prop('disabled', false).text('{{ __("Create Appointment") }}');
    });

    // Load appointments when appointments tab is clicked
    $('.tab-btn[data-tab="appointments"]').on('click', function() {
        loadLeadAppointments();
    });

    // Event delegation for view and cancel appointment buttons
    $(document).on('click', '.view-appointment-btn', function(e) {
        e.preventDefault();
        const appointmentId = $(this).data('appointment-id');
        viewAppointment(appointmentId);
    });

    $(document).on('click', '.cancel-appointment-btn', function(e) {
        e.preventDefault();
        const appointmentId = $(this).data('appointment-id');
        cancelAppointment(appointmentId);
    });

    // Function to load lead appointments
    function loadLeadAppointments() {
        const leadId = {{ $lead->id }};
        const leadEmail = '{{ $lead->email }}';
        const leadName = '{{ $lead->name }}';

        // Load upcoming appointments
        loadAppointmentsByType('upcoming', leadEmail, leadName);
        
        // Load past appointments
        loadAppointmentsByType('past', leadEmail, leadName);
        
        // Load canceled appointments
        loadAppointmentsByType('canceled', leadEmail, leadName);
    }

    function loadAppointmentsByType(type, email, name) {
        $.ajax({
            url: '{{ route("appointments.index") }}',
            method: 'GET',
            data: {
                type: type,
                email: email,
                name: name
            },
            success: function(response) {
                if (response.success) {
                    displayAppointments(type, response.data);
                } else {
                    displayAppointments(type, []);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading appointments:', error);
                displayAppointments(type, []);
            }
        });
    }

    function displayAppointments(type, appointments) {
        const tableId = `${type}-appointments-table`;
        const noAppointmentsId = `${type}-no-appointments`;
        const tbody = $(`#${tableId} tbody`);
        const noAppointmentsDiv = $(`#${noAppointmentsId}`);

        tbody.empty();

        if (appointments.length > 0) {
            appointments.forEach(function(appointment) {
                const row = `
                    <tr>
                        <td>${appointment.event ? appointment.event.title : 'N/A'}</td>
                        <td>${appointment.date}</td>
                        <td>${appointment.time}</td>
                        <td>
                            <span class="badge bg-${getStatusBadgeColor(appointment.status)}">${appointment.status}</span>
                        </td>
                        <td>
                            <div class="action-btn bg-info ms-2">
                                <a href="#" class="mx-3 btn btn-sm d-inline-flex align-items-center view-appointment-btn" data-appointment-id="${appointment.id}" data-bs-toggle="tooltip" title="{{ __('View') }}">
                                    <i class="ti ti-eye text-white"></i>
                                </a>
                            </div>
                            ${appointment.status === 'confirmed' ? `
                            <div class="action-btn bg-danger ms-2">
                                <a href="#" class="mx-3 btn btn-sm d-inline-flex align-items-center cancel-appointment-btn" data-appointment-id="${appointment.id}" data-bs-toggle="tooltip" title="{{ __('Cancel') }}">
                                    <i class="ti ti-x text-white"></i>
                                </a>
                            </div>
                            ` : ''}
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
            noAppointmentsDiv.addClass('d-none');
        } else {
            noAppointmentsDiv.removeClass('d-none');
        }
    }

    function getStatusBadgeColor(status) {
        switch (status) {
            case 'confirmed': return 'success';
            case 'pending': return 'warning';
            case 'canceled': return 'danger';
            case 'completed': return 'info';
            default: return 'secondary';
        }
    }
});

// Global function for status badge colors
function getStatusBadgeColor(status) {
    switch (status) {
        case 'confirmed': return 'success';
        case 'pending': return 'warning';
        case 'canceled': return 'danger';
        case 'completed': return 'info';
        default: return 'secondary';
    }
}

// Global functions for appointment actions
function viewAppointment(appointmentId) {
    // Show loading state
    $('#viewAppointmentModal .modal-body').html('<div class="text-center py-5"><i class="ti ti-loader"></i> Loading...</div>');
    $('#viewAppointmentModal').modal('show');
    
    // Fetch appointment details
    $.ajax({
        url: `/appointments/${appointmentId}`,
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success && response.data) {
                const appointment = response.data;
                
                // Populate modal with appointment details
                $('#view_event_title').text(appointment.event ? appointment.event.title : 'N/A');
                $('#view_appointment_status').html(`<span class="badge bg-${getStatusBadgeColor(appointment.status)}">${appointment.status}</span>`);
                $('#view_appointment_date').text(appointment.date);
                $('#view_appointment_time').text(appointment.time);
                $('#view_customer_name').text(appointment.name || 'N/A');
                $('#view_customer_email').text(appointment.email || 'N/A');
                $('#view_customer_phone').text(appointment.phone || 'N/A');
                $('#view_appointment_location').text(appointment.selected_location ? appointment.selected_location.value : 'N/A');
                $('#view_appointment_notes').text(appointment.notes || 'No notes available');
                
                // Restore modal body
                $('#viewAppointmentModal .modal-body').html(`
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Event') }}</label>
                                <p id="view_event_title" class="mb-0"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Status') }}</label>
                                <p id="view_appointment_status" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Date') }}</label>
                                <p id="view_appointment_date" class="mb-0"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Time') }}</label>
                                <p id="view_appointment_time" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Customer Name') }}</label>
                                <p id="view_customer_name" class="mb-0"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Email') }}</label>
                                <p id="view_customer_email" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Phone') }}</label>
                                <p id="view_customer_phone" class="mb-0"></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Location') }}</label>
                                <p id="view_appointment_location" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">{{ __('Notes') }}</label>
                                <p id="view_appointment_notes" class="mb-0"></p>
                            </div>
                        </div>
                    </div>
                `);
                
                // Populate the data again
                $('#view_event_title').text(appointment.event ? appointment.event.title : 'N/A');
                $('#view_appointment_status').html(`<span class="badge bg-${getStatusBadgeColor(appointment.status)}">${appointment.status}</span>`);
                $('#view_appointment_date').text(appointment.date);
                $('#view_appointment_time').text(appointment.time);
                $('#view_customer_name').text(appointment.name || 'N/A');
                $('#view_customer_email').text(appointment.email || 'N/A');
                $('#view_customer_phone').text(appointment.phone || 'N/A');
                $('#view_appointment_location').text(appointment.selected_location ? appointment.selected_location.value : 'N/A');
                $('#view_appointment_notes').text(appointment.notes || 'No notes available');
            } else {
                show_toastr('error', 'Failed to load appointment details');
                $('#viewAppointmentModal').modal('hide');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading appointment details:', error);
            show_toastr('error', 'Error loading appointment details');
            $('#viewAppointmentModal').modal('hide');
        }
    });
}

function cancelAppointment(appointmentId) {
    if (confirm('{{ __("Are you sure you want to cancel this appointment?") }}')) {
        $.ajax({
            url: `/appointments/${appointmentId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', 'Appointment canceled successfully');
                    loadLeadAppointments();
                } else {
                    show_toastr('error', response.message || 'Failed to cancel appointment');
                }
            },
            error: function(xhr, status, error) {
                show_toastr('error', 'Error canceling appointment');
            }
        });
    }
}
</script>
@endpush
@endsection