{"__meta": {"id": "Xc72e3dda1355ab2a941aad0e3e241796", "datetime": "2025-07-31 04:48:55", "utime": **********.406, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937334.248043, "end": **********.406029, "duration": 1.1579859256744385, "duration_str": "1.16s", "measures": [{"label": "Booting", "start": 1753937334.248043, "relative_start": 0, "end": **********.329424, "relative_end": **********.329424, "duration": 1.081380844116211, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.32945, "relative_start": 1.***************, "end": **********.406032, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "76.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6KdxnXlKJPlDQzTxeeZjfoOBvk5oAaMEaRdM3Ren", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-1385866722 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1385866722\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1659941060 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1659941060\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-483908715 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-483908715\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-618375467 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618375467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-899908263 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-899908263\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2110232054 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjB6Y2V5NnR3YitHUElvMzdYK3NvdGc9PSIsInZhbHVlIjoiQXZTbzF6dHZHcTE3N3NjSmFlcFNjRFEyUzhkdjJTUlhBTHE2N2UvUG10b0JLcmh5Ni8vcTFlY3J6V2lFb2hzMkF5TnRFclhPWXhzTDlrM0VzaUNqc01FMzNzeTRacktpNUtVT05LSnV4VDlZVjNHOHYyVHRFNkJDWURrTWVCMmMwZHBjSHJuVkx0bDdDVUNyMG1JclhtT0tYeDRtSVhzZTlHRlI4b2NrMlN4cmpYbkFqSzVXWmNQR0ZhaWZINjJaNGJ4S3BYZ1l2R2FsWEpZT2cwa3Z6Z0t0WnNGWWhDOWpXbUZnUmxqVGtqcmhkSk4zdWpYOXg0YnA4dXB5OTlPZDlyaGhGUm50bDRBa1pZWEs3bCtLMWViSU9kZ2dKMTBUQXJvbUNBSGc5aHJzV0hTWWEwOHl4bHI4bHBGZVdGSit1RDlBNGJrOE9PNUw2MTFwNmk4R2FyVmNzNEEybzJoNkYwb3Z1R2ZGdW04SUVWcXAyRU1DTnNSUzRERDV1M0J2MXdqbTdsaWErb2gzb083OGJlbFQvSkhpcDZScENMWm5vKy9WUHgyOEVvWnQ2WHFZV2g1bmphZ0lWbGc3NFRKWlBDR2xxaHNGQkI1LzNsbnl4QW41RjZmVnJDRWp4dDBQM29LK25rOXlRUDhFbEE5QW1WbWFON2kvZ2l6NjJBcnMiLCJtYWMiOiI4NjJhMTU5MWNiZTdmNTUwNGRiN2YxMmYzNjBjZDgwOTFhNmM0NmFiZTAxOWUxMTQyY2E2NmE4Nzc4MWZiZWY4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImpqZG5uZ3RYc0djNjdyWE1DYVk3UXc9PSIsInZhbHVlIjoiUERYcWN2SkZYSnM3WWtuYVRjUis5Z2Rqa0JjcFdXMzg5R2g5bWVwbWFIR254cGw2SVRQaER6RTFWS0pMTlZBN3BDbnlsbHlFbkVFckd1RHNFMUlhVnBMUnlVMXRqSzZaYkd0S3IrN2pRV3dFU1EzYVlzSks5aVptNTJLaWRFL01ULzZTNVlIS2RtOWlBN0tKUzNFTi93cGRNaHlUU2U3QXRYR1FlcllmcGRWMXdvMDUvaFhQRE5xOFRSRXBLSkhKODJyWWx4MVVHS2NteVBxTk5tTEc1V25uUU01Vm10YlZsNnA3N0p0ejFqUFNEL2FRalk2a1JHR1ZqU1g1Ui9pRmVaZWZHYkF4Q2lHcE5FMVVNaDlNc1BqUmVXbjlVQXFDTmlRdUZ0ZTZMcTQ3dm1MY0VvU2txdVJqWE1LTEk5TUR5VUxUTVBDdVAyNXR6cTBPSm9sQzliRUwxczlyS0lZaUphV1hwV2RsZnMzNmU2MHg4eEtKd3BVQmJoUmlXcDhxaVJyTXVCYkhPZWx3UlRkUEx3dmgxZEp5NEZiNFlUcm5vUWtDZ1ZsSlV0SkRBSjk4Q1BaVk1zc2pPN0I2Qmd6RUpKVWNSa2xsWDIrWk1MU3B1QitnQUxVVURWOWhERS9mRlNDL3lRQUdTOExJeXBrenVuRk9oMEpOY1NYaThkcUYiLCJtYWMiOiI0YWM4ZmM5OTA1NjA4NzE3MzgwZjYxZTBmZTc5NGM5MmQxNWY1MzQxN2M1MGQ4NWY5MmU1M2ZjMjkzZGU4Y2ViIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjB6Y2V5NnR3YitHUElvMzdYK3NvdGc9PSIsInZhbHVlIjoiQXZTbzF6dHZHcTE3N3NjSmFlcFNjRFEyUzhkdjJTUlhBTHE2N2UvUG10b0JLcmh5Ni8vcTFlY3J6V2lFb2hzMkF5TnRFclhPWXhzTDlrM0VzaUNqc01FMzNzeTRacktpNUtVT05LSnV4VDlZVjNHOHYyVHRFNkJDWURrTWVCMmMwZHBjSHJuVkx0bDdDVUNyMG1JclhtT0tYeDRtSVhzZTlHRlI4b2NrMlN4cmpYbkFqSzVXWmNQR0ZhaWZINjJaNGJ4S3BYZ1l2R2FsWEpZT2cwa3Z6Z0t0WnNGWWhDOWpXbUZnUmxqVGtqcmhkSk4zdWpYOXg0YnA4dXB5OTlPZDlyaGhGUm50bDRBa1pZWEs3bCtLMWViSU9kZ2dKMTBUQXJvbUNBSGc5aHJzV0hTWWEwOHl4bHI4bHBGZVdGSit1RDlBNGJrOE9PNUw2MTFwNmk4R2FyVmNzNEEybzJoNkYwb3Z1R2ZGdW04SUVWcXAyRU1DTnNSUzRERDV1M0J2MXdqbTdsaWErb2gzb083OGJlbFQvSkhpcDZScENMWm5vKy9WUHgyOEVvWnQ2WHFZV2g1bmphZ0lWbGc3NFRKWlBDR2xxaHNGQkI1LzNsbnl4QW41RjZmVnJDRWp4dDBQM29LK25rOXlRUDhFbEE5QW1WbWFON2kvZ2l6NjJBcnMiLCJtYWMiOiI4NjJhMTU5MWNiZTdmNTUwNGRiN2YxMmYzNjBjZDgwOTFhNmM0NmFiZTAxOWUxMTQyY2E2NmE4Nzc4MWZiZWY4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImpqZG5uZ3RYc0djNjdyWE1DYVk3UXc9PSIsInZhbHVlIjoiUERYcWN2SkZYSnM3WWtuYVRjUis5Z2Rqa0JjcFdXMzg5R2g5bWVwbWFIR254cGw2SVRQaER6RTFWS0pMTlZBN3BDbnlsbHlFbkVFckd1RHNFMUlhVnBMUnlVMXRqSzZaYkd0S3IrN2pRV3dFU1EzYVlzSks5aVptNTJLaWRFL01ULzZTNVlIS2RtOWlBN0tKUzNFTi93cGRNaHlUU2U3QXRYR1FlcllmcGRWMXdvMDUvaFhQRE5xOFRSRXBLSkhKODJyWWx4MVVHS2NteVBxTk5tTEc1V25uUU01Vm10YlZsNnA3N0p0ejFqUFNEL2FRalk2a1JHR1ZqU1g1Ui9pRmVaZWZHYkF4Q2lHcE5FMVVNaDlNc1BqUmVXbjlVQXFDTmlRdUZ0ZTZMcTQ3dm1MY0VvU2txdVJqWE1LTEk5TUR5VUxUTVBDdVAyNXR6cTBPSm9sQzliRUwxczlyS0lZaUphV1hwV2RsZnMzNmU2MHg4eEtKd3BVQmJoUmlXcDhxaVJyTXVCYkhPZWx3UlRkUEx3dmgxZEp5NEZiNFlUcm5vUWtDZ1ZsSlV0SkRBSjk4Q1BaVk1zc2pPN0I2Qmd6RUpKVWNSa2xsWDIrWk1MU3B1QitnQUxVVURWOWhERS9mRlNDL3lRQUdTOExJeXBrenVuRk9oMEpOY1NYaThkcUYiLCJtYWMiOiI0YWM4ZmM5OTA1NjA4NzE3MzgwZjYxZTBmZTc5NGM5MmQxNWY1MzQxN2M1MGQ4NWY5MmU1M2ZjMjkzZGU4Y2ViIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110232054\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1078785619 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6KdxnXlKJPlDQzTxeeZjfoOBvk5oAaMEaRdM3Ren</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078785619\", {\"maxDepth\":0})</script>\n"}}