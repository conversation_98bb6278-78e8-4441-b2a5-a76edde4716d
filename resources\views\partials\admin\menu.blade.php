    @php
        use App\Models\Utility;
        $setting = \App\Models\Utility::settings();
        $logo = \App\Models\Utility::get_file('uploads/logo');

        $company_logo = $setting['company_logo_dark'] ?? '';
        $company_logos = $setting['company_logo_light'] ?? '';
        $company_small_logo = $setting['company_small_logo'] ?? '';

        $emailTemplate = \App\Models\EmailTemplate::emailTemplateData();
        $lang = Auth::user()->lang;

        $user = \Auth::user();
        $userPlan = null;
        if ($user->type === 'company') {
            // Load the pricing plan relationship or find by ID if relationship fails
            $pricingPlan = $user->plan;
            if (is_numeric($pricingPlan)) {
                // If plan returns an ID instead of model, fetch the model
                $pricingPlan = \App\Models\PricingPlan::find($pricingPlan);
            }
            $userPlan = $pricingPlan;
        } elseif ($user->type === 'employee') {
            // For employees, get the company's plan to check module availability
            $companyUser = \App\Models\User::find($user->created_by);
            if ($companyUser && $companyUser->plan) {
                $pricingPlan = $companyUser->plan;
                if (is_numeric($pricingPlan)) {
                    $pricingPlan = \App\Models\PricingPlan::find($pricingPlan);
                }
                $userPlan = $pricingPlan;
            }
        } else {
            $userPlan = \App\Models\Plan::getPlan($user->show_dashboard());
        }
    @endphp

<!-- FontAwesome CDN for Icons - Backup if local fails -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<!-- Tabler Icons as additional fallback -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/icons-sprite.svg" />

<!-- Modern 3D Menu Icons CSS -->
<style>
    /* Reset any conflicting styles */
    .dash-micon,
    .dash-micon i,
    .dash-micon *,
    .dash-item .dash-micon,
    .dash-item .dash-micon i,
    .dash-item .dash-micon * {
        box-sizing: border-box;
    }

    /* 3D Icon Base Styles */
    .dash-micon {
        position: relative !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 50px !important;
        height: 50px !important;
        min-width: 50px !important;
        min-height: 50px !important;
        border-radius: 15px !important;
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.8) 50%, rgba(var(--theme-color-rgb), 0.6) 100%) !important;
        box-shadow:
            0 6px 20px rgba(var(--theme-color-rgb), 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.25),
            inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        margin-right: 16px !important;
        overflow: hidden !important;
        flex-shrink: 0 !important;
    }

    .dash-micon::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%) !important;
        border-radius: inherit !important;
        transition: opacity 0.3s ease !important;
        pointer-events: none !important;
        z-index: 1 !important;
    }

    /* Icon Styles - Force FontAwesome to display */
    .dash-micon i,
    .dash-micon .fas,
    .dash-micon .fab,
    .dash-micon .far,
    .dash-micon .fal,
    .dash-micon .ti {
        color: #ffffff !important;
        font-size: 22px !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
        transition: all 0.3s ease !important;
        font-weight: 900 !important;
        line-height: 1 !important;
        display: block !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Font Awesome 5 Free", "FontAwesome" !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-rendering: auto !important;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
        flex-shrink: 0 !important;
    }

    /* Specific FontAwesome classes */
    .dash-micon .fas {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
    }

    .dash-micon .fab {
        font-family: "Font Awesome 6 Brands" !important;
        font-weight: 400 !important;
    }

    .dash-micon .far {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 400 !important;
    }

    /* SVG Icon Styles - Flexbox Centering */
    .dash-micon svg {
        width: 22px !important;
        height: 22px !important;
        fill: none !important;
        stroke: #ffffff !important;
        stroke-width: 2.5 !important;
        stroke-linecap: round !important;
        stroke-linejoin: round !important;
        display: block !important;
        flex-shrink: 0 !important;
    }

    /* Active State */
    .dash-item.active .dash-micon,
    .dash-item.dash-trigger .dash-micon,
    .dash-item.active > .dash-link .dash-micon,
    .dash-item.dash-trigger > .dash-link .dash-micon {
        transform: scale(1.12) !important;
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.9) 50%, rgba(var(--theme-color-rgb), 0.7) 100%) !important;
        box-shadow:
            0 12px 35px rgba(var(--theme-color-rgb), 0.6),
            inset 0 3px 0 rgba(255, 255, 255, 0.35),
            inset 0 -3px 0 rgba(0, 0, 0, 0.25) !important;
    }

    .dash-item.active .dash-micon i,
    .dash-item.dash-trigger .dash-micon i,
    .dash-item.active .dash-micon .fas,
    .dash-item.dash-trigger .dash-micon .fas,
    .dash-item.active .dash-micon .fab,
    .dash-item.dash-trigger .dash-micon .fab,
    .dash-item.active .dash-micon .ti,
    .dash-item.dash-trigger .dash-micon .ti,
    .dash-item.active > .dash-link .dash-micon i,
    .dash-item.dash-trigger > .dash-link .dash-micon i {
        transform: scale(1.1) !important;
        color: #ffffff !important;
        text-shadow: 0 3px 8px rgba(0, 0, 0, 0.6) !important;
        font-weight: 600 !important;
        font-size: 26px !important;
    }

    /* Hover Effects */
    .dash-item:hover .dash-micon,
    .dash-item:hover > .dash-link .dash-micon {
        transform: translateY(-4px) scale(1.06) !important;
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.85) 50%, rgba(var(--theme-color-rgb), 0.65) 100%) !important;
        box-shadow:
            0 10px 30px rgba(var(--theme-color-rgb), 0.5),
            inset 0 3px 0 rgba(255, 255, 255, 0.35),
            inset 0 -3px 0 rgba(0, 0, 0, 0.2) !important;
    }

    .dash-item:hover .dash-micon::before {
        opacity: 1 !important;
    }

    .dash-item:hover .dash-micon i,
    .dash-item:hover .dash-micon .fas,
    .dash-item:hover .dash-micon .fab,
    .dash-item:hover .dash-micon .ti,
    .dash-item:hover > .dash-link .dash-micon i {
        transform: scale(1.05) !important;
        color: #ffffff !important;
        text-shadow: 0 3px 6px rgba(0, 0, 0, 0.5) !important;
        font-size: 25px !important;
    }

    /* Submenu Icons */
    .dash-submenu .dash-micon {
        width: 42px !important;
        height: 42px !important;
        min-width: 42px !important;
        min-height: 42px !important;
        background: linear-gradient(135deg, rgba(var(--theme-color-rgb), 0.6) 0%, rgba(var(--theme-color-rgb), 0.4) 50%, rgba(var(--theme-color-rgb), 0.3) 100%) !important;
        box-shadow:
            0 4px 12px rgba(var(--theme-color-rgb), 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.2),
            inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
        border-radius: 12px !important;
    }

    .dash-submenu .dash-micon i,
    .dash-submenu .dash-micon .fas,
    .dash-submenu .dash-micon .fab,
    .dash-submenu .dash-micon .ti {
        font-size: 20px !important;
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
    }

    .dash-submenu .dash-item.active .dash-micon {
        background: linear-gradient(135deg, var(--theme-color) 0%, rgba(var(--theme-color-rgb), 0.8) 100%) !important;
        transform: scale(1.1) !important;
        box-shadow:
            0 6px 18px rgba(var(--theme-color-rgb), 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 0 -2px 0 rgba(0, 0, 0, 0.2) !important;
    }

    .dash-submenu .dash-item.active .dash-micon i,
    .dash-submenu .dash-item.active .dash-micon .fas,
    .dash-submenu .dash-item.active .dash-micon .fab,
    .dash-submenu .dash-item.active .dash-micon .ti {
        color: #ffffff !important;
        font-size: 22px !important;
        font-weight: 600 !important;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .dash-micon {
            width: 46px !important;
            height: 46px !important;
            min-width: 46px !important;
            min-height: 46px !important;
        }

        .dash-micon i,
        .dash-micon .fas,
        .dash-micon .fab,
        .dash-micon .ti {
            font-size: 22px !important;
            color: #ffffff !important;
        }

        .dash-submenu .dash-micon {
            width: 38px !important;
            height: 38px !important;
            min-width: 38px !important;
            min-height: 38px !important;
        }

        .dash-submenu .dash-micon i,
        .dash-submenu .dash-micon .fas,
        .dash-submenu .dash-micon .fab,
        .dash-submenu .dash-micon .ti {
            font-size: 18px !important;
            color: #ffffff !important;
        }
    }

    @media (max-width: 768px) {
        .dash-micon {
            width: 44px !important;
            height: 44px !important;
            min-width: 44px !important;
            min-height: 44px !important;
            margin-right: 12px !important;
        }

        .dash-micon i,
        .dash-micon .fas,
        .dash-micon .fab,
        .dash-micon .ti {
            font-size: 20px !important;
            color: #ffffff !important;
        }

        .dash-submenu .dash-micon {
            width: 36px !important;
            height: 36px !important;
            min-width: 36px !important;
            min-height: 36px !important;
        }

        .dash-submenu .dash-micon i,
        .dash-submenu .dash-micon .fas,
        .dash-submenu .dash-micon .fab,
        .dash-submenu .dash-micon .ti {
            font-size: 16px !important;
            color: #ffffff !important;
        }
    }

    @media (max-width: 480px) {
        .dash-micon {
            width: 40px !important;
            height: 40px !important;
            min-width: 40px !important;
            min-height: 40px !important;
            margin-right: 10px !important;
        }

        .dash-micon i,
        .dash-micon .fas,
        .dash-micon .fab,
        .dash-micon .ti {
            font-size: 18px !important;
            color: #ffffff !important;
        }

        .dash-submenu .dash-micon {
            width: 32px !important;
            height: 32px !important;
            min-width: 32px !important;
            min-height: 32px !important;
        }

        .dash-submenu .dash-micon i,
        .dash-submenu .dash-micon .fas,
        .dash-submenu .dash-micon .fab,
        .dash-submenu .dash-micon .ti {
            font-size: 14px !important;
            color: #ffffff !important;
        }
    }

    /* Animation for menu items */
    .dash-item {
        transition: all 0.3s ease;
    }

    .dash-link {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    /* .dash-item:hover .dash-link {
        background: rgba(var(--theme-color-rgb), 0.1);
    } */

    /* .dash-item.active .dash-link {
        background: rgba(var(--theme-color-rgb), 0.15);
    } */

    /* Menu text styling */
    .dash-mtext {
        font-weight: 500;
        color: #374151;
        transition: color 0.3s ease;
    }

    /* Top-level menu items with 3D icons - white text on active/hover */
    .dash-navbar > .dash-item.dash-hasmenu.active .dash-mtext {
        color: #ffffff;
        font-weight: 600;
    }

    .dash-navbar > .dash-item.dash-hasmenu:hover .dash-mtext {
        color: #ffffff;
    }

    /* Regular menu items - keep theme colors */
    .dash-navbar > .dash-item:not(.dash-hasmenu).active .dash-mtext {
        color: var(--theme-color);
        font-weight: 600;
    }

    .dash-navbar > .dash-item:not(.dash-hasmenu):hover .dash-mtext {
        color: var(--theme-color);
    }

    /* Submenu items - keep original colors */
    .dash-submenu .dash-item.active .dash-mtext {
        color: var(--theme-color);
        font-weight: 600;
    }

    .dash-submenu .dash-item:hover .dash-mtext {
        color: var(--theme-color);
    }

    /* Arrow styling */
    .dash-arrow {
        margin-left: auto;
        transition: transform 0.3s ease;
    }

    .dash-item.dash-trigger .dash-arrow {
        transform: rotate(90deg);
    }

    /* Force icon visibility and override any conflicting styles */
    .dash-navbar .dash-micon,
    .dash-submenu .dash-micon,
    .navbar-content .dash-micon {
        display: inline-flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .dash-navbar .dash-micon i,
    .dash-navbar .dash-micon .fas,
    .dash-navbar .dash-micon .fab,
    .dash-navbar .dash-micon .ti,
    .dash-submenu .dash-micon i,
    .dash-submenu .dash-micon .fas,
    .dash-submenu .dash-micon .fab,
    .dash-submenu .dash-micon .ti,
    .navbar-content .dash-micon i,
    .navbar-content .dash-micon .fas,
    .navbar-content .dash-micon .fab,
    .navbar-content .dash-micon .ti {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: #ffffff !important;
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", inherit !important;
        font-style: normal !important;
        font-variant: normal !important;
        text-rendering: auto !important;
        -webkit-font-smoothing: antialiased !important;
    }

    /* Ensure FontAwesome icons are properly loaded */
    .fas, .fab, .far, .fal {
        font-family: "Font Awesome 6 Free" !important;
        font-weight: 900 !important;
    }

    .fab {
        font-family: "Font Awesome 6 Brands" !important;
        font-weight: 400 !important;
    }

    /* Additional icon fixes */
    .dash-link {
        display: flex !important;
        align-items: center !important;
        text-decoration: none !important;
    }

    .dash-mtext {
        flex: 1 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* Hide FontAwesome icons when SVG is present */
    .dash-micon.has-svg i {
        display: none !important;
    }

    /* Fallback icon styles */
    .dash-micon .fallback-icon {
        font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
        font-size: 20px !important;
        line-height: 1 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
    }

    /* Theme color support for custom colors */
    body.custom-color .dash-micon {
        background: linear-gradient(135deg, var(--color-customColor) 0%, rgba(var(--color-customColor-rgb), 0.8) 50%, rgba(var(--color-customColor-rgb), 0.6) 100%) !important;
        box-shadow:
            0 6px 20px rgba(var(--color-customColor-rgb), 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.25),
            inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
    }

    body.custom-color .dash-item.active .dash-micon,
    body.custom-color .dash-item.dash-trigger .dash-micon,
    body.custom-color .dash-item.active > .dash-link .dash-micon,
    body.custom-color .dash-item.dash-trigger > .dash-link .dash-micon {
        background: linear-gradient(135deg, var(--color-customColor) 0%, rgba(var(--color-customColor-rgb), 0.9) 50%, rgba(var(--color-customColor-rgb), 0.7) 100%) !important;
        box-shadow:
            0 12px 35px rgba(var(--color-customColor-rgb), 0.6),
            inset 0 3px 0 rgba(255, 255, 255, 0.35),
            inset 0 -3px 0 rgba(0, 0, 0, 0.25) !important;
    }

    body.custom-color .dash-item:hover .dash-micon,
    body.custom-color .dash-item:hover > .dash-link .dash-micon {
        background: linear-gradient(135deg, var(--color-customColor) 0%, rgba(var(--color-customColor-rgb), 0.85) 50%, rgba(var(--color-customColor-rgb), 0.65) 100%) !important;
        box-shadow:
            0 10px 30px rgba(var(--color-customColor-rgb), 0.5),
            inset 0 3px 0 rgba(255, 255, 255, 0.35),
            inset 0 -3px 0 rgba(0, 0, 0, 0.2) !important;
    }

    body.custom-color .dash-submenu .dash-micon {
        background: linear-gradient(135deg, rgba(var(--color-customColor-rgb), 0.6) 0%, rgba(var(--color-customColor-rgb), 0.4) 50%, rgba(var(--color-customColor-rgb), 0.3) 100%) !important;
        box-shadow:
            0 4px 12px rgba(var(--color-customColor-rgb), 0.3),
            inset 0 2px 0 rgba(255, 255, 255, 0.2),
            inset 0 -2px 0 rgba(0, 0, 0, 0.15) !important;
    }

    body.custom-color .dash-submenu .dash-item.active .dash-micon {
        background: linear-gradient(135deg, var(--color-customColor) 0%, rgba(var(--color-customColor-rgb), 0.8) 100%) !important;
        box-shadow:
            0 6px 18px rgba(var(--color-customColor-rgb), 0.5),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 0 -2px 0 rgba(0, 0, 0, 0.2) !important;
    }

    body.custom-color .dash-item:hover .dash-link {
        background: rgba(var(--color-customColor-rgb), 0.1);
    }

    body.custom-color .dash-item.active .dash-link {
        background: rgba(var(--color-customColor-rgb), 0.15);
    }

    /* Custom color theme - top-level menu items with 3D icons */
    body.custom-color .dash-navbar > .dash-item.dash-hasmenu.active .dash-mtext {
        color: #ffffff;
    }

    body.custom-color .dash-navbar > .dash-item.dash-hasmenu:hover .dash-mtext {
        color: #ffffff;
    }

    /* Custom color theme - regular menu items */
    body.custom-color .dash-navbar > .dash-item:not(.dash-hasmenu).active .dash-mtext {
        color: var(--color-customColor);
    }

    body.custom-color .dash-navbar > .dash-item:not(.dash-hasmenu):hover .dash-mtext {
        color: var(--color-customColor);
    }

    /* Custom color theme - submenu items */
    body.custom-color .dash-submenu .dash-item.active .dash-mtext {
        color: var(--color-customColor);
    }

    body.custom-color .dash-submenu .dash-item:hover .dash-mtext {
        color: var(--color-customColor);
    }
</style>

<!-- JavaScript to ensure icons display properly -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // SVG Icon definitions
    const svgIcons = {
        'fa-home': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/></svg>',
        'fa-chart-pie': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.21 15.89A10 10 0 1 1 8 2.83"/><path d="m22 12-10-10v10z"/></svg>',
        'fa-calculator': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="2" width="16" height="20" rx="2"/><line x1="8" y1="6" x2="16" y2="6"/><line x1="16" y1="10" x2="16" y2="10"/><line x1="12" y1="10" x2="12" y2="10"/><line x1="8" y1="10" x2="8" y2="10"/><line x1="16" y1="14" x2="16" y2="14"/><line x1="12" y1="14" x2="12" y2="14"/><line x1="8" y1="14" x2="8" y2="14"/><line x1="16" y1="18" x2="16" y2="18"/><line x1="12" y1="18" x2="12" y2="18"/><line x1="8" y1="18" x2="8" y2="18"/></svg>',
        'fa-coins': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="8" cy="8" r="6"/><path d="M18.09 10.37A6 6 0 1 1 10.34 18"/><path d="M7 6h1v4"/><path d="m16.71 13.88.7.71-2.82 2.82"/></svg>',
        'fa-users': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0-3 3m3-3V8m0 10h10"/></svg>',
        'fa-rocket': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"/><path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"/><path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"/><path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"/></svg>',
        'fa-tasks': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 11H4a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h5m0-7v7m0-7a2 2 0 0 1 2-2h7a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2m0 0H4a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h5m6-10V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h5"/></svg>',
        'fa-calendar': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"/><line x1="16" y1="2" x2="16" y2="6"/><line x1="8" y1="2" x2="8" y2="6"/><line x1="3" y1="10" x2="21" y2="10"/></svg>',
        'fa-user-friends': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="m22 21-3-3m0 0-3 3m3-3V8m0 10h10"/></svg>',
        'fa-box': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/><path d="m3.3 7 8.7 5 8.7-5"/><path d="M12 22V12"/></svg>',
        'fa-life-ring': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="4"/><line x1="4.93" y1="4.93" x2="9.17" y2="9.17"/><line x1="14.83" y1="14.83" x2="19.07" y2="19.07"/><line x1="14.83" y1="9.17" x2="19.07" y2="4.93"/><line x1="14.83" y1="9.17" x2="19.07" y2="4.93"/><line x1="4.93" y1="19.07" x2="9.17" y2="14.83"/></svg>',
        'fa-whatsapp': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"/><path d="M9 10a3 3 0 0 1 6 0c0 2-3 3-3 3"/><path d="M9 17h.01"/></svg>',
        'fa-file': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14,2 14,8 20,8"/></svg>',
        'fa-bullhorn': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 11 18-5v12L3 14v-3z"/><path d="M11.6 16.8a3 3 0 1 1-5.8-1.6"/></svg>',
        'fa-shopping-cart': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="8" cy="21" r="1"/><circle cx="19" cy="21" r="1"/><path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/></svg>',
        'fa-robot': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="10" rx="2" ry="2"/><circle cx="12" cy="5" r="2"/><path d="M12 7v4"/><line x1="8" y1="16" x2="8" y2="16"/><line x1="16" y1="16" x2="16" y2="16"/></svg>',
        'fa-inbox': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22,12 18,12 15,21 9,21 6,12 2,12"/><path d="M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"/></svg>',
        'fa-sliders': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="4" y1="21" x2="4" y2="14"/><line x1="4" y1="10" x2="4" y2="3"/><line x1="12" y1="21" x2="12" y2="12"/><line x1="12" y1="8" x2="12" y2="3"/><line x1="20" y1="21" x2="20" y2="16"/><line x1="20" y1="12" x2="20" y2="3"/><line x1="1" y1="14" x2="7" y2="14"/><line x1="9" y1="8" x2="15" y2="8"/><line x1="17" y1="16" x2="23" y2="16"/></svg>',
        'fa-tag': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/><line x1="7" y1="7" x2="7.01" y2="7"/></svg>',
        'fa-building': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/><path d="M6 12h4"/><path d="M6 16h4"/><path d="M16 12h2"/><path d="M16 16h2"/><path d="M16 20h2"/><path d="M6 20h4"/><path d="M2 22h20"/></svg>',
        'fa-user-plus': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="8.5" cy="7" r="4"/><line x1="20" y1="8" x2="20" y2="14"/><line x1="23" y1="11" x2="17" y2="11"/></svg>',
        'fa-dollar-sign': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="1" x2="12" y2="23"/><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/></svg>',
        'fa-cogs': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 16.24l-4.24 4.24m12.02-12.02l-4.24 4.24M7.76 7.76L3.52 3.52"/></svg>',
        'default': '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><rect x="9" y="9" width="6" height="6"/></svg>'
    };

    // Function to check and fix missing icons
    function fixMissingIcons() {
        const iconContainers = document.querySelectorAll('.dash-micon');

        iconContainers.forEach(container => {
            const icon = container.querySelector('i');
            if (icon && !container.querySelector('svg')) {
                // Check if FontAwesome is working
                const computedStyle = window.getComputedStyle(icon, '::before');
                const content = computedStyle.getPropertyValue('content');

                // If no content or content is "none", use SVG fallback
                if (!content || content === 'none' || content === '""' || content === 'normal') {
                    const iconClass = icon.className;
                    let svgKey = 'default';

                    // Determine which SVG to use based on FontAwesome class
                    if (iconClass.includes('fa-home')) svgKey = 'fa-home';
                    else if (iconClass.includes('fa-chart')) svgKey = 'fa-chart-pie';
                    else if (iconClass.includes('fa-calculator')) svgKey = 'fa-calculator';
                    else if (iconClass.includes('fa-coins')) svgKey = 'fa-coins';
                    else if (iconClass.includes('fa-users')) svgKey = 'fa-users';
                    else if (iconClass.includes('fa-rocket')) svgKey = 'fa-rocket';
                    else if (iconClass.includes('fa-tasks')) svgKey = 'fa-tasks';
                    else if (iconClass.includes('fa-calendar')) svgKey = 'fa-calendar';
                    else if (iconClass.includes('fa-user-friends')) svgKey = 'fa-user-friends';
                    else if (iconClass.includes('fa-box')) svgKey = 'fa-box';
                    else if (iconClass.includes('fa-life-ring')) svgKey = 'fa-life-ring';
                    else if (iconClass.includes('fa-whatsapp')) svgKey = 'fa-whatsapp';
                    else if (iconClass.includes('fa-file')) svgKey = 'fa-file';
                    else if (iconClass.includes('fa-bullhorn')) svgKey = 'fa-bullhorn';
                    else if (iconClass.includes('fa-shopping')) svgKey = 'fa-shopping-cart';
                    else if (iconClass.includes('fa-robot')) svgKey = 'fa-robot';
                    else if (iconClass.includes('fa-inbox')) svgKey = 'fa-inbox';
                    else if (iconClass.includes('fa-sliders')) svgKey = 'fa-sliders';
                    else if (iconClass.includes('fa-tag')) svgKey = 'fa-tag';
                    else if (iconClass.includes('fa-building')) svgKey = 'fa-building';
                    else if (iconClass.includes('fa-user-plus')) svgKey = 'fa-user-plus';
                    else if (iconClass.includes('fa-dollar-sign')) svgKey = 'fa-dollar-sign';
                    else if (iconClass.includes('fa-cogs')) svgKey = 'fa-cogs';

                    // Hide the original icon and add SVG
                    icon.style.display = 'none';
                    container.insertAdjacentHTML('beforeend', svgIcons[svgKey]);
                    container.classList.add('has-svg');

                    // Style the SVG - let flexbox handle centering
                    const svg = container.querySelector('svg');
                    if (svg) {
                        svg.style.width = '22px';
                        svg.style.height = '22px';
                        svg.style.stroke = '#ffffff';
                        svg.style.fill = 'none';
                        svg.style.strokeWidth = '2.5';
                        svg.style.strokeLinecap = 'round';
                        svg.style.strokeLinejoin = 'round';
                        svg.style.display = 'block';
                        svg.style.flexShrink = '0';
                    }
                }
            }
        });
    }

    // Run immediately
    fixMissingIcons();

    // Run again after FontAwesome should have loaded
    setTimeout(fixMissingIcons, 100);
    setTimeout(fixMissingIcons, 500);
    setTimeout(fixMissingIcons, 1000);

    // Also run when fonts are loaded
    if (document.fonts) {
        document.fonts.ready.then(fixMissingIcons);
    }

    // Function to convert hex color to RGB
    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Function to update custom color RGB values
    function updateCustomColorRgb() {
        const customColor = getComputedStyle(document.documentElement).getPropertyValue('--color-customColor');
        if (customColor && customColor.trim() !== '') {
            const rgb = hexToRgb(customColor.trim());
            if (rgb) {
                document.documentElement.style.setProperty('--color-customColor-rgb', `${rgb.r}, ${rgb.g}, ${rgb.b}`);
            }
        }
    }

    // Update RGB values when custom color changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                updateCustomColorRgb();
            }
        });
    });

    // Start observing
    observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['style']
    });

    // Initial update
    updateCustomColorRgb();
});
</script>

@if (isset($setting['cust_theme_bg']) && $setting['cust_theme_bg'] == 'on')
    <nav class="dash-sidebar light-sidebar transprent-bg">
    @else
        <nav class="dash-sidebar light-sidebar ">
@endif
<div class="navbar-wrapper">
    <div class="m-header main-logo">
        <a href="#" class="b-brand">

            @if ($setting['cust_darklayout'] && $setting['cust_darklayout'] == 'on')
                <img src="{{ $logo . '/' . (isset($company_logos) && !empty($company_logos) ? $company_logos : 'logo-light.png') . '?' . time() }}"
                    alt="{{ config('app.name', 'ERPGo-SaaS') }}" class="logo logo-lg">
            @else
                <img src="{{ $logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png') . '?' . time() }}"
                    alt="{{ config('app.name', 'ERPGo-SaaS') }}" class="logo logo-lg">
            @endif

        </a>

    </div>
    <div class="navbar-content">
        @if (\Auth::user()->type != 'client')
            <ul class="dash-navbar">
                @if(Request::segment(1) != 'settings')
                <!--------------------- Start Dashboard ----------------------------------->
                @if (Gate::check('show hrm dashboard') ||
                        Gate::check('show project dashboard') ||
                        Gate::check('show account dashboard') ||
                        Gate::check('show crm dashboard') ||
                        Gate::check('show pos dashboard'))
                    <li
                        class="dash-item dash-hasmenu
                                {{ Request::segment(1) == null ||
                                Request::segment(1) == 'account-dashboard' ||
                                Request::segment(1) == 'hrm-dashboard' ||
                                Request::segment(1) == 'crm-dashboard' ||
                                Request::segment(1) == 'project-dashboard' ||
                                Request::segment(1) == 'account-statement-report' ||
                                Request::segment(1) == 'invoice-summary' ||
                                Request::segment(1) == 'sales' ||
                                Request::segment(1) == 'receivables' ||
                                Request::segment(1) == 'payables' ||
                                Request::segment(1) == 'bill-summary' ||
                                Request::segment(1) == 'product-stock-report' ||
                                Request::segment(1) == 'transaction' ||
                                Request::segment(1) == 'income-summary' ||
                                Request::segment(1) == 'expense-summary' ||
                                Request::segment(1) == 'income-vs-expense-summary' ||
                                Request::segment(1) == 'tax-summary' ||
                                Request::segment(1) == 'income report' ||
                                Request::segment(1) == 'report' ||
                                Request::segment(1) == 'reports-monthly-cashflow' ||
                                Request::segment(1) == 'reports-quarterly-cashflow' ||
                                Request::segment(1) == 'reports-payroll' ||
                                Request::segment(1) == 'report-leave' ||
                                Request::segment(1) == 'reports-monthly-attendance' ||
                                Request::segment(1) == 'reports-lead' ||
                                Request::segment(1) == 'reports-deal' ||
                                Request::segment(1) == 'pos-dashboard' ||
                                Request::segment(1) == 'reports-warehouse' ||
                                Request::segment(1) == 'reports-daily-purchase' ||
                                Request::segment(1) == 'reports-monthly-purchase' ||
                                Request::segment(1) == 'reports-daily-pos' ||
                                Request::segment(1) == 'reports-monthly-pos' ||
                                Request::segment(1) == 'reports-pos-vs-purchase'
                                    ? 'active dash-trigger'
                                    : '' }}">
                        <a href="{{ route('dashboard') }}" class="dash-link ">
                            <span class="dash-micon">
                                <i class="fas fa-home"></i>
                            </span>
                            <span class="dash-mtext">{{ __('Dashboard') }}</span>
                            <span class="dash-arrow"></span></a>
                        <ul class="dash-submenu">
                            @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1)) && Gate::check('show account dashboard'))
                                <!-- <li
                                    class="dash-item {{ Request::segment(1) == null || Request::segment(1) == 'account-dashboard' ? ' active' : '' }}">
                                    <a class="dash-link"
                                        href="{{ route('dashboard') }}">{{ __(' Overview') }}
                                    </a>
                                </li> -->
                            @endif
                            @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1)))
                                @can('show crm dashboard')
                                    <!-- <li
                                        class="dash-item {{ Request::segment(1) == 'crm-dashboard' ? ' active' : '' }}">
                                        <a class="dash-link" href="#">{{ __('CRM') }}</a>
                                    </li> -->
                                @endcan
                            @endif
                        </ul>
                    </li>
                @endif
                <!--------------------- End Dashboard ----------------------------------->

                <!--------------------- Start Reports | Analytics ----------------------------------->
                @if (
                    (!empty($userPlan) && (
                        ($user->type === 'company' && ($userPlan->hasModule('account') || $userPlan->hasModule('crm')))
                        || ($user->type === 'employee' && ($userPlan->hasModule('account') || $userPlan->hasModule('crm')))
                        || ($user->type !== 'company' && $user->type !== 'employee' && ($userPlan->account == 1 || $userPlan->crm == 1))
                    ))
                )
                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'account-report' || Request::segment(1) == 'crm-report' ? 'active dash-trigger' : '' }}">
                        <a href="#" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-chart-pie"></i></span>
                            <span class="dash-mtext">{{ __('Reports | Analytics') }}</span>
                            <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                        </a>
                        <ul class="dash-submenu">
                            @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1)))
                                <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'account-report' || Request::segment(1) == 'report' || Request::segment(1) == 'reports-monthly-cashflow' || Request::segment(1) == 'reports-quarterly-cashflow' ? 'active dash-trigger' : '' }}">
                                    <a class="dash-link" href="#">{{ __('Accounts') }}<span class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        @can('statement report')
                                            <li class="dash-item {{ Request::route()->getName() == 'report.account.statement' ? ' active' : '' }}">
                                                <a class="dash-link" href="{{ route('report.account.statement') }}">{{ __('Account Statement') }}</a>
                                            </li>
                                        @endcan
                                        @can('invoice report')
                                            <li class="dash-item {{ Request::route()->getName() == 'report.invoice.summary' ? ' active' : '' }}">
                                                <a class="dash-link" href="{{ route('report.invoice.summary') }}">{{ __('Invoice Summary') }}</a>
                                            </li>
                                        @endcan
                                        <li class="dash-item {{ Request::route()->getName() == 'report.sales' ? ' active' : '' }}">
                                            <a class="dash-link" href="{{ route('report.sales') }}">{{ __('Sales Report') }}</a>
                                        </li>
                                        <li class="dash-item {{ Request::route()->getName() == 'report.receivables' ? ' active' : '' }}">
                                            <a class="dash-link" href="{{ route('report.receivables') }}">{{ __('Receivables') }}</a>
                                        </li>
                                        <li class="dash-item {{ Request::route()->getName() == 'report.payables' ? ' active' : '' }}">
                                            <a class="dash-link" href="{{ route('report.payables') }}">{{ __('Payables') }}</a>
                                        </li>
                                        @can('bill report')
                                            <li class="dash-item {{ Request::route()->getName() == 'report.bill.summary' ? ' active' : '' }}">
                                                <a class="dash-link" href="{{ route('report.bill.summary') }}">{{ __('Bill Summary') }}</a>
                                            </li>
                                        @endcan
                                        @can('stock report')
                                            <li class="dash-item {{ Request::route()->getName() == 'report.product.stock.report' ? ' active' : '' }}">
                                                <a href="{{ route('report.product.stock.report') }}" class="dash-link">{{ __('Product Stock') }}</a>
                                            </li>
                                        @endcan
                                        @can('loss & profit report')
                                            <li class="dash-item {{ request()->is('reports-monthly-cashflow') || request()->is('reports-quarterly-cashflow') ? 'active' : '' }}">
                                                <a class="dash-link" href="{{ route('report.monthly.cashflow') }}">{{ __('Cash Flow') }}</a>
                                            </li>
                                        @endcan
                                        @can('manage transaction')
                                            <li class="dash-item {{ Request::route()->getName() == 'transaction.index' || Request::route()->getName() == 'transfer.create' || Request::route()->getName() == 'transaction.edit' ? ' active' : '' }}">
                                                <a class="dash-link" href="{{ route('transaction.index') }}">{{ __('Transaction') }}</a>
                                            </li>
                                        @endcan
                                        @can('income report')
                                            <li class="dash-item {{ Request::route()->getName() == 'report.income.summary' ? ' active' : '' }}">
                                                <a class="dash-link" href="{{ route('report.income.summary') }}">{{ __('Income Summary') }}</a>
                                            </li>
                                        @endcan
                                        @can('expense report')
                                            <li class="dash-item {{ Request::route()->getName() == 'report.expense.summary' ? ' active' : '' }}">
                                                <a class="dash-link" href="{{ route('report.expense.summary') }}">{{ __('Expense Summary') }}</a>
                                            </li>
                                        @endcan
                                        @can('income vs expense report')
                                            <li class="dash-item {{ Request::route()->getName() == 'report.income.vs.expense.summary' ? ' active' : '' }}">
                                                <a class="dash-link" href="{{ route('report.income.vs.expense.summary') }}">{{ __('Income VS Expense') }}</a>
                                            </li>
                                        @endcan
                                        @can('tax report')
                                            <li class="dash-item {{ Request::route()->getName() == 'report.tax.summary' ? ' active' : '' }}">
                                                <a class="dash-link" href="{{ route('report.tax.summary') }}">{{ __('Tax Summary') }}</a>
                                            </li>
                                        @endcan
                                    </ul>
                                </li>
                            @endif
                            @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1)))
                                <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'crm-report' || Request::segment(1) == 'reports-lead' || Request::segment(1) == 'reports-deal' ? 'active dash-trigger' : '' }}">
                                    <a class="dash-link" href="#">{{ __('CRM') }}<span class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                    <ul class="dash-submenu">
                                        <li class="dash-item {{ request()->is('reports-lead') ? 'active' : '' }}">
                                            <a class="dash-link" href="{{ route('report.lead') }}">{{ __('Lead') }}</a>
                                        </li>
                                        <li class="dash-item {{ request()->is('reports-deal') ? 'active' : '' }}">
                                            <a class="dash-link" href="{{ route('report.deal') }}">{{ __('Deal') }}</a>
                                        </li>
                                    </ul>
                                </li>
                            @endif
                            <!-- Projects Menu Section -->
                            <li class="dash-item dash-hasmenu {{ in_array(Request::segment(1), ['team-performance', 'task-analysis', 'performance-analysis']) ? 'active dash-trigger' : '' }}">
                                <a class="dash-link" href="#">
                                   {{ __('Projects') }}
                                    <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                                </a>
                                <ul class="dash-submenu">
                                    <li class="dash-item {{ Request::segment(1) == 'team-performance' ? 'active' : '' }}">
                                        <a class="dash-link" href="{{ route('projects.team-performance') }}">{{ __('Team Performance') }}</a>
                                    </li>
                                    <li class="dash-item {{ Request::segment(1) == 'task-analysis' ? 'active' : '' }}">
                                        <a class="dash-link" href="{{ route('projects.task-analysis') }}">{{ __('Task Analysis') }}</a>
                                    </li>
                                    <li class="dash-item {{ Request::segment(1) == 'performance-analysis' ? 'active' : '' }}">
                                        <a class="dash-link" href="{{ route('projects.performance-analysis') }}">{{ __('Performance Analysis') }}</a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                @endif
                <!--------------------- End Reports | Analytics ----------------------------------->

                <!--------------------- Start HRM ----------------------------------->

                {{-- HRM System section hidden from company panel --}}
                {{-- @if (!empty($userPlan) && $userPlan->hrm == 1)
                    @if (Gate::check('manage employee') ||
                        Gate::check('manage set salary') || Gate::check('manage pay slip') ||
                        Gate::check('manage leave') || Gate::check('manage attendance') ||
                        Gate::check('create attendance') || Gate::check('manage indicator') ||
                        Gate::check('manage appraisal') || Gate::check('manage goal tracking') ||
                        Gate::check('manage training') || Gate::check('manage trainer') ||
                        Gate::check('manage job') || Gate::check('create job') ||
                        Gate::check('manage job application') || Gate::check('manage custom question') ||
                        Gate::check('manage job onBoard') || Gate::check('show interview schedule') ||
                        Gate::check('show career') || Gate::check('manage award') ||
                        Gate::check('manage transfer') || Gate::check('manage resignation') ||
                        Gate::check('manage travel') || Gate::check('manage promotion') ||
                        Gate::check('manage complaint') || Gate::check('manage warning') ||
                        Gate::check('manage termination') || Gate::check('manage announcement') ||
                        Gate::check('manage holiday') || Gate::check('manage event') ||
                        Gate::check('manage meeting') || Gate::check('manage assets') ||
                        Gate::check('manage document') || Gate::check('manage company policy') ||
                        Gate::check('manage branch') || Gate::check('manage department') ||
                        Gate::check('manage designation') || Gate::check('manage leave type') ||
                        Gate::check('manage document type') || Gate::check('manage payslip type') ||
                        Gate::check('manage allowance option') || Gate::check('manage loan option') ||
                        Gate::check('manage deduction option') || Gate::check('manage goal type') ||
                        Gate::check('manage training type') || Gate::check('manage award type') ||
                        Gate::check('manage termination type') || Gate::check('manage job category') ||
                        Gate::check('manage job stage') || Gate::check('manage performance type') ||
                        Gate::check('manage competencies'))

                        <li
                            class="dash-item dash-hasmenu {{ Request::segment(1) == 'holiday-calender' ||
                            Request::segment(1) == 'leavetype' || Request::segment(1) == 'leave' ||
                            Request::segment(1) == 'attendanceemployee' || Request::segment(1) == 'bulkattendance' ||
                            Request::segment(1) == 'indicator' || Request::segment(1) == 'appraisal' ||
                            Request::segment(1) == 'goaltracking' || Request::segment(1) == 'trainer' ||
                            Request::segment(1) == 'event' || Request::segment(1) == 'meeting' ||
                            Request::segment(1) == 'account-assets' || Request::segment(1) == 'leavetype' ||
                            Request::segment(1) == 'meeting-calender' || Request::segment(1) == 'document-upload' ||
                            Request::segment(1) == 'document' || Request::segment(1) == 'performanceType' ||
                            Request::segment(1) == 'branch' || Request::segment(1) == 'department' ||
                            Request::segment(1) == 'designation' || Request::segment(1) == 'employee' ||
                            Request::segment(1) == 'leave_requests' || Request::segment(1) == 'holidays' ||
                            Request::segment(1) == 'policies' || Request::segment(1) == 'leave_calender' ||
                            Request::segment(1) == 'award' || Request::segment(1) == 'transfer' ||
                            Request::segment(1) == 'resignation' || Request::segment(1) == 'training' ||
                            Request::segment(1) == 'travel' || Request::segment(1) == 'promotion' ||
                            Request::segment(1) == 'complaint' || Request::segment(1) == 'warning' ||
                            Request::segment(1) == 'termination' || Request::segment(1) == 'announcement' ||
                            Request::segment(1) == 'job' || Request::segment(1) == 'job-application' ||
                            Request::segment(1) == 'candidates-job-applications' || Request::segment(1) == 'job-onboard' ||
                            Request::segment(1) == 'custom-question' || Request::segment(1) == 'interview-schedule' ||
                            Request::segment(1) == 'career' || Request::segment(1) == 'holiday' ||
                            Request::segment(1) == 'setsalary' || Request::segment(1) == 'payslip' ||
                            Request::segment(1) == 'paysliptype' || Request::segment(1) == 'company-policy' ||
                            Request::segment(1) == 'job-stage' || Request::segment(1) == 'job-category' ||
                            Request::segment(1) == 'terminationtype' || Request::segment(1) == 'awardtype' ||
                            Request::segment(1) == 'trainingtype' || Request::segment(1) == 'goaltype' ||
                            Request::segment(1) == 'allowanceoption' || Request::segment(1) == 'competencies' ||
                            Request::segment(1) == 'loanoption' || Request::segment(1) == 'deductionoption'
                                ? 'active dash-trigger'
                                : '' }}">
                            <a href="#!" class="dash-link ">
                                <span class="dash-micon">
                                    <i class="ti ti-user"></i>
                                </span>
                                <span class="dash-mtext">
                                    {{ __('HRM System') }}
                                </span>
                                <span class="dash-arrow">
                                    <i data-feather="chevron-right"></i>
                                </span>
                            </a>
                            <ul class="dash-submenu">
                                @can('manage employee')
                                    <li
                                        class="dash-item  {{ Request::segment(1) == 'employee' ? 'active dash-trigger' : '' }}   ">
                                        @if (\Auth::user()->type == 'Employee')
                                            @php
                                                $employee = App\Models\Employee::where(
                                                    'user_id',
                                                    \Auth::user()->id,
                                                )->first();
                                            @endphp
                                            <a class="dash-link"
                                                href="{{ route('employee.show', \Illuminate\Support\Facades\Crypt::encrypt($employee->id)) }}">{{ __('Employee') }}</a>
                                        @else
                                            <a href="{{ route('employee.index') }}" class="dash-link">
                                                {{ __('Employee Setup') }}
                                            </a>
                                        @endif
                                    </li>
                                @endcan

                                @if (Gate::check('manage set salary') || Gate::check('manage pay slip'))
                                    <li
                                        class="dash-item dash-hasmenu  {{ Request::segment(1) == 'setsalary' || Request::segment(1) == 'payslip' ? 'active dash-trigger' : '' }}">
                                        <a class="dash-link" href="#">{{ __('Payroll Setup') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            @can('manage set salary')
                                                <li class="dash-item {{ request()->is('setsalary*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('setsalary.index') }}">{{ __('Set salary') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage pay slip')
                                                <li class="dash-item {{ request()->is('payslip*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('payslip.index') }}">{{ __('Payslip') }}</a>
                                                </li>
                                            @endcan
                                        </ul>
                                    </li>
                                @endif

                                @if (Gate::check('manage leave') || Gate::check('manage attendance'))
                                    <li
                                        class="dash-item dash-hasmenu  {{ Request::segment(1) == 'leave' || Request::segment(1) == 'attendanceemployee' ? 'active dash-trigger' : '' }}">
                                        <a class="dash-link" href="#">{{ __('Leave Management Setup') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            @can('manage leave')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'leave.index' ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('leave.index') }}">{{ __('Manage Leave') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage attendance')
                                                <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'attendanceemployee' ? 'active dash-trigger' : '' }}"
                                                    href="#navbar-attendance" data-toggle="collapse" role="button"
                                                    aria-expanded="{{ Request::segment(1) == 'attendanceemployee' ? 'true' : 'false' }}">
                                                    <a class="dash-link" href="#">{{ __('Attendance') }}<span
                                                            class="dash-arrow"><i
                                                                data-feather="chevron-right"></i></span></a>
                                                    <ul class="dash-submenu">
                                                        <li
                                                            class="dash-item {{ Request::route()->getName() == 'attendanceemployee.index' ? 'active' : '' }}">
                                                            <a class="dash-link"
                                                                href="{{ route('attendanceemployee.index') }}">{{ __('Mark Attendance') }}</a>
                                                        </li>
                                                        @can('create attendance')
                                                            <li
                                                                class="dash-item {{ Request::route()->getName() == 'attendanceemployee.bulkattendance' ? 'active' : '' }}">
                                                                <a class="dash-link"
                                                                    href="{{ route('attendanceemployee.bulkattendance') }}">{{ __('Bulk Attendance') }}</a>
                                                            </li>
                                                        @endcan
                                                    </ul>
                                                </li>
                                            @endcan
                                        </ul>
                                    </li>
                                @endif

                                @if (Gate::check('manage indicator') || Gate::check('manage appraisal') || Gate::check('manage goal tracking'))
                                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'indicator' || Request::segment(1) == 'appraisal' || Request::segment(1) == 'goaltracking' ? 'active dash-trigger' : '' }}"
                                        href="#navbar-performance" data-toggle="collapse" role="button"
                                        aria-expanded="{{ Request::segment(1) == 'indicator' || Request::segment(1) == 'appraisal' || Request::segment(1) == 'goaltracking' ? 'true' : 'false' }}">
                                        <a class="dash-link" href="#">{{ __('Performance Setup') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul
                                            class="dash-submenu {{ Request::segment(1) == 'indicator' || Request::segment(1) == 'appraisal' || Request::segment(1) == 'goaltracking' ? 'show' : 'collapse' }}">
                                            @can('manage indicator')
                                                <li class="dash-item {{ request()->is('indicator*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('indicator.index') }}">{{ __('Indicator') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage appraisal')
                                                <li class="dash-item {{ request()->is('appraisal*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('appraisal.index') }}">{{ __('Appraisal') }}</a>
                                                </li>
                                            @endcan --}}
                                            {{-- @can('manage goal tracking')
                                                <li
                                                    class="dash-item  {{ request()->is('goaltracking*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('goaltracking.index') }}">{{ __('Goal Tracking') }}</a>
                                                </li>
                                            @endcan
                                        </ul>
                                    </li>
                                @endif

                                @if (Gate::check('manage training') || Gate::check('manage trainer') || Gate::check('show training'))
                                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'trainer' || Request::segment(1) == 'training' ? 'active dash-trigger' : '' }}"
                                        href="#navbar-training" data-toggle="collapse" role="button"
                                        aria-expanded="{{ Request::segment(1) == 'trainer' || Request::segment(1) == 'training' ? 'true' : 'false' }}">
                                        <a class="dash-link" href="#">{{ __('Training Setup') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            @can('manage training')
                                                <li class="dash-item {{ request()->is('training*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('training.index') }}">{{ __('Training List') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage trainer')
                                                <li class="dash-item {{ request()->is('trainer*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('trainer.index') }}">{{ __('Trainer') }}</a>
                                                </li>
                                            @endcan

                                        </ul>
                                    </li>
                                @endif

                                @if (Gate::check('manage job') ||
                                        Gate::check('create job') ||
                                        Gate::check('manage job application') ||
                                        Gate::check('manage job onBoard') ||
                                        Gate::check('manage custom question') ||
                                        Gate::check('show interview schedule') ||
                                        Gate::check('show career'))
                                    <li
                                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'job' || Request::segment(1) == 'job-application' || Request::segment(1) == 'candidates-job-applications' || Request::segment(1) == 'job-onboard' || Request::segment(1) == 'custom-question' || Request::segment(1) == 'interview-schedule' || Request::segment(1) == 'career' ? 'active dash-trigger' : '' }}    ">
                                        <a class="dash-link" href="#">{{ __('Recruitment Setup') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            @can('manage job')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'job.index' || Request::route()->getName() == 'job.create' || Request::route()->getName() == 'job.edit' || Request::route()->getName() == 'job.show' ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('job.index') }}">{{ __('Jobs') }}</a>
                                                </li>
                                            @endcan
                                            @can('create job')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'job.create' ? 'active' : '' }} ">
                                                    <a class="dash-link"
                                                        href="{{ route('job.create') }}">{{ __('Job Create') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage job application')
                                                <li
                                                    class="dash-item {{ request()->is('job-application*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('job-application.index') }}">{{ __('Job Application') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage job onBoard')
                                                <li
                                                    class="dash-item {{ request()->is('candidates-job-applications') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('job.application.candidate') }}">{{ __('Job Candidate') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage job onBoard')
                                                <li
                                                    class="dash-item {{ request()->is('job-onboard*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('job.on.board') }}">{{ __('Job On-boarding') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage custom question')
                                                <li
                                                    class="dash-item  {{ request()->is('custom-question*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('custom-question.index') }}">{{ __('Custom Question') }}</a>
                                                </li>
                                            @endcan
                                            @can('show interview schedule')
                                                <li
                                                    class="dash-item {{ request()->is('interview-schedule*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('interview-schedule.index') }}">{{ __('Interview Schedule') }}</a>
                                                </li>
                                            @endcan
                                            @can('show career')
                                                <li class="dash-item {{ request()->is('career*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('career', [\Auth::user()->creatorId(), $lang]) }}">{{ __('Career') }}</a>
                                                </li>
                                            @endcan
                                        </ul>
                                    </li>
                                @endif

                                @if (Gate::check('manage award') ||
                                        Gate::check('manage transfer') ||
                                        Gate::check('manage resignation') ||
                                        Gate::check('manage travel') ||
                                        Gate::check('manage promotion') ||
                                        Gate::check('manage complaint') ||
                                        Gate::check('manage warning') ||
                                        Gate::check('manage termination') ||
                                        Gate::check('manage announcement') ||
                                        Gate::check('manage holiday'))
                                    <li
                                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'holiday-calender' || Request::segment(1) == 'holiday' || Request::segment(1) == 'policies' || Request::segment(1) == 'award' || Request::segment(1) == 'transfer' || Request::segment(1) == 'resignation' || Request::segment(1) == 'travel' || Request::segment(1) == 'promotion' || Request::segment(1) == 'complaint' || Request::segment(1) == 'warning' || Request::segment(1) == 'termination' || Request::segment(1) == 'announcement' || Request::segment(1) == 'competencies' ? 'active dash-trigger' : '' }}">
                                        <a class="dash-link" href="#">{{ __('HR Admin Setup') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            @can('manage award')
                                                <li class="dash-item {{ request()->is('award*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('award.index') }}">{{ __('Award') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage transfer')
                                                <li class="dash-item  {{ request()->is('transfer*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('transfer.index') }}">{{ __('Transfer') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage resignation')
                                                <li
                                                    class="dash-item {{ request()->is('resignation*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('resignation.index') }}">{{ __('Resignation') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage travel')
                                                <li class="dash-item {{ request()->is('travel*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('travel.index') }}">{{ __('Trip') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage promotion')
                                                <li class="dash-item {{ request()->is('promotion*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('promotion.index') }}">{{ __('Promotion') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage complaint')
                                                <li class="dash-item {{ request()->is('complaint*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('complaint.index') }}">{{ __('Complaints') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage warning')
                                                <li class="dash-item {{ request()->is('warning*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('warning.index') }}">{{ __('Warning') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage termination')
                                                <li
                                                    class="dash-item {{ request()->is('termination*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('termination.index') }}">{{ __('Termination') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage announcement')
                                                <li
                                                    class="dash-item {{ request()->is('announcement*') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('announcement.index') }}">{{ __('Announcement') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage holiday')
                                                <li
                                                    class="dash-item {{ request()->is('holiday*') || request()->is('holiday-calender') ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('holiday.index') }}">{{ __('Holidays') }}</a>
                                                </li>
                                            @endcan
                                        </ul>
                                    </li>
                                @endif

                                @can('manage event')
                                    <li class="dash-item {{ request()->is('event*') ? 'active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('event.index') }}">{{ __('Event Setup') }}</a>
                                    </li>
                                @endcan
                                @can('manage meeting')
                                    <li class="dash-item {{ request()->is('meeting*') ? 'active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('meeting.index') }}">{{ __('Meeting') }}</a>
                                    </li>
                                @endcan
                                @can('manage assets')
                                    <li class="dash-item {{ request()->is('account-assets*') ? 'active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('account-assets.index') }}">{{ __('Employees Asset Setup ') }}</a>
                                    </li>
                                @endcan
                                @can('manage document')
                                    <li class="dash-item {{ request()->is('document-upload*') ? 'active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('document-upload.index') }}">{{ __('Document Setup') }}</a>
                                    </li>
                                @endcan
                                @can('manage company policy')
                                    <li class="dash-item {{ request()->is('company-policy*') ? 'active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('company-policy.index') }}">{{ __('Company policy') }}</a>
                                    </li>
                                @endcan

                                @if (\Auth::user()->type == 'company' || \Auth::user()->type == 'HR')
                                    <li
                                        class="dash-item {{ Request::segment(1) == 'leavetype' ||
                                        Request::segment(1) == 'document' ||
                                        Request::segment(1) == 'performanceType' ||
                                        Request::segment(1) == 'branch' ||
                                        Request::segment(1) == 'department' ||
                                        Request::segment(1) == 'designation' ||
                                        Request::segment(1) == 'job-stage' ||
                                        Request::segment(1) == 'competencies' ||
                                        Request::segment(1) == 'job-category' ||
                                        Request::segment(1) == 'terminationtype' ||
                                        Request::segment(1) == 'awardtype' ||
                                        Request::segment(1) == 'trainingtype' ||
                                        Request::segment(1) == 'goaltype' ||
                                        Request::segment(1) == 'paysliptype' ||
                                        Request::segment(1) == 'allowanceoption' ||
                                        Request::segment(1) == 'loanoption' ||
                                        Request::segment(1) == 'deductionoption'
                                            ? 'active dash-trigger'
                                            : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('branch.index') }}">{{ __('HRM System Setup') }}</a>
                                    </li>
                                @endif

                                @can('manage employee')
                                    <li class="dash-item {{ Request::route()->getName() == 'employee.index' ? 'active' : '' }}">
                                        <a href="{{ route('employee.index') }}" class="dash-link">
                                            <span class="dash-micon"><i class="ti ti-users"></i></span>
                                            <span class="dash-mtext">{{ __('Employee Management') }}</span>
                                        </a>
                                    </li>
                                @endcan

                            </ul>
                        </li>
                    @endif
                @endif --}}

                <!--------------------- End HRM ----------------------------------->

                <!--------------------- Start Account ----------------------------------->

                @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1)))
                    @if (Gate::check('manage budget plan') || Gate::check('income vs expense report') ||
                            Gate::check('manage goal') || Gate::check('manage constant tax') ||
                            Gate::check('manage constant category') || Gate::check('manage constant unit') ||
                            Gate::check('manage constant custom field') || Gate::check('manage print settings') ||
                            Gate::check('manage customer') || Gate::check('manage vender') ||
                            Gate::check('manage proposal') || Gate::check('manage bank account') ||
                            Gate::check('manage bank transfer') || Gate::check('manage invoice') ||
                            Gate::check('manage revenue') || Gate::check('manage credit note') ||
                            Gate::check('manage bill') || Gate::check('manage payment') ||
                            Gate::check('manage debit note') || Gate::check('manage chart of account') ||
                            Gate::check('manage journal entry') || Gate::check('balance sheet report') ||
                            Gate::check('ledger report') || Gate::check('trial balance report') )
                        <li
                            class="dash-item dash-hasmenu
                                        {{ Request::route()->getName() == 'print-setting' ||
                                        Request::segment(1) == 'customer' || Request::segment(1) == 'vender' ||
                                        Request::segment(1) == 'proposal' || Request::segment(1) == 'bank-account' ||
                                        Request::segment(1) == 'bank-transfer' || Request::segment(1) == 'invoice' ||
                                        Request::segment(1) == 'revenue' || Request::segment(1) == 'credit-note' ||
                                        Request::segment(1) == 'taxes' || Request::segment(1) == 'product-category' ||
                                        Request::segment(1) == 'product-unit' || Request::segment(1) == 'payment-method' ||
                                        Request::segment(1) == 'custom-field' || Request::segment(1) == 'chart-of-account-type' ||
                                        (Request::segment(1) == 'transaction' && Request::segment(2) != 'ledger' &&
                                            Request::segment(2) != 'balance-sheet-report' && Request::segment(2) != 'trial-balance') ||
                                        Request::segment(1) == 'goal' || Request::segment(1) == 'budget' ||
                                        Request::segment(1) == 'chart-of-account' || Request::segment(1) == 'journal-entry' ||
                                        Request::segment(2) == 'ledger' || Request::segment(2) == 'balance-sheet' ||
                                        Request::segment(2) == 'trial-balance' || Request::segment(2) == 'profit-loss' ||
                                        Request::segment(1) == 'bill' || Request::segment(1) == 'expense' ||
                                        Request::segment(1) == 'payment' || Request::segment(1) == 'debit-note' || (Request::route()->getName() == 'report.balance.sheet') || (Request::route()->getName() == 'trial-balance-report') ? ' active dash-trigger'
                                            : '' }}">
                            <a href="#!" class="dash-link"><span class="dash-micon"><i
                                        class="fas fa-calculator"></i></span><span
                                    class="dash-mtext">{{ __('Accounting System ') }}
                                </span><span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                            </a>
                            <ul class="dash-submenu">

                                @if (Gate::check('manage bank account') || Gate::check('manage bank transfer'))
                                    <li
                                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'bank-account' || Request::segment(1) == 'bank-transfer' ? 'active dash-trigger' : '' }}">
                                        <a class="dash-link" href="#">{{ __('Banking') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            <li
                                                class="dash-item {{ Request::route()->getName() == 'bank-account.index' || Request::route()->getName() == 'bank-account.create' || Request::route()->getName() == 'bank-account.edit' ? ' active' : '' }}">
                                                <a class="dash-link"
                                                    href="{{ route('bank-account.index') }}">{{ __('Account') }}</a>
                                            </li>
                                            <li
                                                class="dash-item {{ Request::route()->getName() == 'bank-transfer.index' || Request::route()->getName() == 'bank-transfer.create' || Request::route()->getName() == 'bank-transfer.edit' ? ' active' : '' }}">
                                                <a class="dash-link"
                                                    href="{{ route('bank-transfer.index') }}">{{ __('Transfer') }}</a>
                                            </li>
                                        </ul>
                                    </li>
                                @endif
                                @if (Gate::check('manage customer') ||
                                        Gate::check('manage proposal') ||
                                        Gate::check('manage invoice') ||
                                        Gate::check('manage revenue') ||
                                        Gate::check('manage credit note'))
                                    <li
                                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'customer' || Request::segment(1) == 'proposal' || Request::segment(1) == 'invoice' || Request::segment(1) == 'revenue' || Request::segment(1) == 'credit-note' ? 'active dash-trigger' : '' }}">
                                        <a class="dash-link" href="#">{{ __('Sales') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            @if (Gate::check('manage customer'))
                                                <li
                                                    class="dash-item {{ Request::segment(1) == 'customer' ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('customer.index') }}">{{ __('Customer') }}</a>
                                                </li>
                                            @endif
                                            @if (Gate::check('manage proposal'))
                                                <li
                                                    class="dash-item {{ Request::segment(1) == 'proposal' ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('proposal.index') }}">{{ __('Estimate') }}</a>
                                                </li>
                                            @endif
                                            @can('manage invoice')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'invoice.index' || Request::route()->getName() == 'invoice.create' || Request::route()->getName() == 'invoice.edit' || Request::route()->getName() == 'invoice.show' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('invoice.index') }}">{{ __('Invoice') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage revenue')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'revenue.index' || Request::route()->getName() == 'revenue.create' || Request::route()->getName() == 'revenue.edit' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('revenue.index') }}">{{ __('Revenue') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage credit note')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'credit.note' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('credit.note') }}">{{ __('Credit Note') }}</a>
                                                </li>
                                            @endcan
                                        </ul>
                                    </li>
                                @endif
                                @if (Gate::check('manage vender') ||
                                        Gate::check('manage bill') ||
                                        Gate::check('manage payment') ||
                                        Gate::check('manage debit note'))
                                    <li
                                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'bill' || Request::segment(1) == 'vender' || Request::segment(1) == 'expense' || Request::segment(1) == 'payment' || Request::segment(1) == 'debit-note' ? 'active dash-trigger' : '' }}">
                                        <a class="dash-link" href="#">{{ __('Purchases') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            @if (Gate::check('manage vender'))
                                                <li
                                                    class="dash-item {{ Request::segment(1) == 'vender' ? 'active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('vender.index') }}">{{ __('Suppiler') }}</a>
                                                </li>
                                            @endif
                                            @can('manage bill')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'bill.index' || Request::route()->getName() == 'bill.create' || Request::route()->getName() == 'bill.edit' || Request::route()->getName() == 'bill.show' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                    href="{{ route('bill.index') }}">{{ __('Bill') }}</a>
                                                </li>
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'expense.index' || Request::route()->getName() == 'expense.create' || Request::route()->getName() == 'expense.edit' || Request::route()->getName() == 'expense.show' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('expense.index') }}">{{ __('Expense') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage payment')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'payment.index' || Request::route()->getName() == 'payment.create' || Request::route()->getName() == 'payment.edit' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('payment.index') }}">{{ __('Payment') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage debit note')
                                                <li
                                                    class="dash-item  {{ Request::route()->getName() == 'debit.note' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('debit.note') }}">{{ __('Debit Note') }}</a>
                                                </li>
                                            @endcan
                                        </ul>
                                    </li>
                                @endif
                                @if (Gate::check('manage chart of account') ||
                                        Gate::check('manage journal entry') ||
                                        Gate::check('ledger report') ||
                                        Gate::check('bill report') ||
                                        Gate::check('income vs expense report') ||
                                        Gate::check('trial balance report'))
                                    <li
                                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'chart-of-account' ||
                                        Request::segment(1) == 'journal-entry' ||
                                        Request::segment(2) == 'profit-loss' ||
                                        Request::segment(2) == 'ledger' ||
                                        Request::segment(2) == 'trial-balance-report' ||
                                        Request::segment(2) == 'balance-sheet-report' ||
                                        Request::segment(2) == 'trial-balance' || (Request::route()->getName() == 'report.balance.sheet') || (Request::route()->getName() == 'trial-balance-report') ? 'active dash-trigger'
                                            : '' }}">
                                        <a class="dash-link" href="#">{{ __('Double Entry') }}<span
                                                class="dash-arrow"><i data-feather="chevron-right"></i></span></a>
                                        <ul class="dash-submenu">
                                            @can('manage chart of account')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'chart-of-account.index' || Request::route()->getName() == 'chart-of-account.show' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('chart-of-account.index') }}">{{ __('Chart of Accounts') }}</a>
                                                </li>
                                            @endcan
                                            @can('manage journal entry')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'journal-entry.edit' ||
                                                    Request::route()->getName() == 'journal-entry.create' ||
                                                    Request::route()->getName() == 'journal-entry.index' ||
                                                    Request::route()->getName() == 'journal-entry.show'
                                                        ? ' active'
                                                        : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('journal-entry.index') }}">{{ __('Journal Account') }}</a>
                                                </li>
                                            @endcan
                                            @can('ledger report')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'report.ledger' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('report.ledger', 0) }}">{{ __('Ledger Summary') }}</a>
                                                </li>
                                            @endcan
                                            @can('bill report')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'report.balance.sheet' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('report.balance.sheet') }}">{{ __('Balance Sheet') }}</a>
                                                </li>
                                            @endcan
                                            @can('income vs expense report')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'report.profit.loss' ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('report.profit.loss') }}">{{ __('Profit & Loss') }}</a>
                                                </li>
                                            @endcan
                                            @can('trial balance report')
                                                <li
                                                    class="dash-item {{ Request::route()->getName() == 'trial.balance' || (Request::route()->getName() == 'trial-balance-report') ? ' active' : '' }}">
                                                    <a class="dash-link"
                                                        href="{{ route('trial.balance') }}">{{ __('Trial Balance') }}</a>
                                                </li>
                                            @endcan
                                        </ul>
                                    </li>
                                @endif
                                @if (\Auth::user()->type == 'company')
                                    <li class="dash-item {{ Request::segment(1) == 'budget' ? 'active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('budget.index') }}">{{ __('Budget Planner') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('manage goal'))
                                    <li class="dash-item {{ Request::segment(1) == 'goal' ? 'active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('goal.index') }}">{{ __('Financial Goal') }}</a>
                                    </li>
                                @endif
                                @if (Gate::check('manage constant tax') ||
                                        Gate::check('manage constant category') ||
                                        Gate::check('manage constant unit') ||
                                        Gate::check('manage constant custom field'))
                                    <li
                                        class="dash-item {{ Request::segment(1) == 'taxes' || Request::segment(1) == 'product-category' || Request::segment(1) == 'product-unit' || Request::segment(1) == 'payment-method' || Request::segment(1) == 'custom-field' || Request::segment(1) == 'chart-of-account-type' ? 'active dash-trigger' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('taxes.index') }}">{{ __('Accounting Setup') }}</a>
                                    </li>
                                @endif

                                @if (Gate::check('manage print settings'))
                                    <li
                                        class="dash-item {{ Request::route()->getName() == 'print-setting' ? ' active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('print.setting') }}">{{ __('Print Settings') }}</a>
                                    </li>
                                @endif

                            </ul>
                        </li>
                    @endif
                @endif

                <!--------------------- End Account ----------------------------------->

                <!--------------------- Start Finance ----------------------------------->

                @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('account')) || ($user->type === 'employee' && $userPlan->hasModule('account')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->account == 1)))
                    @if (Gate::check('manage customer') || Gate::check('manage vender') || Gate::check('manage proposal') ||
                         Gate::check('manage invoice') || Gate::check('manage revenue') || Gate::check('manage bill') ||
                         Gate::check('manage expense') || Gate::check('manage payment') || Gate::check('manage chart of account') ||
                         Gate::check('manage journal entry') || Gate::check('ledger report') || Gate::check('bill report') ||
                         Gate::check('manage bank account') || Gate::check('manage bank transfer'))
                        <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'finance' ? 'active dash-trigger' : '' }}">
                            <a href="{{ route('finance.dashboard') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-coins"></i></span>
                                <span class="dash-mtext">{{ __('Finance') }}</span>
                            </a>
                        </li>
                    @endif
                @endif

                <!--------------------- End Finance ----------------------------------->

                <!--------------------- Start CRM ----------------------------------->

                @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('crm')) || ($user->type === 'employee' && $userPlan->hasModule('crm')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->crm == 1)))
                    @if (Gate::check('manage lead') ||
                            Gate::check('manage deal') ||
                            Gate::check('manage form builder') ||
                            Gate::check('manage contract') ||
                            Gate::check('manage pipeline') ||
                            true)
                        <li
                            class="dash-item dash-hasmenu {{ Request::segment(1) == 'stages' || Request::segment(1) == 'labels' || Request::segment(1) == 'sources' || Request::segment(1) == 'lead_stages' || Request::segment(1) == 'pipelines' || Request::segment(1) == 'deals' || Request::segment(1) == 'leads' || Request::segment(1) == 'form_builder' || Request::segment(1) == 'contractType' || Request::segment(1) == 'form_response' || Request::segment(1) == 'contract' ? ' active dash-trigger' : '' }}">
                            <a href="{{ route('leads.index') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-users"></i></span>
                                <span class="dash-mtext">{{ __('Lead Tracker') }}</span>
                            </a>
                        </li>
                    @endif
                @endif

                <!--------------------- End CRM ----------------------------------->
                <!--------------------- Start Booking System ----------------------------------->
                @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('booking')) || ($user->type === 'employee' && $userPlan->hasModule('booking')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->booking == 1)))
                    @can('manage project task')
                        <li class="dash-item {{ request()->is('calendar*') ? 'active' : '' }}">
                            <a href="{{ route('task.calendar', ['all']) }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-address-card fa-2x"></i></span>
                                <span class="dash-mtext">{{ __('Smart Scheduler') }}</span>
                            </a>
                        </li>
                    @endcan
                @endif
                <!--------------------- End Booking System ----------------------------------->

                <!--------------------- Start Booking Module ----------------------------------->

                <!-- @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('booking')) || ($user->type === 'employee' && $userPlan->hasModule('booking')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->booking == 1)))
                    @if (Gate::check('manage booking') || Gate::check('view booking'))
                        <li
                            class="dash-item {{ Request::segment(1) == 'bookings' || Request::segment(1) == 'appointment-bookings' || Request::segment(1) == 'appointments' || Request::segment(1) == 'calendar' && Request::segment(2) == 'events' ? ' active' : '' }}">
                            <a class="dash-link" href="{{ route('calendar.view') }}">
                                <span class="dash-micon"><i class="ti ti-calendar-event"></i></span>
                                <span class="dash-mtext">{{ __('Calendar') }}</span>
                            </a>
                        </li>
                    @endif
                @endif  -->

                <!--------------------- End Booking Module ----------------------------------->

                <!--------------------- Start Project ----------------------------------->

                @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('project')) || ($user->type === 'employee' && $userPlan->hasModule('project')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->project == 1)))
                    @can('manage project')
                        <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'project' || Request::segment(1) == 'projects' || request()->is('projects/*') ? 'active dash-trigger' : '' }}">
                            <a href="{{ route('projects.index') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-rocket"></i></span>
                                <span class="dash-mtext">{{ __('Mission Control') }}</span>
                            </a>
                        </li>
                    @endcan
                @endif

                <!--------------------- End Project ----------------------------------->

                <!--------------------- Start My Tasks (Personal Tasks) ----------------------------------->
                @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('personal_tasks')) || ($user->type === 'employee' && $userPlan->hasModule('personal_tasks')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->personal_tasks == 1)))
                    @can('manage personal task')
                        <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'my-tasks' || Request::segment(1) == 'personal-tasks' || request()->is('personal-taskboard*') ? 'active dash-trigger' : '' }}">
                            <a href="{{ route('my-tasks.index') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-tasks"></i></span>
                                <span class="dash-mtext">{{ __('My Tasks') }}</span>
                            </a>
                        </li>
                    @endcan
                @endif
                <!--------------------- End My Tasks (Personal Tasks) ----------------------------------->
                @if (\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff')
                        <!-- OMX Flow Module - Show for users with omx flow module permission -->
                        

                        

                       

                        <!-- Campaigns - Show only if user has specific permission -->
                        @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1)))
                            @if ($user->hasModulePermission('omx_flow', 'campaigns'))
                                <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : '' }}">
                                    <a href="{{ route('company.modules.whatsapp-campaign') }}" class="dash-link">
                                        <span class="dash-micon"><i class="ti ti-send"></i></span><span
                                            class="dash-mtext">{{ __('Campaigns') }}</span>
                                    </a>
                                </li>
                            @endif
                        @endif

                       

                        

                        <!-- Unified Inbox - Available for all users (no restrictions) -->
                        @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1)))
                            @if (Gate::check('access omx flow'))
                            <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat') ? 'active dash-trigger' : '' }}">
                    <a href="{{ route('company.modules.whatsapp-chat') }}" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-inbox"></i></span><span
                            class="dash-mtext">{{ __('Unified Inbox') }}</span> 
                    </a>
                </li>
                            @endif
                        @endif

                        <!-- Automatish Module - Show for users with automatish module permission -->
                        @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('automatish')) || ($user->type === 'employee' && $userPlan->hasModule('automatish')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->automatish == 1)))
                            @if (Gate::check('access automatish'))
                                @php
                                    $automatishModule = \App\Models\ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
                                @endphp
                                @if($automatishModule && $automatishModule->sso_endpoint)
                                    <li class="dash-item dash-hasmenu">
                                        <a href="{{ route('company.modules.automatish') }}" class="dash-link">
                                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                                class="dash-mtext">{{ __('Auto Pilot') }}</span>
                                        </a>
                                    </li>
                                @endif
                            @endif
                        @endif

                    @endif
                @endif
                @if (\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff')
                        <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' ? 'active' : '' }}">
                            <a href="{{ route('company.modules.omx-flow') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-comments"></i></span><span
                                    class="dash-mtext">{{ __('Chat Flows') }}</span>
                            </a>
                        </li>
                    @endif
                



                <!--------------------- Start User Managaement System ----------------------------------->

                @if (\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && Gate::check('manage client'))
                    <li
                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'clients' ? ' active dash-trigger' : '' }}">

                        <a href="#!" class="dash-link "><span class="dash-micon"><i class="fas fa-user-friends"></i></span><span
                                class="dash-mtext">{{ __('User Management') }}</span><span class="dash-arrow"><i
                                    data-feather="chevron-right"></i></span></a>
                        <ul class="dash-submenu">
                            <!-- @can('manage user')
                                <li class="dash-item {{ Request::route()->getName() == 'users.index' ? 'active' : '' }}">
                                    <a class="dash-link" href="{{ route('users.index') }}">{{ __('Users') }}</a>
                                </li>
                            @endcan -->
                            @can('manage employee')
                                <li class="dash-item {{ Request::route()->getName() == 'employee.index' ? 'active' : '' }}">
                                    <a href="{{ route('employee.index') }}" class="dash-link">
                                    
                                        {{ __('Employee') }}
                                    </a>
                                </li>
                            @endcan
                            @can('manage client')
                                <li
                                    class="dash-item {{ Request::route()->getName() == 'clients.index' || Request::segment(1) == 'clients' || Request::route()->getName() == 'clients.edit' ? ' active' : '' }}">
                                    <a class="dash-link" href="{{ route('clients.index') }}">{{ __('Client') }}</a>
                                </li>
                            @endcan
                        </ul>
                    </li>
                @endif

                <!--------------------- End User Managaement System----------------------------------->


                <!--------------------- Start Products System ----------------------------------->

                <!-- @if (Gate::check('manage product & service'))
                    <li class="dash-item dash-hasmenu">
                        <a href="#!" class="dash-link ">
                            <span class="dash-micon"><i class="fas fa-box"></i></span><span
                                class="dash-mtext">{{ __('Products System') }}</span><span class="dash-arrow">
                                <i data-feather="chevron-right"></i></span>
                        </a>
                        <ul class="dash-submenu">
                            @if (Gate::check('manage product & service'))
                                <li class="dash-item {{ Request::segment(1) == 'productservice' ? 'active' : '' }}">
                                    <a href="{{ route('productservice.index') }}"
                                        class="dash-link">{{ __('Product & Services') }}
                                    </a>
                                </li>
                                <li class="dash-item {{ Request::segment(1) == 'productstock' ? 'active' : '' }}">
                                    <a href="{{ route('productstock.index') }}"
                                        class="dash-link">{{ __('Product Stock') }}
                                    </a>
                                </li>
                            @endif
                        </ul>
                    </li>
                @endif -->

                <!--------------------- End Products System ----------------------------------->


                <!--------------------- Start POs System ----------------------------------->
                {{-- POS System section hidden from company panel --}}
                {{-- @if (!empty($userPlan) && $userPlan->pos == 1)
                    @if (Gate::check('manage warehouse') ||
                            Gate::check('manage purchase') ||
                            Gate::check('manage quotation') ||
                            Gate::check('create barcode') ||
                            Gate::check('manage pos') ||
                            Gate::check('manage print settings'))
                        <li
                            class="dash-item dash-hasmenu {{ Request::segment(1) == 'warehouse' || Request::segment(1) == 'purchase' || Request::segment(1) == 'quotation' || Request::route()->getName() == 'pos.barcode' || Request::route()->getName() == 'pos.print' || Request::route()->getName() == 'pos.show' ? ' active dash-trigger' : '' }}">
                            <a href="#!" class="dash-link"><span class="dash-micon"><i
                                        class="ti ti-layers-difference"></i></span><span
                                    class="dash-mtext">{{ __('POS System') }}</span><span class="dash-arrow"><i
                                        data-feather="chevron-right"></i></span></a>
                            <ul
                                class="dash-submenu {{ Request::segment(1) == 'warehouse' ||
                                Request::segment(1) == 'purchase' ||
                                Request::route()->getName() == 'pos.barcode' ||
                                Request::route()->getName() == 'pos.print' ||
                                Request::route()->getName() == 'pos.show'
                                    ? 'show'
                                    : '' }}">
                                @can('manage warehouse')
                                    <li
                                        class="dash-item {{ Request::route()->getName() == 'warehouse.index' || Request::route()->getName() == 'warehouse.show' ? ' active' : '' }}">
                                        <a class="dash-link" href="{{ route('warehouse.index') }}">{{ __('Warehouse') }}</a>
                                    </li>
                                @endcan
                                @can('manage purchase')
                                    <li
                                        class="dash-item {{ Request::route()->getName() == 'purchase.index' || Request::route()->getName() == 'purchase.create' || Request::route()->getName() == 'purchase.edit' || Request::route()->getName() == 'purchase.show' ? ' active' : '' }}">
                                        <a class="dash-link" href="{{ route('purchase.index') }}">{{ __('Purchase') }}</a>
                                    </li>
                                @endcan
                                @can('manage quotation')
                                    <li
                                        class="dash-item {{ Request::route()->getName() == 'quotation.index' || Request::route()->getName() == 'quotations.create' || Request::route()->getName() == 'quotation.edit' || Request::route()->getName() == 'quotation.show' ? ' active' : '' }}">
                                        <a class="dash-link" href="{{ route('quotation.index') }}">{{ __('Quotation') }}</a>
                                    </li>
                                @endcan
                                @can('manage pos')
                                    <li class="dash-item {{ Request::route()->getName() == 'pos.index' ? ' active' : '' }}">
                                        <a class="dash-link" href="{{ route('pos.index') }}">{{ __(' Add POS') }}</a>
                                    </li>
                                    <li
                                        class="dash-item {{ Request::route()->getName() == 'pos.report' || Request::route()->getName() == 'pos.show' ? ' active' : '' }}">
                                        <a class="dash-link" href="{{ route('pos.report') }}">{{ __('POS') }}</a>
                                    </li>
                                @endcan
                                @can('manage warehouse')
                                    <li
                                        class="dash-item {{ Request::route()->getName() == 'warehouse-transfer.index' || Request::route()->getName() == 'warehouse-transfer.show' ? ' active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('warehouse-transfer.index') }}">{{ __('Transfer') }}</a>
                                    </li>
                                @endcan
                                @can('create barcode')
                                    <li
                                        class="dash-item {{ Request::route()->getName() == 'pos.barcode' || Request::route()->getName() == 'pos.print' ? ' active' : '' }}">
                                        <a class="dash-link" href="{{ route('pos.barcode') }}">{{ __('Print Barcode') }}</a>
                                    </li>
                                @endcan
                                @can('manage pos')
                                    <li
                                        class="dash-item {{ Request::route()->getName() == 'pos-print-setting' ? ' active' : '' }}">
                                        <a class="dash-link"
                                            href="{{ route('pos.print.setting') }}">{{ __('Print Settings') }}</a>
                                    </li>
                                @endcan
                            </ul>
                        </li>
                    @endif
                @endif --}}
                <!--------------------- End POs System ----------------------------------->

                @if (\Auth::user()->type != 'super admin'  && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff')
                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'support' ? 'active dash-trigger' : '' }}">
                        <a href="{{ route('support.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-life-ring"></i></span><span
                                class="dash-mtext">{{ __('Support System') }}</span>
                        </a>
                    </li>
                    {{-- Zoom Meeting and Messenger hidden from sidebar --}}
                    {{--
                    <li
                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'zoom-meeting' || Request::segment(1) == 'zoom-meeting-calender' ? 'active' : '' }}">
                        <a href="{{ route('zoom-meeting.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-user-check"></i></span><span
                                class="dash-mtext">{{ __('Zoom Meeting') }}</span>
                        </a>
                    </li>

                    
                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'chats' ? 'active' : '' }}">
                        <a href="{{ url('calendar/events') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                                class="dash-mtext">{{ __('Calender') }}</span>
                        </a>
                    </li>


                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'chats' ? 'active' : '' }}">
                        <a href="{{ url('chats') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                                class="dash-mtext">{{ __('Messenger') }}</span>
                        </a>
                    </li>
                    --}}
                    

                @if (\Auth::user()->type == 'company')
                    <!-- <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'notification_templates' ? 'active' : '' }}">
                        <a href="{{ route('notification-templates.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-notification"></i></span><span
                                class="dash-mtext">{{ __('Notification Template') }}</span>
                        </a>
                    </li> -->
                @endif

                <!--------------------- Start System Setup ----------------------------------->

                @if (\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin')
                    @if (Gate::check('manage company plan') || Gate::check('manage order') || Gate::check('manage company settings'))
                        <li
                            class="dash-item dash-hasmenu {{ Request::segment(1) == 'settings' ||
                            Request::segment(1) == 'plans' ||
                            Request::segment(1) == 'stripe' ||
                            Request::segment(1) == 'order'
                                ? ' active dash-trigger'
                                : '' }}">
                            <a href="{{ route('settings') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-sliders-h"></i></span><span
                                    class="dash-mtext">{{ __('System Settings') }}</span>
                                <!-- <span class="dash-arrow">
                                    <i data-feather="chevron-right"></i>
                                </span> -->
                            </a>
                            <ul class="dash-submenu">
                                @if (Gate::check('manage company settings'))
                                    <!-- <li
                                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'settings' ? ' active' : '' }}">
                                        <a href="{{ route('settings') }}"
                                            class="dash-link">{{ __('System Settings') }}
                                        </a>
                                    </li> -->
                                @endif
                                @if (Gate::check('manage pricing plan'))
                                    <li
                                        class="dash-item{{ Request::route()->getName() == 'pricing-plans.index' || Request::route()->getName() == 'stripe' ? ' active' : '' }}">
                                        <a href="{{ route('pricing-plans.index') }}"
                                            class="dash-link">{{ __('Setup Subscription Plan') }}</a>
                                    </li>
                                @endif
                                <!-- <li
                                    class="dash-item{{ Request::route()->getName() == 'referral-program.company' ? ' active' : '' }}">
                                    <a href="{{ route('referral-program.company') }}"
                                        class="dash-link">{{ __('Referral Program') }}</a>
                                </li> -->

                                @if (Gate::check('manage order') && Auth::user()->type == 'company')
                                    <!-- <li class="dash-item {{ Request::segment(1) == 'order' ? 'active' : '' }}">
                                        <a href="{{ route('order.index') }}" class="dash-link">{{ __('Order') }}</a>
                                    </li> -->
                                @endif
                            </ul>
                        </li>
                    @endif
                @endif
                @endif




                <!--------------------- Start Settings ----------------------------------->
                @if (\Auth::user()->type != 'super admin' && \Auth::user()->type != 'system admin' && \Auth::user()->type != 'staff')
                    @if(Request::segment(1) == 'settings')
                        <!-- Back to Main Menu -->
                        <li class="dash-item dash-hasmenu">
                            <a href="{{ route('dashboard') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-arrow-left"></i></span>
                                <span class="dash-mtext">{{ __('Back to Main Menu') }}</span>
                            </a>
                        </li>
                        
                        <!-- Settings Sections -->
                        <li class="dash-item dash-hasmenu {{ !isset($section) || $section == 'brand' ? 'active' : '' }}">
                            <a href="{{ route('settings.brand') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-palette"></i></span>
                                <span class="dash-mtext">{{ __('Brand Settings') }}</span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu {{ $section == 'system' ? 'active' : '' }}">
                            <a href="{{ route('settings.system') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-desktop"></i></span>
                                <span class="dash-mtext">{{ __('General Settings') }}</span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu {{ $section == 'currency' ? 'active' : '' }}">
                            <a href="{{ route('settings.currency') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-dollar-sign"></i></span>
                                <span class="dash-mtext">{{ __('Currency Settings') }}</span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu {{ $section == 'email' ? 'active' : '' }}">
                            <a href="{{ route('settings.email') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-envelope"></i></span>
                                <span class="dash-mtext">{{ __('Email Settings') }}</span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu {{ $section == 'sso' ? 'active' : '' }}">
                            <a href="{{ route('settings.sso') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-key"></i></span>
                                <span class="dash-mtext">{{ __('SSO Settings') }}</span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu {{ $section == 'custom-fields' ? 'active' : '' }}">
                            <a href="{{ route('settings.custom-fields') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-edit"></i></span>
                                <span class="dash-mtext">{{ __('Custom Field Settings') }}</span>
                            </a>
                        </li>
                        <li class="dash-item dash-hasmenu {{ $section == 'tags' ? 'active' : '' }}">
                            <a href="{{ route('settings.tags') }}" class="dash-link">
                                <span class="dash-micon"><i class="fas fa-tags"></i></span>
                                <span class="dash-mtext">{{ __('Tag Settings') }}</span>
                            </a>
                        </li>
                       
                    @else
                        <!-- Main System Settings Link -->
                 
                    @endif
                @endif
                <!--------------------- End Settings ----------------------------------->

                <!--------------------- End System Setup ----------------------------------->
            </ul>
        @endif
        @if (\Auth::user()->type == 'client')
            <ul class="dash-navbar">
                @if (Gate::check('manage client dashboard'))
                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'dashboard' ? ' active' : '' }}">
                        <a href="{{ route('client.dashboard.view') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-home fa-2"></i></span><span
                                class="dash-mtext">{{ __('Dashboard') }}</span>
                        </a>
                    </li>
                @endif
                @if (Gate::check('manage deal'))
                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'deals' ? ' active' : '' }}">
                        <a href="{{ route('deals.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                class="dash-mtext">{{ __('Deals') }}</span>
                        </a>
                    </li>
                @endif
                @if (Gate::check('manage contract'))
                    <li
                        class="dash-item dash-hasmenu {{ Request::route()->getName() == 'contract.index' || Request::route()->getName() == 'contract.show' ? 'active' : '' }}">
                        <a href="{{ route('contract.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                class="dash-mtext">{{ __('Contract') }}</span>
                        </a>
                    </li>
                @endif
                @if (Gate::check('manage project'))
                    <li class="dash-item dash-hasmenu  {{ Request::segment(1) == 'projects' ? ' active' : '' }}">
                        <a href="{{ route('projects.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-share"></i></span><span
                                class="dash-mtext">{{ __('Project') }}</span>
                        </a>
                    </li>
                @endif
                @if (Gate::check('manage project'))
                    <li
                        class="dash-item  {{ Request::route()->getName() == 'project_report.index' || Request::route()->getName() == 'project_report.show' ? 'active' : '' }}">
                        <a class="dash-link" href="{{ route('project_report.index') }}">
                            <span class="dash-micon"><i class="ti ti-chart-line"></i></span><span
                                class="dash-mtext">{{ __('Project Report') }}</span>
                        </a>
                    </li>
                @endif

                @if (Gate::check('manage project task'))
                    <li class="dash-item dash-hasmenu  {{ Request::segment(1) == 'taskboard' ? ' active' : '' }}">
                        <a href="{{ route('taskBoard.view', 'list') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-list-check"></i></span><span
                                class="dash-mtext">{{ __('Tasks') }}</span>
                        </a>
                    </li>
                @endif



                {{-- Bug tracking hidden as per user preference --}}
                {{-- @if (Gate::check('manage bug report'))
                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'bugs-report' ? ' active' : '' }}">
                        <a href="{{ route('bugs.view', 'list') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-bug"></i></span><span
                                class="dash-mtext">{{ __('Bugs') }}</span>
                        </a>
                    </li>
                @endif --}}

                @if (Gate::check('manage timesheet'))
                    <li
                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'timesheet-list' ? ' active' : '' }}">
                        <a href="{{ route('timesheet.list') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-clock"></i></span><span
                                class="dash-mtext">{{ __('Timesheet') }}</span>
                        </a>
                    </li>
                @endif



                <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'support' ? 'active' : '' }}">
                    <a href="{{ route('support.index') }}" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-headset fa-2"></i></span><span
                            class="dash-mtext">{{ __('Support System') }}</span>
                    </a>
                </li>

               

                <!-- WhatsApp Campaign - Show only if user has OMX Flow permissions -->
                @if (!empty($userPlan) && (($user->type === 'company' && $userPlan->hasModule('omx_flow')) || ($user->type === 'employee' && $userPlan->hasModule('omx_flow')) || ($user->type !== 'company' && $user->type !== 'employee' && $userPlan->omx_flow == 1)))
                    @if ($user->hasModulePermission('omx_flow', 'campaigns') || Gate::check('access omx flow'))
                        <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : '' }}">
                            <a href="{{ route('company.modules.whatsapp-campaign') }}" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-send"></i></span><span
                                    class="dash-mtext">{{ __('Campaign') }}</span>
                            </a>
                        </li>
                    @endif
                @endif

             
                
        @endif
                @if (\Auth::user()->type == 'system admin' || \Auth::user()->type == 'staff')
            <ul class="dash-navbar">
                <!-- Dashboard - Show only if user has permission or is system admin -->
                @if (\Auth::user()->type == 'system admin' || \Auth::user()->can('view system admin dashboard'))
                <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'system-admin' && Request::segment(2) == 'dashboard' ? ' active' : '' }}">
                    <a href="{{ route('system-admin.dashboard') }}" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-home"></i></span><span
                            class="dash-mtext">{{ __('Dashboard') }}</span>
                    </a>
                </li>
                @endif

                <!-- White Label - Show if user has permission or is system admin -->
                @if (\Auth::user()->type == 'system admin' || \Auth::user()->can('view white label'))
                    <li
                        class="dash-item dash-hasmenu {{ Request::route()->getName() == 'users.index' || Request::route()->getName() == 'users.create' || Request::route()->getName() == 'users.edit' || Request::route()->getName() == 'user.userlog' ? ' active' : '' }}">
                        <a href="{{ route('users.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-tag"></i></span><span
                                class="dash-mtext">{{ __('White Label') }}</span>
                        </a>
                    </li>
                @endif

                <!-- Sub-accounts - Show if user has permission or is system admin -->
                @if (\Auth::user()->type == 'system admin' || \Auth::user()->can('view sub accounts'))
                    <li
                        class="dash-item dash-hasmenu {{ Request::segment(1) == 'system-admin' && Request::segment(2) == 'companies' ? ' active' : '' }}">
                        <a href="{{ route('system-admin.companies') }}" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-building"></i></span><span
                                class="dash-mtext">{{ __('Sub-accounts') }}</span>
                        </a>
                    </li>
                @endif

                <!-- Staff Management - Only for System Admins -->
                @if (\Auth::user()->type == 'system admin')
                <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'system-admin' && Request::segment(2) == 'staff' ? ' active' : '' }}">
                    <a href="{{ route('system-admin.staff.index') }}" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-user-plus"></i></span><span
                            class="dash-mtext">{{ __('Staff Management') }}</span>
                    </a>
                </li>
                @endif

                <!-- Role Management - Hidden from system admin, only show for other user types with permission -->
                @if (\Auth::user()->type != 'system admin' && \Auth::user()->can('view role management'))
                    <li
                        class="dash-item dash-hasmenu {{ Request::route()->getName() == 'roles.index' || Request::route()->getName() == 'roles.create' || Request::route()->getName() == 'roles.edit' ? ' active' : '' }}">
                        <a href="{{ route('roles.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-shield"></i></span><span
                                class="dash-mtext">{{ __('Role Management') }}</span>
                        </a>
                    </li>
                @endif

                {{-- @if (Gate::check('manage pricing plan')) --}}
                <!-- Plan Management - Show if user has permission or is system admin -->
                {{-- @if (\Auth::user()->type == 'system admin' || \Auth::user()->can('view plan management'))
                    <li class="dash-item dash-hasmenu  {{ Request::segment(1) == 'plans' ? 'active' : '' }}">
                        <a href="{{ route('plans.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-trophy"></i></span><span
                                class="dash-mtext">{{ __('Plan') }}</span>
                        </a>
                    </li>
                @endif --}}

                <!-- Pricing Plans - Show if user has permission or is system admin -->
                @if (\Auth::user()->type == 'system admin' || \Auth::user()->can('view pricing plans'))
                    <li class="dash-item dash-hasmenu  {{ Request::segment(1) == 'pricing-plans' ? 'active' : '' }}">
                        <a href="{{ route('pricing-plans.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-dollar-sign"></i></span><span
                                class="dash-mtext">{{ __('Pricing Plans') }}</span>
                        </a>
                    </li>
                @endif

                <!-- Support System - Show to system admin or staff with permission -->
                @if (\Auth::user()->type == 'system admin' || (\Auth::user()->type == 'staff' && \Auth::user()->can('view support system')))
                <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'support' ? 'active' : '' }}">
                    <a href="{{ route('support.index') }}" class="dash-link">
                        <span class="dash-micon"><i class="fas fa-life-ring"></i></span><span
                            class="dash-mtext">{{ __('Support System') }}</span>
                    </a>
                </li>
                @endif

                <!-- Settings - Show only to system admin -->
                @if (\Auth::user()->type == 'system admin')
                    <li
                        class="dash-item dash-hasmenu {{ Request::route()->getName() == 'systems.index' ? ' active' : '' }}">
                        <a href="{{ route('systems.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="fas fa-cogs"></i></span><span
                                class="dash-mtext">{{ __('Settings') }}</span>
                        </a>
                    </li>
                @endif

                <!-- WhatsApp Template - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-template' || Request::segment(3) == 'whatsapp-campaign') ? 'active dash-trigger' : '' }}">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                            class="dash-mtext">{{ __('WhatsApp Template') }}</span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu {{ Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-template' || Request::segment(3) == 'whatsapp-campaign') ? 'show' : '' }}">
                        <li class="dash-item {{ Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : '' }}">
                            <a class="dash-link" href="{{ route('company.modules.whatsapp-template') }}">
                                {{ __('Templates') }}
                            </a>
                        </li>
                        <li class="dash-item {{ Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : '' }}">
                            <a class="dash-link" href="{{ route('company.modules.whatsapp-campaign') }}">
                                {{ __('Campaign') }}
                            </a>
                        </li>
                    </ul>
                </li> -->

                <!-- WhatsApp Flow - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-flow' ? 'active' : '' }}">
                    <a href="{{ route('company.modules.whatsapp-flow') }}" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-flow"></i></span><span
                            class="dash-mtext">{{ __('WhatsApp Flow') }}</span>
                    </a>
                </li> -->

                <!-- Bot Management - Available for all users (no restrictions) -->
                <!-- <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'active dash-trigger' : '' }}">
                    <a href="#!" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                            class="dash-mtext">{{ __('Bot Management') }}</span>
                        <span class="dash-arrow"><i data-feather="chevron-right"></i></span>
                    </a>
                    <ul class="dash-submenu {{ Request::segment(2) == 'modules' && (Request::segment(3) == 'bot-replies' || Request::segment(3) == 'bot-flows') ? 'show' : '' }}">
                        <li class="dash-item {{ Request::segment(2) == 'modules' && Request::segment(3) == 'bot-replies' ? 'active' : '' }}">
                            <a class="dash-link" href="{{ route('company.modules.bot-replies') }}">
                                {{ __('Bot Replies') }}
                            </a>
                        </li>
                        <li class="dash-item {{ Request::segment(2) == 'modules' && Request::segment(3) == 'bot-flows' ? 'active' : '' }}">
                            <a class="dash-link" href="{{ route('company.modules.bot-flows') }}">
                                {{ __('Bot Flows') }}
                            </a>
                        </li>
                    </ul>
                </li> -->
            </ul>
        @endif

        @if (\Auth::user()->type == 'super admin')
            <ul class="dash-navbar">
                @if (Gate::check('manage super admin dashboard'))
                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'dashboard' ? ' active' : '' }}">
                        <a href="{{ route('client.dashboard.view') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-home"></i></span><span
                                class="dash-mtext">{{ __('Dashboard') }}</span>
                        </a>
                    </li>
                @endif

                @can('manage user')
                    <li
                        class="dash-item dash-hasmenu {{ Request::route()->getName() == 'users.index' || Request::route()->getName() == 'users.create' || Request::route()->getName() == 'users.edit' ? ' active' : '' }}">
                        <a href="{{ route('users.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-users"></i></span><span
                                class="dash-mtext">{{ __('Companies') }}</span>
                        </a>
                    </li>
                @endcan

                @if (Gate::check('manage pricing plan'))
                    <li class="dash-item dash-hasmenu  {{ Request::segment(1) == 'pricing-plans' ? 'active' : '' }}">
                        <a href="{{ route('pricing-plans.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-currency-dollar"></i></span><span
                                class="dash-mtext">{{ __('Pricing Plans') }}</span>
                        </a>
                    </li>
                @endif

                <li class="dash-item dash-hasmenu {{ request()->is('plan_request*') ? 'active' : '' }}">
                    <a href="{{ route('plan_request.index') }}" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-arrow-up-right-circle"></i></span><span
                            class="dash-mtext">{{ __('Plan Request') }}</span>
                    </a>
                </li>

                <li class="dash-item dash-hasmenu  {{ Request::segment(1) == '' ? 'active' : '' }}">
                    <a href="{{ route('referral-program.index') }}" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-discount-2"></i></span><span
                            class="dash-mtext">{{ __('Referral Program') }}</span>
                    </a>
                </li>

                @if (Gate::check('manage coupon'))
                    <li class="dash-item dash-hasmenu {{ Request::segment(1) == 'coupons' ? 'active' : '' }}">
                        <a href="{{ route('coupons.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-gift"></i></span><span
                                class="dash-mtext">{{ __('Coupon') }}</span>
                        </a>
                    </li>
                @endif

                @if (Gate::check('manage order'))
                    <li class="dash-item dash-hasmenu  {{ Request::segment(1) == 'orders' ? 'active' : '' }}">
                        <a href="{{ route('order.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-shopping-cart-plus"></i></span><span
                                class="dash-mtext">{{ __('Order') }}</span>
                        </a>
                    </li>
                @endif

                <li
                    class="dash-item dash-hasmenu {{ Request::segment(1) == 'email_template' || Request::route()->getName() == 'manage.email.language' ? ' active dash-trigger' : 'collapsed' }}">
                    <a href="{{ route('email_template.index') }}" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-template"></i></span>
                        <span class="dash-mtext">{{ __('Email Template') }}</span>
                    </a>
                </li>

                @include('landingpage::menu.landingpage')

                <!-- OMX Flow for Super Admin - Show for users with omx flow module permission -->
                @if (!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin')))
                    @if (Gate::check('access omx flow'))
                        <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' ? 'active' : '' }}">
                            <a href="{{ route('company.modules.omx-flow') }}" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-rocket"></i></span><span
                                    class="dash-mtext">{{ __('OMX Flow') }}</span>
                            </a>
                        </li>
                    @endif
                @endif

                <!-- WhatsApp Template for Super Admin - Show only if user has OMX Flow permissions -->
                @if (!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin')))
                    @if (Gate::check('access omx flow'))
                        <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-template' ? 'active' : '' }}">
                            <a href="{{ route('company.modules.whatsapp-template') }}" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-message-circle"></i></span><span
                                    class="dash-mtext">{{ __('WhatsApp Template') }}</span>
                            </a>
                        </li>
                    @endif
                @endif

                <!-- WhatsApp Campaign for Super Admin - Show only if user has OMX Flow permissions -->
                @if (!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('omx_flow')) || ($user->type === 'system admin')))
                    @if (Gate::check('access omx flow'))
                        <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && Request::segment(3) == 'whatsapp-campaign' ? 'active' : '' }}">
                            <a href="{{ route('company.modules.whatsapp-campaign') }}" class="dash-link">
                                <span class="dash-micon"><i class="ti ti-send"></i></span><span
                                    class="dash-mtext">{{ __('WhatsApp Campaign') }}</span>
                            </a>
                        </li>
                    @endif
                @endif

               

                <!-- Unified Inbox for Super Admin - Available for all users (no restrictions) -->
                <li class="dash-item dash-hasmenu {{ Request::segment(2) == 'modules' && (Request::segment(3) == 'whatsapp-chat' || Request::segment(3) == 'facebook-chat' || Request::segment(3) == 'instagram-chat') ? 'active dash-trigger' : '' }}">
                    <a href="{{ route('company.modules.whatsapp-chat') }}" class="dash-link">
                        <span class="dash-micon"><i class="ti ti-inbox"></i></span><span
                            class="dash-mtext">{{ __('Unified Inbox') }}</span> 
                    </a>
                </li>

                <!-- Automatish Module - Show for super admin users with automatish module permission -->
                @if (!empty($userPlan) && (($user->type === 'super admin' && $userPlan->hasModule('automatish')) || ($user->type === 'system admin')))
                    @if (Gate::check('access automatish'))
                        @php
                            $automatishModule = \App\Models\ModuleIntegration::where('name', 'Automatish')->where('enabled', true)->first();
                        @endphp
                        @if($automatishModule && $automatishModule->sso_endpoint)
                            <li class="dash-item dash-hasmenu">
                                @if(Auth::user()->type === 'company')
                                    <a href="{{ route('company.modules.automatish') }}" class="dash-link">
                                @else
                                    <a href="{{ route('module-integration.sso-login', $automatishModule->id) }}" class="dash-link">
                                @endif
                                    <span class="dash-micon"><i class="ti ti-robot"></i></span><span
                                        class="dash-mtext">{{ __('Automatish') }}</span>
                                </a>
                            </li>
                        @endif
                    @endif
                @endif

                @if (Gate::check('manage system settings'))
                    <li
                        class="dash-item dash-hasmenu {{ Request::route()->getName() == 'systems.index' ? ' active' : '' }}">
                        <a href="{{ route('systems.index') }}" class="dash-link">
                            <span class="dash-micon"><i class="ti ti-settings"></i></span><span
                                class="dash-mtext">{{ __('Settings') }}</span>
                        </a>
                    </li>
                @endif

            </ul>
        @endif


    </div>
</div>
</nav>

