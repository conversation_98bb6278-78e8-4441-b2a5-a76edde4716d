{"__meta": {"id": "Xb65c572f5a921ffa90ce1f634142b3d7", "datetime": "2025-07-31 04:42:59", "utime": **********.933794, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753936978.88769, "end": **********.933824, "duration": 1.0461339950561523, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1753936978.88769, "relative_start": 0, "end": **********.84279, "relative_end": **********.84279, "duration": 0.9550998210906982, "duration_str": "955ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.842814, "relative_start": 0.****************, "end": **********.933826, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "91.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "qNGZdCmcZf4jMjBOpjjVZwVSi37AGnGK34cl9Lq2", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1881133786 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1881133786\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-302718433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-302718433\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2144078709 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2144078709\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1549826807 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549826807\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1039081429 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1039081429\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-482819255 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:42:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVMckxLNXE0WUtDT1JFRXZxeCtiamc9PSIsInZhbHVlIjoiYVFoUFFwWGFDYTA4bDIwallHT1JRcWdlV01VKzQxRHJoSlFGQ2tyUVU0QkNZdFJSY2pyb09oQTMzTWtiTkJzMk9Vc2w1SXc2TU1rejhxMVZGRS9YWHpDOUE5WFZwNzZvdEJ1ejJkVHlIZkNDV1JHMDFhQ0JrYU56bmNIaGp3U25CS09rS1JoVjdSZG13RFlwemtTRUs0UGRac1g0RCtNOHpzdVBGUzFwMHQ4eDliamhqYkFUMlQ3YkZKczJOc2VPSmh6V0FHRElkbUhLQkZRM0dHUTZ1YjFNOUJWcTRXbVdPaXhwTzNjNTNTWlMwK3l4MWtBSncwK1doa3ZXcjB2b3hUYlZjOVdzemFEZ0lBYXgyWGVjbjVPRm5jb1FRaExVZjNSNVljUjF5aTNuVVBSZnFRd0pxS2k0bE5naTZSTU9RMklIYVRiZDBkU2hrVkVjUDFYaURrcUxmazcxbnNBUFhkZTRFUkNMenVURXM0VlNQMEl4bUFlSlh2aTkwM05uYjA2MzlPc3dFazQxUGF2cURxZU9JbnMzNUZSVTNPQ21WYnhPdWJYaEo2RndZcTVxU2p6c25XSXc0THhpQUlzWG5QRUdpN2R1cEQ5R0lQd1h0ZkxFek9XYnkvKzNVZThONmkvOVJKMk8zZW1ldExuV01BamNVZEFrVmxHTDNaTFkiLCJtYWMiOiIzOGYwOGE2MDM3OGVmOWVlNmY3NTczMzI2MTE5MDE2ZDYyMDBlZWM0ZGZlNjM5NjVlNzk5ZDg1OGVhZmZlNWVjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:42:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkhkWEZ5aC9MRWpmQXk0THVOd3RrRkE9PSIsInZhbHVlIjoiRGw5OWh4emlUQmhHTy9xU2REQWkzK0gvNHdvMG9vUUhsVU8xMjA1UzE2a3ZzZEg2M2c1cy8wWmxhY3pFWjBWQVRpdWtHZlZpVnR4MG5zVkZTaitvZ2VLSzI0dWJmTno1elF1Wisyd016ZHdaSEtvcHI3R1Q0N3BqS001UStiVjBoNXU4WFl4Y2R4TXpHZHdoeW13WUh1c04zWE9jT0lMc0NIQThYVkFvWGpKUnJzeVNnYlJZUGUxck9uYnNsb3hIV3l6RzVSZWZQMTlBODg2L2t4aGovaVhJbkR6ZWJ0YXRMa0lKZFdBU1FnVDhuTTdQM3FUOWw5eURLZXJSWW1rSmpieGhMdnMyZ3lzR3MzVHljRTR0eXZ2cGlmZzVkN0hKNXpqem8xZGx5bndKRkJmUWVkN2htRlRRaDFyRWhDaE5RM0xZNG5lMDRkK09LVWdVS0ZPNkxOY2lKYVA1YVJlZWV0UTdGNHFkdVFwaFhEaVhaK1MyWDhGZ2s5NGg3ek0vYVlNM3hWYjYxZnA2SEdaNk1xenJaUEhCa1pwUndrOGxQekZPOVhGblJGV2NMTThpYlFqMENZcCtKTVk3N1orWlZGTzk0bmVUUkVyZXVBYXU5bC9zRE5zYVRhbmtSQ3A5eHNDcjZjS0xEdWxIMk5wZ0ptbzZlVG51WEViUnVqSkUiLCJtYWMiOiJjNzZjZjlmODBmMjIxYjZmOGNiNzVhNzU0MTZlM2VkMjE0YTk0NGJlODc5YzEwMDRlNjA2YTgyMTYxMGQzYjJjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:42:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVMckxLNXE0WUtDT1JFRXZxeCtiamc9PSIsInZhbHVlIjoiYVFoUFFwWGFDYTA4bDIwallHT1JRcWdlV01VKzQxRHJoSlFGQ2tyUVU0QkNZdFJSY2pyb09oQTMzTWtiTkJzMk9Vc2w1SXc2TU1rejhxMVZGRS9YWHpDOUE5WFZwNzZvdEJ1ejJkVHlIZkNDV1JHMDFhQ0JrYU56bmNIaGp3U25CS09rS1JoVjdSZG13RFlwemtTRUs0UGRac1g0RCtNOHpzdVBGUzFwMHQ4eDliamhqYkFUMlQ3YkZKczJOc2VPSmh6V0FHRElkbUhLQkZRM0dHUTZ1YjFNOUJWcTRXbVdPaXhwTzNjNTNTWlMwK3l4MWtBSncwK1doa3ZXcjB2b3hUYlZjOVdzemFEZ0lBYXgyWGVjbjVPRm5jb1FRaExVZjNSNVljUjF5aTNuVVBSZnFRd0pxS2k0bE5naTZSTU9RMklIYVRiZDBkU2hrVkVjUDFYaURrcUxmazcxbnNBUFhkZTRFUkNMenVURXM0VlNQMEl4bUFlSlh2aTkwM05uYjA2MzlPc3dFazQxUGF2cURxZU9JbnMzNUZSVTNPQ21WYnhPdWJYaEo2RndZcTVxU2p6c25XSXc0THhpQUlzWG5QRUdpN2R1cEQ5R0lQd1h0ZkxFek9XYnkvKzNVZThONmkvOVJKMk8zZW1ldExuV01BamNVZEFrVmxHTDNaTFkiLCJtYWMiOiIzOGYwOGE2MDM3OGVmOWVlNmY3NTczMzI2MTE5MDE2ZDYyMDBlZWM0ZGZlNjM5NjVlNzk5ZDg1OGVhZmZlNWVjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:42:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkhkWEZ5aC9MRWpmQXk0THVOd3RrRkE9PSIsInZhbHVlIjoiRGw5OWh4emlUQmhHTy9xU2REQWkzK0gvNHdvMG9vUUhsVU8xMjA1UzE2a3ZzZEg2M2c1cy8wWmxhY3pFWjBWQVRpdWtHZlZpVnR4MG5zVkZTaitvZ2VLSzI0dWJmTno1elF1Wisyd016ZHdaSEtvcHI3R1Q0N3BqS001UStiVjBoNXU4WFl4Y2R4TXpHZHdoeW13WUh1c04zWE9jT0lMc0NIQThYVkFvWGpKUnJzeVNnYlJZUGUxck9uYnNsb3hIV3l6RzVSZWZQMTlBODg2L2t4aGovaVhJbkR6ZWJ0YXRMa0lKZFdBU1FnVDhuTTdQM3FUOWw5eURLZXJSWW1rSmpieGhMdnMyZ3lzR3MzVHljRTR0eXZ2cGlmZzVkN0hKNXpqem8xZGx5bndKRkJmUWVkN2htRlRRaDFyRWhDaE5RM0xZNG5lMDRkK09LVWdVS0ZPNkxOY2lKYVA1YVJlZWV0UTdGNHFkdVFwaFhEaVhaK1MyWDhGZ2s5NGg3ek0vYVlNM3hWYjYxZnA2SEdaNk1xenJaUEhCa1pwUndrOGxQekZPOVhGblJGV2NMTThpYlFqMENZcCtKTVk3N1orWlZGTzk0bmVUUkVyZXVBYXU5bC9zRE5zYVRhbmtSQ3A5eHNDcjZjS0xEdWxIMk5wZ0ptbzZlVG51WEViUnVqSkUiLCJtYWMiOiJjNzZjZjlmODBmMjIxYjZmOGNiNzVhNzU0MTZlM2VkMjE0YTk0NGJlODc5YzEwMDRlNjA2YTgyMTYxMGQzYjJjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:42:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-482819255\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1676344887 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">qNGZdCmcZf4jMjBOpjjVZwVSi37AGnGK34cl9Lq2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676344887\", {\"maxDepth\":0})</script>\n"}}