@extends('layouts.admin')

@section('page-title')
    {{ __('Contact Groups') }}
@endsection

@push('css')
<style>
    /* Primary color overrides */
    .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
        transition: all 0.2s ease;
    }
    .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0b5ed7;
        transform: translateY(-1px);
    }
    .text-primary {
        color: #0d6efd !important;
    }
    .bg-primary {
        background-color: #0d6efd !important;
    }
    .border-primary {
        border-color: #0d6efd !important;
    }
    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
    .form-check-input:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table-success {
        background-color: #d4edda !important;
        transition: background-color 0.3s ease;
    }

    .btn-action-modern {
        background: none;
        border: none;
        padding: 8px;
        border-radius: 6px;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .btn-action-modern:hover {
        background-color: rgba(0, 0, 0, 0.05);
        transform: translateY(-1px);
    }

    .action-buttons-group {
        gap: 2px;
    }

    .form-group label {
        font-weight: 500;
    }

    .modal-header {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 1.5rem;
    }

    .modal-body {
        padding: 1.5rem;
        max-height: 70vh;
        overflow-y: auto;
    }

    .modal-footer {
        border-top: 1px solid #e9ecef;
        padding: 1rem 1.5rem;
    }

    .modal-lg {
        max-width: 800px;
    }

    .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    .alert {
        border-radius: 0.375rem;
    }

    .text-muted {
        color: #6c757d !important;
    }

    hr {
        margin: 1rem 0;
        color: inherit;
        background-color: currentColor;
        border: 0;
        opacity: 0.25;
    }

    hr:not([size]) {
        height: 1px;
    }

    /* Action Buttons Styling */
    .action-buttons-group {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin-right: 10px;
    }

    .btn-action-modern {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        background: #ffffff;
        color: #64748b;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .btn-action-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s ease;
    }

    .btn-action-modern:hover::before {
        left: 100%;
    }

    .btn-action-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: var(--bs-primary);
        background: rgba(var(--bs-primary-rgb), 0.1);
    }

    /* Avatar styling */
    .avatar-sm {
        width: 40px;
        height: 40px;
        flex-shrink: 0;
    }

    .avatar-title {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        border-radius: 50%;
        background-color: #0d6efd !important;
        color: white !important;
    }

    .avatar-title i {
        color: white !important;
        font-size: 16px;
    }

    /* Ensure proper avatar display */
    .d-flex.align-items-center .avatar-sm {
        margin-right: 12px;
    }

    /* Override any conflicting styles */
    .avatar-title.bg-primary {
        background-color: #0d6efd !important;
    }

    .avatar-title.rounded-circle {
        border-radius: 50% !important;
    }

    /* Table improvements */
    .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }

    .table td {
        vertical-align: middle;
        padding: 12px 8px;
    }

    /* Badge improvements */
    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    /* Dropdown improvements */
    .dropdown-menu {
        border: 1px solid #e2e8f0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .dropdown-item {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    /* Modal improvements */
    .modal-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e2e8f0;
    }

    .modal-title {
        font-weight: 600;
        color: #1e293b;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .action-buttons-group {
            flex-wrap: wrap;
            gap: 4px;
        }

        .btn-action-modern {
            width: 35px;
            height: 35px;
            font-size: 14px;
        }
    }
</style>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Contact Groups') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-8">
                         
                        </div>
                        <div class="col-lg-4">
                            <div class="d-flex align-items-center justify-content-end">
                                <!-- Action Buttons Group -->
                                <div class="action-buttons-group d-inline-flex align-items-center me-2">
                                    <!-- Create New Group -->
                                    <button class="btn btn-primary" onclick="createNewContactGroup()">
                                        <i class="fas fa-plus me-2"></i>{{ __('Create New Group') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="contact-groups-table">
                            <thead>
                                <tr>
                                    <th class="text-center">{{ __('Group Information') }}</th>
                                    <th class="text-center">{{ __('Members') }}</th>
                                    <th class="text-center">{{ __('Created') }}</th>
                                    <th class="text-center">{{ __('Updated') }}</th>
                                    <th class="text-center">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($contactGroups as $group)
                                    <tr>
                                        <td class="text-left">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title">
                                                        <i class="fas fa-users"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ $group->name }}</strong><br>
                                                    @if($group->description)
                                                        <small class="text-muted">
                                                            <i class="fa fa-info-circle"></i> {{ Str::limit($group->description, 50) }}
                                                        </small>
                                                    @else
                                                        <small class="text-muted">
                                                            <i class="fa fa-info-circle"></i> {{ __('No description') }}
                                                        </small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-primary">{{ $group->leads_count }} {{ __('Contacts') }}</span>
                                        </td>
                                        <td class="text-center">
                                            {{ $group->created_at->format('d/m/Y') }}<br>
                                            <small class="text-muted">{{ $group->created_at->format('H:i:s A') }}</small>
                                        </td>
                                        <td class="text-center">
                                            {{ $group->updated_at->format('d/m/Y') }}<br>
                                            <small class="text-muted">{{ $group->updated_at->format('H:i:s A') }}</small>
                                        </td>
                                        <td class="text-center">
                                            <div class="action-btn">
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="viewGroupMembers('{{ $group->id }}', '{{ $group->name }}')">
                                                            <i class="fas fa-eye me-2"></i>{{ __('View Members') }}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="attachContactToGroup('{{ $group->id }}', '{{ $group->name }}')">
                                                            <i class="fas fa-user-plus me-2"></i>{{ __('Add Contacts') }}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="editContactGroup('{{ $group->id }}', '{{ $group->name }}', '{{ $group->description }}')">
                                                            <i class="fas fa-edit me-2"></i>{{ __('Edit Group') }}
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a class="dropdown-item text-danger" href="#" onclick="deleteContactGroup('{{ $group->id }}', '{{ $group->name }}')">
                                                            <i class="fas fa-trash me-2"></i>{{ __('Delete Group') }}
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <div class="text-center">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">{{ __('No contact groups found') }}</h5>
                                                <p class="text-muted mb-4">{{ __('Create your first contact group to organize your contacts efficiently') }}</p>
                                                <button type="button" class="btn btn-primary" onclick="createNewContactGroup()">
                                                    <i class="fas fa-plus me-2"></i>{{ __('Create Your First Group') }}
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- View Group Members Modal -->
    <div class="modal fade" id="viewGroupMembersModal" tabindex="-1" aria-labelledby="viewGroupMembersModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewGroupMembersModalLabel">
                        <i class="fas fa-users me-2 text-primary"></i>{{ __('Group Members') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="group-members-content">
                        <!-- Members will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create New Contact Group Modal -->
    <div class="modal fade" id="createContactGroupModal" tabindex="-1" aria-labelledby="createContactGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createContactGroupModalLabel">
                        <i class="fas fa-plus me-2 text-primary"></i>{{ __('Create New Contact Group') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="createContactGroupForm">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="group_name" class="form-label fw-semibold">{{ __('Group Name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="group_name" name="name" required placeholder="{{ __('Enter group name') }}">
                        </div>
                        <div class="mb-3">
                            <label for="group_description" class="form-label fw-semibold">{{ __('Description') }}</label>
                            <textarea class="form-control" id="group_description" name="description" rows="3" placeholder="{{ __('Optional description for this group') }}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{{ __('Create Group') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Contact Group Modal -->
    <div class="modal fade" id="editContactGroupModal" tabindex="-1" aria-labelledby="editContactGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editContactGroupModalLabel">
                        <i class="fas fa-edit me-2 text-primary"></i>{{ __('Edit Contact Group') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editContactGroupForm">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="edit_group_id" name="group_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_group_name" class="form-label fw-semibold">{{ __('Group Name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_group_name" name="name" required placeholder="{{ __('Enter group name') }}">
                        </div>
                        <div class="mb-3">
                            <label for="edit_group_description" class="form-label fw-semibold">{{ __('Description') }}</label>
                            <textarea class="form-control" id="edit_group_description" name="description" rows="3" placeholder="{{ __('Optional description for this group') }}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>{{ __('Update Group') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Attach Contact to Group Modal -->
    <div class="modal fade" id="attachContactModal" tabindex="-1" aria-labelledby="attachContactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="attachContactModalLabel">
                        <i class="fas fa-user-plus me-2 text-primary"></i>{{ __('Add Contacts to Group') }}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="attachContactForm">
                    @csrf
                    <input type="hidden" id="attach_group_id" name="group_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label fw-semibold">{{ __('Select Contacts to Add') }}</label>
                            <div id="available-contacts-list" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                <div class="text-center py-3">
                                    <i class="fas fa-spinner fa-spin text-primary mb-2"></i>
                                    <p class="text-muted mb-0">{{ __('Loading available contacts...') }}</p>
                                </div>
                            </div>
                        </div>
                        <div id="selected-contacts-summary" class="mt-3" style="display: none;">
                            <h6 class="fw-semibold text-primary">{{ __('Selected Contacts:') }}</h6>
                            <div id="selected-contacts-list" class="d-flex flex-wrap gap-2"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                        <button type="submit" class="btn btn-primary" id="attachContactsBtn" disabled>
                            <i class="fas fa-plus me-2"></i>{{ __('Add Selected Contacts') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
    <script>
        let isSubmitting = false; // Flag to prevent duplicate submissions

        function viewGroupMembers(groupId, groupName) {
            $('#viewGroupMembersModalLabel').html('<i class="fas fa-users me-2 text-primary"></i>Members of ' + groupName);
            $('#group-members-content').html('<div class="text-center py-3"><i class="fas fa-spinner fa-spin text-primary mb-2"></i><p class="text-muted mb-0">Loading members...</p></div>');
            $('#viewGroupMembersModal').modal('show');
            
            // Load group members via AJAX
            $.ajax({
                url: '/contact-groups/' + groupId + '/members',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.members) {
                        let html = '<div class="table-responsive"><table class="table table-hover"><thead class="table-light"><tr><th>Name</th><th>Type</th><th>Email</th><th>Phone</th><th>Action</th></tr></thead><tbody>';

                        if (response.members.length > 0) {
                            response.members.forEach(function(member) {
                                html += '<tr>';
                                html += '<td><strong>' + member.name + '</strong></td>';
                                html += '<td><span class="badge bg-primary">' + member.type + '</span></td>';
                                html += '<td>' + (member.email || '-') + '</td>';
                                html += '<td>' + (member.phone || '-') + '</td>';
                                html += '<td><button class="btn btn-sm btn-outline-danger" onclick="removeMemberFromGroup(' + groupId + ', ' + member.id + ', \'' + member.name + '\')"><i class="fa fa-trash"></i></button></td>';
                                html += '</tr>';
                            });
                        } else {
                            html += '<tr><td colspan="5" class="text-center py-3"><i class="fas fa-users fa-2x text-muted mb-2"></i><p class="text-muted mb-0">No members found in this group</p></td></tr>';
                        }

                        html += '</tbody></table></div>';
                        $('#group-members-content').html(html);
                    } else {
                        $('#group-members-content').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Failed to load group members</div>');
                    }
                },
                error: function() {
                    $('#group-members-content').html('<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>Error loading group members</div>');
                }
            });
        }

        function createNewContactGroup() {
            $('#createContactGroupForm')[0].reset();
            $('#createContactGroupModal').modal('show');
        }

        function editContactGroup(groupId, groupName, groupDescription) {
            $('#edit_group_id').val(groupId);
            $('#edit_group_name').val(groupName);
            $('#edit_group_description').val(groupDescription || '');
            $('#editContactGroupModal').modal('show');
        }

        function attachContactToGroup(groupId, groupName) {
            $('#attach_group_id').val(groupId);
            $('#attachContactModalLabel').html('<i class="fas fa-user-plus me-2 text-primary"></i>Add Contacts to ' + groupName);
            $('#attachContactModal').modal('show');

            // Load available contacts
            loadAvailableContacts();
        }

        function loadAvailableContacts() {
            $('#available-contacts-list').html('<div class="text-center py-3"><i class="fas fa-spinner fa-spin text-primary mb-2"></i><p class="text-muted mb-0">Loading available contacts...</p></div>');

            $.ajax({
                url: '/contact-groups/available-contacts',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.contacts) {
                        let html = '';

                        if (response.contacts.length > 0) {
                            response.contacts.forEach(function(contact) {
                                html += '<div class="form-check mb-2 p-2 border rounded">';
                                html += '<input class="form-check-input contact-checkbox" type="checkbox" value="' + contact.id + '" id="contact_' + contact.id + '">';
                                html += '<label class="form-check-label w-100" for="contact_' + contact.id + '">';
                                html += '<div class="d-flex align-items-center">';
                                html += '<div class="avatar-sm me-2">';
                                html += '<div class="avatar-title bg-primary rounded-circle">';
                                html += '<i class="fas fa-user text-white"></i>';
                                html += '</div>';
                                html += '</div>';
                                html += '<div>';
                                html += '<strong>' + contact.name + '</strong>';
                                html += '<br><small class="text-muted">';
                                if (contact.email) html += contact.email + ' | ';
                                if (contact.phone) html += contact.phone;
                                html += '</small>';
                                html += '</div>';
                                html += '</div>';
                                html += '</label>';
                                html += '</div>';
                            });
                        } else {
                            html = '<div class="text-center py-3">';
                            html += '<i class="fas fa-users fa-2x text-muted mb-2"></i>';
                            html += '<h6 class="text-muted">No Available Contacts</h6>';
                            html += '<p class="text-muted mb-0">All contacts are already assigned to groups.</p>';
                            html += '</div>';
                        }

                        $('#available-contacts-list').html(html);

                        // Add event listeners for checkboxes
                        $('.contact-checkbox').on('change', updateSelectedContactsSummary);
                    } else {
                        $('#available-contacts-list').html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-2"></i>Failed to load available contacts</div>');
                    }
                },
                error: function() {
                    $('#available-contacts-list').html('<div class="alert alert-danger"><i class="fas fa-times-circle me-2"></i>Error loading available contacts</div>');
                }
            });
        }

        function updateSelectedContactsSummary() {
            const selectedContacts = $('.contact-checkbox:checked');
            const selectedCount = selectedContacts.length;

            if (selectedCount > 0) {
                $('#selected-contacts-summary').show();
                $('#attachContactsBtn').prop('disabled', false);

                let summaryHtml = '';
                selectedContacts.each(function() {
                    const contactId = $(this).val();
                    const contactLabel = $('label[for="contact_' + contactId + '"] strong').text();
                    summaryHtml += '<span class="badge bg-primary me-1 mb-1">' + contactLabel + '</span>';
                });

                $('#selected-contacts-list').html(summaryHtml);
            } else {
                $('#selected-contacts-summary').hide();
                $('#attachContactsBtn').prop('disabled', true);
            }
        }

        function removeMemberFromGroup(groupId, leadId, leadName) {
            if (confirm('Are you sure you want to remove "' + leadName + '" from this contact group?')) {
                $.ajax({
                    url: '/contact-groups/' + groupId + '/leads/' + leadId,
                    method: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            show_toastr('success', response.message);
                            // Refresh the members list
                            viewGroupMembers(groupId, $('#viewGroupMembersModalLabel').text().replace('Members of ', ''));
                        } else {
                            show_toastr('error', response.message);
                        }
                    },
                    error: function() {
                        show_toastr('error', 'Error removing member from group');
                    }
                });
            }
        }

        function deleteContactGroup(groupId, groupName) {
            if (confirm('Are you sure you want to delete the contact group "' + groupName + '"? This action cannot be undone.')) {
                $.ajax({
                    url: '/contact-groups/' + groupId,
                    method: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            show_toastr('success', response.message);
                            location.reload();
                        } else {
                            show_toastr('error', response.message);
                        }
                    },
                    error: function() {
                        show_toastr('error', 'Error deleting contact group');
                    }
                });
            }
        }

        // Form submission handlers with duplicate prevention
        $('#createContactGroupForm').on('submit', function(e) {
            e.preventDefault();
            
            if (isSubmitting) return false;
            isSubmitting = true;

            const formData = new FormData(this);
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Creating...');

            $.ajax({
                url: '/contact-groups',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        show_toastr('success', response.message);
                        $('#createContactGroupModal').modal('hide');
                        location.reload();
                    } else {
                        show_toastr('error', response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        show_toastr('error', response.message);
                    } else {
                        show_toastr('error', 'Error creating contact group');
                    }
                },
                complete: function() {
                    isSubmitting = false;
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
        });

        $('#editContactGroupForm').on('submit', function(e) {
            e.preventDefault();
            
            if (isSubmitting) return false;
            isSubmitting = true;

            const groupId = $('#edit_group_id').val();
            const formData = new FormData(this);
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Updating...');

            $.ajax({
                url: '/contact-groups/' + groupId,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        show_toastr('success', response.message);
                        $('#editContactGroupModal').modal('hide');
                        location.reload();
                    } else {
                        show_toastr('error', response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        show_toastr('error', response.message);
                    } else {
                        show_toastr('error', 'Error updating contact group');
                    }
                },
                complete: function() {
                    isSubmitting = false;
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
        });

        $('#attachContactForm').on('submit', function(e) {
            e.preventDefault();
            
            if (isSubmitting) return false;
            isSubmitting = true;

            const groupId = $('#attach_group_id').val();
            const selectedContactIds = $('.contact-checkbox:checked').map(function() {
                return parseInt($(this).val());
            }).get();

            if (selectedContactIds.length === 0) {
                show_toastr('error', 'Please select at least one contact to attach');
                isSubmitting = false;
                return;
            }

            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Adding...');

            $.ajax({
                url: '/contact-groups/' + groupId + '/attach-contacts',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    contact_ids: selectedContactIds
                },
                success: function(response) {
                    if (response.success) {
                        show_toastr('success', response.message);
                        $('#attachContactModal').modal('hide');
                        location.reload();
                    } else {
                        show_toastr('error', response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        show_toastr('error', response.message);
                    } else {
                        show_toastr('error', 'Error attaching contacts to group');
                    }
                },
                complete: function() {
                    isSubmitting = false;
                    submitBtn.prop('disabled', false).html(originalText);
                }
            });
        });

        // Reset modal states when closed
        $('#attachContactModal').on('hidden.bs.modal', function() {
            $('#selected-contacts-summary').hide();
            $('#attachContactsBtn').prop('disabled', true);
            $('.contact-checkbox').prop('checked', false);
        });

        // Additional functions for import/export (placeholder)
        function openImportModal() {
            show_toastr('info', 'Import functionality coming soon!');
        }

        function exportGroups() {
            show_toastr('info', 'Export functionality coming soon!');
        }
    </script>
@endpush
