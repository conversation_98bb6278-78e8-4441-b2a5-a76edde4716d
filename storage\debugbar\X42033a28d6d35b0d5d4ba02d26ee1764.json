{"__meta": {"id": "X42033a28d6d35b0d5d4ba02d26ee1764", "datetime": "2025-07-31 04:48:31", "utime": **********.509478, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937310.561053, "end": **********.509507, "duration": 0.9484539031982422, "duration_str": "948ms", "measures": [{"label": "Booting", "start": 1753937310.561053, "relative_start": 0, "end": **********.409594, "relative_end": **********.409594, "duration": 0.8485410213470459, "duration_str": "849ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409611, "relative_start": 0.****************, "end": **********.509509, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "99.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Kn6WuPr63x0zQglamxL7QllBcHOHWuWbULs3qgyh", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-469905710 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-469905710\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1046492654 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1046492654\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-429812045 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-429812045\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-880023907 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880023907\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2030929046 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2030929046\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2141455306 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:48:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjI0clVkbE5ZaU1uVkdjUWRZa2hGSVE9PSIsInZhbHVlIjoiak9ndGk0NVE3Z0MzUUxUemdLbTNtZDl1K2theWozTmFSYnE5TVJKNFZ2bVdNNWdUWEFPTWJhNmc0TTZzVFVJdW1VQnMzaDB4ck0wWXhwSGNYRHY0K1NrWTBUaUtKbG5IWUNXMjYrM3cvTHY2VzZEU0RYQ3doZ3pBNTFzL1A5MkZUVWJHTk16ZHZGODlVTHNKRU5mTWRNY2lQUDZiSzMzNUhKU0RGYWF2YVFCcENiLzBNUnhvWDFBeVdPcDAxMDhwRmJiMG5IRGszM2dwSnh5MjFKS0dpTG52eGpsNDRvY25SNFVUY290RG5UWEJ2YmtSRTVWdjAwWlhxUllwV1BFWmZsUWFIMEpCSG1PbjczZnlVQU9qcmIzbFYxSXljZWZTMUhQMEcrV2NrNTNicTg0R2NRRDllYjhZU1psVHEyQ3h2UkR6b0FxUk4wbUk5USt5VDJnZHhmdHlMbTFjL082WC85dXhNRXphazZRdWUzcjZYVlhLYld3c00xd3FzdWx6eG1TejNSYk8rZ2x2eW1HNEdtbzREbHJKMXUwK3BDRTNJZk9yZmZUWkhxd1JCZUEwT0dCOUY4cDM4cUNlS1B3NThYQlZJMDNpVzJsSTFGSzNJMkgycTJ2VmhhK3BNeW5CSSt6KzFFSjJzMllhWkFkUnd1VTlWUWJmblZvejZ5T0kiLCJtYWMiOiJhMDIzNGZiZDkxYTk5OTlhZTUwNDg0MmNlMjcyZjJkNTY0YTgwOWNjNDQxMWE2ZWMwYjQ2OWI0MTRlMDhlZGEyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ii9DZGtaTEFMYzQ5R0NtRnJxSUhTTmc9PSIsInZhbHVlIjoiaEh3SkhzRkJ1ZFkwbVhCT2JrcVZtMTNpTEx2d1hSZHdiN1hqOUlwd2ppQUtCeEtkS0RrQWJubXVFbS94eER3bXBoZHI5Q3JFQndxeDlKOFludllid2ZiRW1JTTU0eHJ6Z0p5MDlJTkVJTHBWa3V5M3pUZjBRZDk0dlNmdTJiOUNFd1VnbHoxbjhyejFZWnVhdGhmUzMrWm55dmhheTVLVk5wcjZJbWJ3aVE1OW1JSWRydHBLVXlJU1EyeUpURS90R0RkK1V3QldKdDlzZlVNbTBDQjU1dHlHMEZtMXR4S1phM3F0Z0JUTVJXekFsbmhQV3YxNEhrbldSMDFnaC9LNDBkbFNnSEdNWTVEK1RlRFR6Y014Y3VZMlNkNDBURjRLdGhNZHJ0YnZ2STVFQ1piWHFLV2I4WFhCTk10RVZKQWJrR3cvekxMWWxrRDRlTEQ2T1NHd1h5WEN0R0ZmMmZWWVJyalRGWmNzbXZWZTZ0eURXY2pnelUzMHZXL3h4djRpdEtwUW1BMFNwY3ZTTTZQUjhiMnlBNEM4VTBTOXcrSWI4b2hybkZ1REUzeHp0Ykg2Szk5KzRYMHNFTVA4LzhBa2RuL1JoM2E5Nnp1ZnRLZThOdEIyVGdoVEtBc0FsZkN3UmdzZXMvVlRkeHVyM3VqTHpSUWZDN2dVQWFJMjNXZmwiLCJtYWMiOiJjZWVjODI0NjcyZTM1ZTZiZDcxNDcwZWYwYmUyNjBmMGEyNjA5ZDQwMTAxNjQ4NDE3ODA0N2IyOWNmZmMyZjNiIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:48:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjI0clVkbE5ZaU1uVkdjUWRZa2hGSVE9PSIsInZhbHVlIjoiak9ndGk0NVE3Z0MzUUxUemdLbTNtZDl1K2theWozTmFSYnE5TVJKNFZ2bVdNNWdUWEFPTWJhNmc0TTZzVFVJdW1VQnMzaDB4ck0wWXhwSGNYRHY0K1NrWTBUaUtKbG5IWUNXMjYrM3cvTHY2VzZEU0RYQ3doZ3pBNTFzL1A5MkZUVWJHTk16ZHZGODlVTHNKRU5mTWRNY2lQUDZiSzMzNUhKU0RGYWF2YVFCcENiLzBNUnhvWDFBeVdPcDAxMDhwRmJiMG5IRGszM2dwSnh5MjFKS0dpTG52eGpsNDRvY25SNFVUY290RG5UWEJ2YmtSRTVWdjAwWlhxUllwV1BFWmZsUWFIMEpCSG1PbjczZnlVQU9qcmIzbFYxSXljZWZTMUhQMEcrV2NrNTNicTg0R2NRRDllYjhZU1psVHEyQ3h2UkR6b0FxUk4wbUk5USt5VDJnZHhmdHlMbTFjL082WC85dXhNRXphazZRdWUzcjZYVlhLYld3c00xd3FzdWx6eG1TejNSYk8rZ2x2eW1HNEdtbzREbHJKMXUwK3BDRTNJZk9yZmZUWkhxd1JCZUEwT0dCOUY4cDM4cUNlS1B3NThYQlZJMDNpVzJsSTFGSzNJMkgycTJ2VmhhK3BNeW5CSSt6KzFFSjJzMllhWkFkUnd1VTlWUWJmblZvejZ5T0kiLCJtYWMiOiJhMDIzNGZiZDkxYTk5OTlhZTUwNDg0MmNlMjcyZjJkNTY0YTgwOWNjNDQxMWE2ZWMwYjQ2OWI0MTRlMDhlZGEyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ii9DZGtaTEFMYzQ5R0NtRnJxSUhTTmc9PSIsInZhbHVlIjoiaEh3SkhzRkJ1ZFkwbVhCT2JrcVZtMTNpTEx2d1hSZHdiN1hqOUlwd2ppQUtCeEtkS0RrQWJubXVFbS94eER3bXBoZHI5Q3JFQndxeDlKOFludllid2ZiRW1JTTU0eHJ6Z0p5MDlJTkVJTHBWa3V5M3pUZjBRZDk0dlNmdTJiOUNFd1VnbHoxbjhyejFZWnVhdGhmUzMrWm55dmhheTVLVk5wcjZJbWJ3aVE1OW1JSWRydHBLVXlJU1EyeUpURS90R0RkK1V3QldKdDlzZlVNbTBDQjU1dHlHMEZtMXR4S1phM3F0Z0JUTVJXekFsbmhQV3YxNEhrbldSMDFnaC9LNDBkbFNnSEdNWTVEK1RlRFR6Y014Y3VZMlNkNDBURjRLdGhNZHJ0YnZ2STVFQ1piWHFLV2I4WFhCTk10RVZKQWJrR3cvekxMWWxrRDRlTEQ2T1NHd1h5WEN0R0ZmMmZWWVJyalRGWmNzbXZWZTZ0eURXY2pnelUzMHZXL3h4djRpdEtwUW1BMFNwY3ZTTTZQUjhiMnlBNEM4VTBTOXcrSWI4b2hybkZ1REUzeHp0Ykg2Szk5KzRYMHNFTVA4LzhBa2RuL1JoM2E5Nnp1ZnRLZThOdEIyVGdoVEtBc0FsZkN3UmdzZXMvVlRkeHVyM3VqTHpSUWZDN2dVQWFJMjNXZmwiLCJtYWMiOiJjZWVjODI0NjcyZTM1ZTZiZDcxNDcwZWYwYmUyNjBmMGEyNjA5ZDQwMTAxNjQ4NDE3ODA0N2IyOWNmZmMyZjNiIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:48:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141455306\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kn6WuPr63x0zQglamxL7QllBcHOHWuWbULs3qgyh</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}