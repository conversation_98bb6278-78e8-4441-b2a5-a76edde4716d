{"__meta": {"id": "X1fa79060c70633e77e91b98213d99c0b", "datetime": "2025-07-31 04:54:55", "utime": **********.062589, "method": "GET", "uri": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753937694.000448, "end": **********.06262, "duration": 1.0621719360351562, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753937694.000448, "relative_start": 0, "end": 1753937694.977769, "relative_end": 1753937694.977769, "duration": 0.9773209095001221, "duration_str": "977ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753937694.977786, "relative_start": 0.****************, "end": **********.062623, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "84.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#3032\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1861 to 1867\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1861\" onclick=\"\">routes/web.php:1861-1867</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6j1Fg2xKQ1vybLZlbnbN0BM4PqNbiVCeI0kuGICN", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png", "status_code": "<pre class=sf-dump id=sf-dump-306966593 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-306966593\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1142207682 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1142207682\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-810499802 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-810499802\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-695290600 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695290600\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1105726762 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1105726762\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-51804281 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 04:54:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitpWHMyQkdycUFpTzFOaEZESDkvM0E9PSIsInZhbHVlIjoiVWFpa1pHemIwaERtSjVvUEZDQ29qVXhLamI2NGhhVTBzMWorSVpTTmREMWxCL0hMci9mcXRaZ2MzTTRpd0o4TkFWWjU4UmZoMWZvMVdMN3pYTGJja2REZC9hN2FUaHhBallsU0I5MU1RT1lMVkw4Y1hOQSsrNFpjYlZKcDV1Z0xNS3hIbGM0U2pnV3ZoOGZGSEkrMi92N3BFbk9nZFFYY2hqTWVJRlRJT1dmVFJkTXltakFNeCtyVXZvdlZYeHJLaXliajVsYTdkVHYzdTMxaWQwNkJtd1ZWYmxzZm5YVkFwcHBvUHhBM0czWFlRaXJEOVcwUEMxOGRnRVhKWFdjTjBLTENSd01FbUxNY2o1WkR2eU5IcUFudGRidVZPT3A2VTRxeUFWVWtVTkZKZ2dNUllKYlpHOGp5ZGo0ZjNBQUtUbUl0MGcrbTVjUnFBd2JYY0JzMHZNN1IxZU9ib08rMkR3K25pK1hlUjZBNUhURE5ZUXNocTh6Zk14QjdJRk5GTW0yWWR5dkszSVIycE5seEdWNzY2ajZiYUZVZE56K3p0RU5FYTNkOGtVTUx6azk1T2UrSWNhMzlRejAvZ2RicGVVMlBiNFY2aExFVkwzQmR0emNvK1NkaWxPd3ppamVaWDgreWx1ZG1Ed1BQUkxJZ2tna2k1Q3FGdTF6bnlVTUkiLCJtYWMiOiIzZGViM2Y0YjQ4YzcwY2I2N2JkMTZlYzA2ODQzNDA4MTM0NGJhOGM3NjMwMGJkZTUwZjVmMjFjM2NmZjM1Y2Q5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNrbk9KKzI1elVoc0orSWdoVkNCNUE9PSIsInZhbHVlIjoiUHQyMkNzZjA1NkkrK2UyaldxL1p3c3gxclUzOXhxSndKcUNkaE1DK1lRazVNTkFYSGhzclVNOW5vUjdvdDRmdUJSSjhoR2x6OHViSWtzcGw2SWREQXpOM2FqaGNtREJBcGs1YlRXTWw3OVRvbC9BY29pTCtzTnBQMXJyWlgwU1RkY0FobmtPWG9ucHRPNnJJWVg3dWg4SWhOMDV2MkxEV0FwTVZNak1oRnZiZHlWaDBmaXZ0L1graGlJM3ZvRnFHczVHOFJFcldMRmtXNkJYbWNIVFhqRm5KNk5sc1c2V0NkYjcvc3pOOUxQck5yVGtncnBrakdHUWVFbHJSSGNZM1pKa2ZIZWZRY0lzWUc4ZzdJeHhLNjZwVksvU0sreWZhZFhMOE9MQVdhYnljZjFNVjJ0NytiWk1xb1U5cVh0UmFNWm1QSmVKMlFXc3REQ1VISTVXYy80N0tycFZhR1YwOGY1MHlFMUM3a3hyWTNFNWhBYWY0WjhWYURFL1g2Mi8rd0JDbElneWNWTUpYNE5BcnFyNnUxeDlLYnIvK2IyUG9FSWkvQTFtZFY3emxHNWFkR3FrN3R3TlFXN1B5Vk1kbk9sVFByN3RST3VCa3ZLMTVBd2FISFUvRXdoNHBLdlpnMXpQOGpUZnhpMSt4L3JLT2lDVVpqdUhES0xEeDdKcEoiLCJtYWMiOiJiN2E1MzQ4MzdjZDAwZDBlOTljYmVmNDdkYmQyYTAyNWViYTlkMzJjN2E3YTkxZjM4MTVhZjA3YTQ2MWM4Nzc5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 06:54:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitpWHMyQkdycUFpTzFOaEZESDkvM0E9PSIsInZhbHVlIjoiVWFpa1pHemIwaERtSjVvUEZDQ29qVXhLamI2NGhhVTBzMWorSVpTTmREMWxCL0hMci9mcXRaZ2MzTTRpd0o4TkFWWjU4UmZoMWZvMVdMN3pYTGJja2REZC9hN2FUaHhBallsU0I5MU1RT1lMVkw4Y1hOQSsrNFpjYlZKcDV1Z0xNS3hIbGM0U2pnV3ZoOGZGSEkrMi92N3BFbk9nZFFYY2hqTWVJRlRJT1dmVFJkTXltakFNeCtyVXZvdlZYeHJLaXliajVsYTdkVHYzdTMxaWQwNkJtd1ZWYmxzZm5YVkFwcHBvUHhBM0czWFlRaXJEOVcwUEMxOGRnRVhKWFdjTjBLTENSd01FbUxNY2o1WkR2eU5IcUFudGRidVZPT3A2VTRxeUFWVWtVTkZKZ2dNUllKYlpHOGp5ZGo0ZjNBQUtUbUl0MGcrbTVjUnFBd2JYY0JzMHZNN1IxZU9ib08rMkR3K25pK1hlUjZBNUhURE5ZUXNocTh6Zk14QjdJRk5GTW0yWWR5dkszSVIycE5seEdWNzY2ajZiYUZVZE56K3p0RU5FYTNkOGtVTUx6azk1T2UrSWNhMzlRejAvZ2RicGVVMlBiNFY2aExFVkwzQmR0emNvK1NkaWxPd3ppamVaWDgreWx1ZG1Ed1BQUkxJZ2tna2k1Q3FGdTF6bnlVTUkiLCJtYWMiOiIzZGViM2Y0YjQ4YzcwY2I2N2JkMTZlYzA2ODQzNDA4MTM0NGJhOGM3NjMwMGJkZTUwZjVmMjFjM2NmZjM1Y2Q5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNrbk9KKzI1elVoc0orSWdoVkNCNUE9PSIsInZhbHVlIjoiUHQyMkNzZjA1NkkrK2UyaldxL1p3c3gxclUzOXhxSndKcUNkaE1DK1lRazVNTkFYSGhzclVNOW5vUjdvdDRmdUJSSjhoR2x6OHViSWtzcGw2SWREQXpOM2FqaGNtREJBcGs1YlRXTWw3OVRvbC9BY29pTCtzTnBQMXJyWlgwU1RkY0FobmtPWG9ucHRPNnJJWVg3dWg4SWhOMDV2MkxEV0FwTVZNak1oRnZiZHlWaDBmaXZ0L1graGlJM3ZvRnFHczVHOFJFcldMRmtXNkJYbWNIVFhqRm5KNk5sc1c2V0NkYjcvc3pOOUxQck5yVGtncnBrakdHUWVFbHJSSGNZM1pKa2ZIZWZRY0lzWUc4ZzdJeHhLNjZwVksvU0sreWZhZFhMOE9MQVdhYnljZjFNVjJ0NytiWk1xb1U5cVh0UmFNWm1QSmVKMlFXc3REQ1VISTVXYy80N0tycFZhR1YwOGY1MHlFMUM3a3hyWTNFNWhBYWY0WjhWYURFL1g2Mi8rd0JDbElneWNWTUpYNE5BcnFyNnUxeDlLYnIvK2IyUG9FSWkvQTFtZFY3emxHNWFkR3FrN3R3TlFXN1B5Vk1kbk9sVFByN3RST3VCa3ZLMTVBd2FISFUvRXdoNHBLdlpnMXpQOGpUZnhpMSt4L3JLT2lDVVpqdUhES0xEeDdKcEoiLCJtYWMiOiJiN2E1MzQ4MzdjZDAwZDBlOTljYmVmNDdkYmQyYTAyNWViYTlkMzJjN2E3YTkxZjM4MTVhZjA3YTQ2MWM4Nzc5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 06:54:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-51804281\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1322000999 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6j1Fg2xKQ1vybLZlbnbN0BM4PqNbiVCeI0kuGICN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"86 characters\">http://localhost:8000/storage/uploads/avatar/logo-dark-removebg-preview_1752904903.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322000999\", {\"maxDepth\":0})</script>\n"}}